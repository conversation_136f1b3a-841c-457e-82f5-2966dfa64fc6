import { SeTagTheme } from "../tag";

export interface ItemSelectableCard {
  title: string;
  description: string;
  translateNoAttr?: boolean;
  separatorTitle?: boolean;
  columnSize?: number;
  clickable?: boolean;
  align?: 'left' | 'right';
}

export enum SelectiveCardMode {
  SELECTIVE = 'SELECTIVE',
  INTERACTIVE = 'INTERACTIVE'
}

export interface SelectiveCardTag {
  title: string;
  tagTheme: SeTagTheme;
  closable?: boolean
}

export interface HeaderItem {
  title: string
  icon?: string
}