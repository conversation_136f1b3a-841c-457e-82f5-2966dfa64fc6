import { EventEmitter, Type } from '@angular/core';
import { FlattenedCell } from '../cells/cells.model';

export interface FlattenedRow {
  rowComponent: Type<RowComponent>;
  rowConfig: RowConfig;
  cells: FlattenedCell[];
  id: string;
  selected?: boolean;
  keyGroup?: string;
  isGrouped?: boolean;
  isParent?: boolean;
  isChild?: boolean;
  isCollapsed?: boolean;
}

export interface FlattenedGroupRow extends FlattenedRow {
  key: string;
  name: string;
  isCollapsed: boolean;
  children?: FlattenedRow[]
}

export interface RowConfig {
  background?: string;
  colSpan?: number;
  [x: string]: any;
}

export interface RowComponent {
  row: FlattenedRow;
  rowClick: EventEmitter<FlattenedRow>;
  rowConfig?: RowConfig;
}
