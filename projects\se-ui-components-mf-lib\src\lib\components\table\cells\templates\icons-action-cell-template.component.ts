import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { SeButton } from '../../../button';
import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import {
  CellComponent,
  FlattenedCell,
  IconActionsCellConfig,
  IconAction,
} from '../cells.model';

@Component({
  selector: 'app-icon-actions-cell',
  template: `
    <div
      class="d-flex flex-row justify-content-start justify-content-md-end gap-2"
    >
      <ng-container *ngFor="let icon of icons">
        <button
          class="action-button"
          [title]="icon.title | translate"
          [attr.aria-label]="icon.ariaLabel ?? icon.label | translate"
          (click)="onIconClick(icon, $event)"
        >
          <ng-icon [name]="icon.name" />
        </button>

        <se-button
          [btnTheme]="'secondary'"
          [size]="'small'"
          class="d-inline-flex d-md-none"
          [ariaLabel]="icon.ariaLabel ?? icon.label | translate"
          (onClick)="onIconClick(icon, $event)"
        >
          {{ icon.label || '' | translate }}
        </se-button>
      </ng-container>

      <se-button-dropdown
        *ngIf="dropdownItems.length && !!dropdownButtonOptions"
        [items]="dropdownItems"
        [buttonOptions]="dropdownButtonOptions"
      />
    </div>
  `,
  styleUrls: ['../styles/icon-actions-cell-template.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IconActionsCellComponent implements CellComponent {
  @Input() value: unknown;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: IconActionsCellConfig;

  get icons(): IconAction[] {
    return this.cellConfig.iconActions?.icons ?? [];
  }

  get dropdownItems(): MenuItem[] {
    return (
      this.cellConfig.iconActions?.buttonActions?.map((element) => {
        if (element?.command) {
          element['data'] = this.cell.rowData;
        }
        return element;
      }) ?? []
    );
  }

  get dropdownButtonOptions(): SeButton | null {
    return this.cellConfig.iconActions?.button ?? null;
  }

  onIconClick(icon: IconAction, event: MouseEvent): void {
    event.stopImmediatePropagation();
    event.stopPropagation();
    if (icon.command) icon.command(this.cell.rowData);
  }
}
