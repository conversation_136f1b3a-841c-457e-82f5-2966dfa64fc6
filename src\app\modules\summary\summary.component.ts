import { Component, OnInit } from '@angular/core';
import { SummaryData } from './summary.model';
import { Router } from '@angular/router';
import { AppRoutes } from '@core/models/app-routes.enum';

@Component({
  selector: 'app-summary',
  templateUrl: './summary.component.html',
  styleUrls: [],
})
export class SummaryComponent implements OnInit {
  protected summaryDataList: SummaryData[] = [];
  constructor(private router: Router) {
    // Initialization if needed
  }

  ngOnInit(): void {
    this.summaryDataList = [
      { label: 'SE_DECINF_MF.MODULE_SUMMARY.PRESENTER', value: 'Value' },
      { label: 'SE_DECINF_MF.MODULE_SUMMARY.DECLARANT', value: 'Value' },
      { label: 'SE_DECINF_MF.MODULE_SUMMARY.MODEL', value: 'Value' },
      { label: 'SE_DECINF_MF.MODULE_SUMMARY.TAX_YEAR', value: 'Value' },
      { label: 'SE_DECINF_MF.MODULE_SUMMARY.FILE_NAME', value: 'Value' },
      { label: 'SE_DECINF_MF.MODULE_SUMMARY.TOTAL_REGISTRES', value: 'Value' },
    ];
  }

  protected goBack(): void {
    this.router.navigate([AppRoutes.YEAR_DECLARATION]);
  }

  protected onSubmit(): void {
    // TODO: Implement submission logic
  }
}
