(self.webpackChunkse_ui_components_mf_lib=self.webpackChunkse_ui_components_mf_lib||[]).push([[492],{"./projects/se-ui-components-mf-lib/src/lib/components/link/link.stories.ts":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Link:()=>Link,default:()=>link_stories});var _class,tslib_es6=__webpack_require__("./node_modules/tslib/tslib.es6.mjs"),link_component=__webpack_require__("./projects/se-ui-components-mf-lib/src/lib/components/link/link.component.ts.css?ngResource!=!./node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=CiAgICAgIGEgewogICAgICAgIGNvbG9yOiB2YXIoLS1kZWdyYWRhZG9zLXJvc2EtNTAwLCAjYTgxYjhkKTsKICAgICAgICBmb250LWZhbWlseTogT3BlbiBTYW5zOwogICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICBmb250LXN0eWxlOiBub3JtYWw7CiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsKICAgICAgICBsaW5lLWhlaWdodDogMTlweDsKICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7CiAgICAgIH0KCiAgICAgIGE6aG92ZXIgewogICAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lOwogICAgICB9CgogICAgICBhOmFjdGl2ZSB7CiAgICAgICAgY29sb3I6IHZhcigtLWRlZ3JhZGFkb3Mtcm9zYS03MDAsICM1NTBCNDcpOwogICAgICB9CgogICAgICBhLmRpc2FibGVkIHsKICAgICAgICBjb2xvcjogdmFyKC0tdGV4dG9zLW5vcm1hbCwgIzk5OSk7CiAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDsKICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7CiAgICAgIH0KICAgIA%3D%3D!./projects/se-ui-components-mf-lib/src/lib/components/link/link.component.ts"),link_component_default=__webpack_require__.n(link_component),core=__webpack_require__("./node_modules/@angular/core/fesm2022/core.mjs");let LinkComponent=((_class=class LinkComponent{constructor(){this.disabled=!1}}).propDecorators={disabled:[{type:core.Input}]},_class);LinkComponent=(0,tslib_es6.gn)([(0,core.Component)({selector:"se-link",template:'\n    <a href="#" [ngClass]="{disabled}">\n      <ng-content></ng-content>\n    </a>',styles:[link_component_default()]})],LinkComponent);const link_stories={title:"Components/Link",component:LinkComponent,tags:["autodocs"],render:args=>({props:args,template:"\n    <se-link>Web link</se-link>\n    "})},Link={}},"./node_modules/css-loader/dist/runtime/api.js":module=>{"use strict";module.exports=function(cssWithMappingToString){var list=[];return list.toString=function toString(){return this.map((function(item){var content="",needLayer=void 0!==item[5];return item[4]&&(content+="@supports (".concat(item[4],") {")),item[2]&&(content+="@media ".concat(item[2]," {")),needLayer&&(content+="@layer".concat(item[5].length>0?" ".concat(item[5]):""," {")),content+=cssWithMappingToString(item),needLayer&&(content+="}"),item[2]&&(content+="}"),item[4]&&(content+="}"),content})).join("")},list.i=function i(modules,media,dedupe,supports,layer){"string"==typeof modules&&(modules=[[null,modules,void 0]]);var alreadyImportedModules={};if(dedupe)for(var k=0;k<this.length;k++){var id=this[k][0];null!=id&&(alreadyImportedModules[id]=!0)}for(var _k=0;_k<modules.length;_k++){var item=[].concat(modules[_k]);dedupe&&alreadyImportedModules[item[0]]||(void 0!==layer&&(void 0===item[5]||(item[1]="@layer".concat(item[5].length>0?" ".concat(item[5]):""," {").concat(item[1],"}")),item[5]=layer),media&&(item[2]?(item[1]="@media ".concat(item[2]," {").concat(item[1],"}"),item[2]=media):item[2]=media),supports&&(item[4]?(item[1]="@supports (".concat(item[4],") {").concat(item[1],"}"),item[4]=supports):item[4]="".concat(supports)),list.push(item))}},list}},"./node_modules/css-loader/dist/runtime/noSourceMaps.js":module=>{"use strict";module.exports=function(i){return i[1]}},"./projects/se-ui-components-mf-lib/src/lib/components/link/link.component.ts.css?ngResource!=!./node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=CiAgICAgIGEgewogICAgICAgIGNvbG9yOiB2YXIoLS1kZWdyYWRhZG9zLXJvc2EtNTAwLCAjYTgxYjhkKTsKICAgICAgICBmb250LWZhbWlseTogT3BlbiBTYW5zOwogICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICBmb250LXN0eWxlOiBub3JtYWw7CiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsKICAgICAgICBsaW5lLWhlaWdodDogMTlweDsKICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7CiAgICAgIH0KCiAgICAgIGE6aG92ZXIgewogICAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lOwogICAgICB9CgogICAgICBhOmFjdGl2ZSB7CiAgICAgICAgY29sb3I6IHZhcigtLWRlZ3JhZGFkb3Mtcm9zYS03MDAsICM1NTBCNDcpOwogICAgICB9CgogICAgICBhLmRpc2FibGVkIHsKICAgICAgICBjb2xvcjogdmFyKC0tdGV4dG9zLW5vcm1hbCwgIzk5OSk7CiAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDsKICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7CiAgICAgIH0KICAgIA%3D%3D!./projects/se-ui-components-mf-lib/src/lib/components/link/link.component.ts":(module,__unused_webpack_exports,__webpack_require__)=>{var ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___=__webpack_require__("./node_modules/css-loader/dist/runtime/noSourceMaps.js"),___CSS_LOADER_EXPORT___=__webpack_require__("./node_modules/css-loader/dist/runtime/api.js")(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);___CSS_LOADER_EXPORT___.push([module.id,"\n      a {\n        color: var(--degradados-rosa-500, #a81b8d);\n        font-family: Open Sans;\n        font-size: 14px;\n        font-style: normal;\n        font-weight: 400;\n        line-height: 19px;\n        text-decoration: none;\n      }\n\n      a:hover {\n        text-decoration: underline;\n      }\n\n      a:active {\n        color: var(--degradados-rosa-700, #550B47);\n      }\n\n      a.disabled {\n        color: var(--textos-normal, #999);\n        cursor: not-allowed;\n        text-decoration: none;\n      }\n    ",""]),module.exports=___CSS_LOADER_EXPORT___.toString()}}]);