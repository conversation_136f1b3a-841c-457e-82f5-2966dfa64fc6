import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { provideIcons } from '@ng-icons/core';
import { TranslateModule } from '@ngx-translate/core';
import { NG_ICONS } from '../../../shared/shared.model';
import { SeSharedModule } from '../../../shared/shared.module';
import { SeButtonModule } from '../../button';
import { SeDropdownModule } from '../../dropdown/dropdown.module';
import { SeModalOutputEvents } from '../../modal/modal.model';
import { SeModalModule } from '../../modal/modal.module';
import { Column } from '../../table/columns/column.model';
import { Row } from '../../table/table.model';
import { SeTableModule } from '../../table/table.module';
import { AttachFile, SeAttachFileModal } from './attach-modal.model';
import { CellEvent } from '../../table/cells/cells.model';
import { UploadFilesService } from '../upload-files.service';
import { SeDropdownOption } from '../../dropdown/dropdown.model';

@Component({
  selector: 'se-attach-modal',
  template: `
    <se-modal
      [data]="data"
      (modalOutputEvent)="saveList($event)"
      (modalSecondaryButtonEvent)="closeModal()"
      [customActions]="customButton">
      <se-table
        [columns]="data.tableColumns"
        [data]="rows"
        (onCellEvent)="onCellEvent($event)">
      </se-table>
    </se-modal>

    <ng-template #customButton>
      <se-button
        class="button-align"
        [btnTheme]="'primary'"
        [disabled]="isButtonDisabled"
        (onClick)="saveList(SeModalOutputEvents.MAIN_ACTION)"
      >
        {{ 'UI_COMPONENTS.BUTTONS.ATTACH' | translate }}
      </se-button>
    </ng-template>
  `,
  styleUrls: ['./attach-modal.component.scss'],
  standalone: true,
  imports: [
    SeModalModule,
    SeTableModule,
    SeDropdownModule,
    CommonModule,
    ReactiveFormsModule,
    SeSharedModule,
    FormsModule,
    SeButtonModule,
    TranslateModule,
  ],
  providers: [provideIcons(NG_ICONS)],
})
export class AttachFileTableComponent {
  SeModalOutputEvents = SeModalOutputEvents;

  private _data!: SeAttachFileModal;
  private optionsDropdown: SeDropdownOption[] = [];

  private _inputData!: SeAttachFileModal;
  private _useFileNameAsDescription: boolean = false;

  @Input() set data(data: SeAttachFileModal) {
    this._inputData = data;
    this._updateState();
  }

  @Input() set useFileNameAsDescription(val: boolean) {
    this._useFileNameAsDescription = val;
    if (val)
    {
      this._updateState();
    }

  }

  get data() {
    return this._data;
  }

  get isButtonDisabled(): boolean {
    let result: boolean = true;
    const fieldsRequired: string[] = this.data.tableColumns
      .filter((column: Column) => {
        return (
          (column.cellComponentName === 'dropdownCellComponent' ||
          column.cellComponentName === 'inputCellComponent')
          && column.cellConfig?.['required']
        );
      })
      .map((column: Column) => column.key);

    result = this.rows.some((row: Row) => {
      const { data } = row;
      return fieldsRequired.some((value) => !data[value]?.value);
    });

    return result;
  }

  rows!: Row[];

  @Output() list: EventEmitter<AttachFile[]> = new EventEmitter<AttachFile[]>();

  constructor(
    private activatedModalService: NgbActiveModal,
    private uploadFileService: UploadFilesService
  ) { }

  closeModal() {
    this.list.emit([]);
    this.activatedModalService.close();
  }

  onCellEvent(event: CellEvent) {
    if (this.rows.length === 1 && event.type === 'deleteRow') {
      this.closeModal();
    }
  }

  saveList(event: string) {
    if (event === SeModalOutputEvents.MAIN_ACTION) {
      this.list.emit(
        this.data.files
          ?.filter((file: AttachFile) => {
            return !!this.rows.find((row: Row) => {
              return (
                file.name === row?.data!['name']?.value ||
                (file['name'] as any)?.value === row?.data!['name']?.value
              );
            });
          })
          ?.map((element: AttachFile) => {
            const { data } = this.rows.find((row: Row) => {
              return element.name === row?.data!['name']?.value;
            }) as Row;
            element['description'] = data['description']?.value ?? '';
            element['idType'] = data['docType']?.value ?? '';
            element['docType'] = this.optionsDropdown.find((option) => option['id'] === data['docType']?.value)?.label || data['docType']?.value;
            return element;
          })
      );
    }

    this.activatedModalService.close();
  }

  private getDocTypesList(tableColumns: Column[]): SeDropdownOption[] {
    const index: number = tableColumns.findIndex(
      (column) =>
        column.cellComponentName === 'dropdownCellComponent' &&
        column.key === 'docType',
    );

    if(index === -1 || !tableColumns?.[index].cellConfig?.['options']) return [];

    return tableColumns[index].cellConfig?.['options'].map((option: SeDropdownOption) => {
      return {
        id: option[tableColumns[index].cellConfig?.['optionValue'] || 'id'],
        label: option[tableColumns[index].cellConfig?.['optionLabel'] || 'label'],
      };
    });
  }

  private _addColumn(data: SeAttachFileModal): SeAttachFileModal {
    if (
      data?.hasActions &&
      !data.tableColumns.find((columns) => columns.key === 'download')
    ) {
      data.tableColumns.push({
        header: '',
        key: 'download',
        size: 5,
        resizable: false,
        cellComponentName: 'actionsCellComponent',
        cellConfig: {
          hasDownload: false,
          hasConfirmation: true,
        },
      });
    }

    return data;
  }

  private _updateState() {
    const data = this._inputData;
    const useFileNameAsDescription = this._useFileNameAsDescription;
    if (!data) return;
    if (data.files) {
      this.rows = this.uploadFileService.addTableRows(data.files, useFileNameAsDescription);
    }
    this._data = this._addColumn(data);
    this.optionsDropdown = this.getDocTypesList(data.tableColumns);
  }
}
