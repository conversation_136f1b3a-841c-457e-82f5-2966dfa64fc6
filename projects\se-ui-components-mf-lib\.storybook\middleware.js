const { createProxyMiddleware } = require('http-proxy-middleware');

// More info: https://github.com/storybookjs/storybook/pull/435
module.exports = function (router) {
  const proxyOptions = {
    "/api/seguretat-admin/*": {
      target: "https://dev.admin-seu.atc.intranet.gencat.cat/api/seguretat-admin",
      secure: false,
      logLevel: "debug",
      changeOrigin: true,
      pathRewrite: { "^/api/seguretat-admin": "" },
    },
    "/api/seguretat-privat/*": {
      target: "https://dev.admin-seu.atc.intranet.gencat.cat/api/seguretat-privat",
      secure: false,
      logLevel: "debug",
      changeOrigin: true,
      pathRewrite: { "^/api/seguretat-privat": "" },
    },
    "/api/seguretat/*": {
      target: "https://dev.seu2.atc.intranet.gencat.cat/api/seguretat",
      secure: false,
      logLevel: "debug",
      changeOrigin: true,
      pathRewrite: { "^/api/seguretat": "" },
    },
    "/mf/pt-commons-mf/*": {
      target: "https://dev.seu2.atc.intranet.gencat.cat/mf/pt-commons-mf",
      secure: false,
      logLevel: "debug",
      changeOrigin: true,
      pathRewrite: { "^/mf/pt-commons-mf": "" },
    },
    "/api/gestions/*": {
      target: "https://dev.seu2.atc.intranet.gencat.cat/api/gestions",
      secure: false,
      logLevel: "debug",
      changeOrigin: true,
      pathRewrite: { "^/api/gestions": "" },
    },
    "/api/tributs/*": {
      target: "https://dev.seu2.atc.intranet.gencat.cat/api/tributs",
      secure: false,
      logLevel: "debug",
      changeOrigin: true,
      pathRewrite: { "^/api/tributs": "" },
    },
    "/api/micro-service/*": {
      target: "http://dev.ui-components.atc.intranet.gencat.cat/api/micro-service/",
      secure: false,
      logLevel: "debug",
      changeOrigin: true,
      pathRewrite: { "^/api/micro-service": "" },
    }
  };

  Object.keys(proxyOptions).forEach((path) => {
    router.use(path, createProxyMiddleware(proxyOptions[path]));
  });
};
