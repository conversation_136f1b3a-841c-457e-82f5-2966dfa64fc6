import { Directive, Input, OnInit, ViewContainerRef } from '@angular/core';
import { FlattenedRow } from '../rows/rows.model';
import { FlattenedCell } from './cells.model';

@Directive({
  selector: '[cellTemplate]',
})
export class CellTemplateDirective implements OnInit {
  @Input() cell!: FlattenedCell;
  @Input() row!: FlattenedRow;

  constructor(private viewContainer: ViewContainerRef) {}

  ngOnInit(): void {
    const cellComponent = this.viewContainer.createComponent(
      this.cell.component
    );

    cellComponent.instance.value = this.cell.value;
    cellComponent.instance.cellConfig = this.cell.cellConfig;
    cellComponent.instance.row = this.row;
    cellComponent.instance.cell = this.cell;
    cellComponent.instance.column = this.cell.column;
    cellComponent.changeDetectorRef.markForCheck();
  }
}
