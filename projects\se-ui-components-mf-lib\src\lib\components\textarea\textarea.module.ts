import {CommonModule} from '@angular/common';
import {NgModule} from '@angular/core';
import {TextareaComponent} from './textarea.component';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {SeSharedModule} from "../../shared/shared.module";
import {SeFormControlErrorModule} from "../form-control-error/form-control-error.module";

@NgModule({
  declarations: [TextareaComponent],
  imports: [CommonModule, ReactiveFormsModule, SeSharedModule, FormsModule, SeFormControlErrorModule],
  exports: [TextareaComponent],
})
export class SeTextareaModule {
}
