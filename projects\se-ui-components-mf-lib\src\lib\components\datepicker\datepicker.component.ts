import { Nullable } from './../../models/nullable.model';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
  ViewChild,
  forwardRef,
} from '@angular/core';
import { ControlContainer, ControlValueAccessor, FormControl, FormGroup, NG_VALUE_ACCESSOR } from '@angular/forms';
import { Calendar } from 'primeng/calendar';
import { DateUtilsService } from '../../services';

@Component({
  selector: 'se-datepicker',
  template: `
    <div class="se-datepicker" [ngClass]="{'showIcon': showIcon}">
      <div class="input-label" *ngIf="label || tooltip">
        <label *ngIf="label" aria-hidden="true">{{ label | translate }}</label>
        <ng-icon class="tooltip-icon" *ngIf="tooltip" name="matInfo" [pTooltipAccessible]="tooltipText"></ng-icon>
      </div>
      <div class="se-datepicker__container">
        <ng-icon class="valid-icon" *ngIf="control.touched && control.status === 'VALID' && showValidation" name="matCheckOutline"></ng-icon>
        <ng-icon 
          *ngIf="showIcon"
          class="calendar-icon"   
          [ngClass]="[
            control.disabled ? 'disabled' : '',
            control.touched && control.status === 'INVALID' ? 'invalid' : ''
          ]"
          [name]="iconName"
          (click)="openCalendar()">
        </ng-icon>
        <p-calendar
          #calendarComponent
          class="calendar-input"
          [ngClass]="[ 
            control.disabled ? 'disabled' : '', 
            control.touched && control.status === 'INVALID' ? 'invalid' : ''
          ]"
          [ariaLabel]="
            ((ariaLabel ? ariaLabel : label) | translate) +
            ': ' +
            formatAriaLabelDate()
          "
          [inputId]="id"        
          [(ngModel)]="value"
          [inline]="inline"
          [minDate]="minDate"
          [maxDate]="maxDate"
          [disabledDates]="disabledDates"
          [appendTo]="appendTo"
          [showTime]="showTime"
          [dateFormat]="dateFormat"
          [placeholder]="placeholder"
          [defaultDate]="defaultDate"
          [disabled]="disabled"
          [selectionMode]="selectionMode"
          [firstDayOfWeek]="1"
          (onSelect)="onSelect($event)"
          (onInput)="onInput($event)"
        ></p-calendar>
        <label class="hidden-aria-label">
          {{
            ((ariaLabel ?? label) | translate) +
              ': ' +
              this.formatAriaLabelDate()
          }}
        </label>
      </div>
      <se-error-message [control]="control"></se-error-message>
    </div>
  `,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DatepickerComponent),
      multi: true,
    },
  ],
  styleUrls: ['./datepicker.component.scss'],
})
export class DatepickerComponent implements ControlValueAccessor {

  /* Component internal variables */
	@ViewChild('calendarComponent', { static: true }) private calendar: Calendar | undefined;

  @Input() id!: string;
  @Input() label!: string;
  @Input() ariaLabel: string | undefined;
  @Input() appendTo: string | null = null;
  @Input() minDate!: Date;
  @Input() maxDate!: Date;
  @Input() defaultDate: Date = new Date();
  @Input() disabledDates: Date[] = [];
  @Input() showTime = false;
  @Input() showIcon = false;
  @Input() iconName = 'matCalendarMonthOutline';
  @Input() placeholder = '';
  @Input() inline = false;
  @Input() selectionMode: 'single' | 'multiple' | 'range' | undefined = 'single';
  @Input() formControlName!: string;
  @Input() dateFormat: string = 'dd/mm/yy';
  @Input() tooltip = false;
  @Input() tooltipText: string | TemplateRef<HTMLElement> | undefined;
  @Input() showValidation = true;

  @Input() set disabled(value: boolean) {
    this._disabled = value;
    if(!value) {
      this.getFormControl()?.enable({ emitEvent: true });
    } else {
      this.getFormControl()?.disable({ emitEvent: true });
    }
  }

  get disabled() { return this._disabled };

  @Output() dateSelectedEvent = new EventEmitter<Date | Date[]>();

  value: Date | Date[] | null = [];
  control!: FormControl;

  private _listDates: Date[] = [];
  private _disabled: boolean = false;


  constructor(
    private controlContainer: ControlContainer,
    public dateUtilsService: DateUtilsService,
  ) {}

  ngOnInit() {
    this.control = this.getFormControl();

    this._disabled = this.control.disabled || this.disabled;

    this.control.registerOnDisabledChange((value) => { this.setDisabledState(value) })
  }

  ngAfterViewInit(): void {
    this.setCalendarInputRole();
  }

  private onChange = (value: any) => {};
  private onTouched = () => {};

  writeValue(value: any): void {
    this.value = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this._disabled = isDisabled;
  }

  onSelect(date: Date) {
    if (this.selectionMode !== 'single') {
      // Reset listDates when selection mode is range and listDates length is 2
      if (
        this.selectionMode === 'range' && 
        (
          this._listDates.length === 2 ||
          date < this._listDates[0]  
        ) 
      ) {
        this._listDates = [];
      }

      this._listDates.push(date);
    } 

    const currentValue = this.selectionMode === 'single' ? date : this._listDates;
    
    this.onChange(currentValue);
    this.onTouched();
    this.value = currentValue;
    this.dateSelectedEvent.emit(currentValue);
  }

  onInput(event: InputEvent) {
    // Update value when it is deleted
    if(!event.data) {
      this.onChange(null);
      this.onTouched();
      this.value = null;
    } else if (this.value && this.selectionMode === 'single') {
      this.onSelect(this.value as Date)
    }
  }
  
  private getFormControl() : FormControl {
    if(this.control){
      return this.control;
    }

    return (this.controlContainer.control as FormGroup).get(
      this.formControlName
    ) as FormControl;
  }

  private setCalendarInputRole(){
		// aria-haspopup needs one of the following roles: menu, listbox, tree, grid, dialog, combobox or true
		const [inputCalendar] = this.calendar?.containerViewChild?.nativeElement.querySelectorAll('.p-calendar > input');
		if (inputCalendar){
			inputCalendar.role = 'combobox';
			inputCalendar.setAttribute('aria-controls', 'listbox' + this.id);
			inputCalendar.setAttribute('aria-expanded', false);
		} 
	}

  openCalendar() {
    this.calendar?.toggle();
  }

  formatAriaLabelDate(): Nullable<string> {
    if (Array.isArray(this.value)) {
      return this.value
        .map((d) => this.dateUtilsService.formatDate(d, 'dd/MM/yyyy') ?? '')
        .join(' - ');
    }

    return this.dateUtilsService.formatDate(this.value, 'dd/MM/yyyy') ?? '';
  }
}
