.se-input {
  position: relative;
  margin-bottom: 1rem;

  .input-label {
    &.right-align {
      text-align: right;
    }

    &.center-align {
      text-align: center;
    }
  }

  .input-element {
    position: relative;

    .input {
      font-family: var(--font-primary);
      font-size: var(--text-sm);
      line-height: var(--line-sm);
      color: var(--color-gray-700);
      display: flex;
      height: 40px;
      width: 100%;
      padding: 0px 8px;
      align-items: center;
      border-radius: 4px !important;
      border: 1px solid var(--color-gray-400);
      background: var(--color-white);
      outline: none;

      &.disabled {
        cursor: not-allowed;
        background: var(--color-gray-200);
        text-decoration: none;
        opacity: 0.8;
      }

      &.invalid {
        border-color: var(--color-red-400);
        background: var(--color-white);
      }

      &.valid {
        border-color: var(--color-green-300);
        background: var(--color-white);
        padding-right: 38px;
      }

      &.clear-icon-spacing {
        padding-right: 38px;
      }

      &.second-icon-spacing {
        padding-right: 62px;
      }

      &.readonly {
        border: none;
        background-color: transparent;
        color: var(--color-black);
        font-family: var(--font-primary);
        font-size: var(--text-sm);
        line-height: 40px;
        opacity: 1 !important;
        padding: 0;
      }

      &.with-icon {
        border-radius: 4px 0 0 4px;
      }

      &.right-align {
        text-align: right;
      }

      &.center-align {
        text-align: center;
      }

      &:enabled:focus {
        border-color: var(--color-blue-500);
        outline: 2px solid var(--color-primary-link);
        outline-offset: 1px;

        &.invalid {
          border-color: var(--color-red-400);
          background: var(--color-white);
        }

        &.valid {
          border-color: var(--color-green-300);
          background: var(--color-white);
          padding-right: 38px;
        }

        &.clear-icon-spacing {
          padding-right: 38px;
        }

        &.second-icon-spacing {
          padding-right: 62px;
        }
      }
    }

    .input-check {
      color: var(--color-green-300);
      position: absolute;
      top: 8px;
      right: 8px;
      width: 24px;
      height: 24px;
      font-size: var(--text-xl);
    }

    .input-clear {
      color: var(--color-gray-700);
      position: absolute;
      top: 8px;
      right: 8px;
      width: 24px;
      height: 24px;
      font-size: var(--text-xl);
    }

    .input-clear:hover {
      cursor: pointer;
    }

    .second-icon {
      right: 32px;
    }

    .input-icon {
      position: absolute;
      justify-content: center;
      display: flex;
      height: 40px;
      width: 40px;
      right: 0;
      top: 0;
      align-items: center;
      border-radius: 0 4px 4px 0;
      border: 1px solid var(--color-gray-400);
      background: var(--color-blue-500);
      color: var(--color-white);
      outline: none;

      &.secondary {
        border: 1px solid var(--color-primary-action);
        background: var(--color-white);
        color: var(--color-primary-action);
      }

      &.disabled {
        cursor: not-allowed !important;
        background: var(--color-gray-400);
        text-decoration: none;
      }

      &.invalid {
        border-color: var(--color-red-400);
        background: var(--color-red-400);
      }

      &.valid {
        border-color: var(--color-green-300);
        background: var(--color-green-300);
      }
    }

  }

  &__inline {
    display: inline-flex;
    align-items: flex-start;

    .input-label {
      margin-right: 1rem;
      height: 40px;
      line-height: 40px;
      margin-bottom: 0;
    }
  }
}
