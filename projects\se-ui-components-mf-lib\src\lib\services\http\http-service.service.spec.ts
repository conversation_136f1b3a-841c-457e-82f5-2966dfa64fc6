import { TestBed } from '@angular/core/testing';
import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TranslateService } from '@ngx-translate/core';
import { SeAuthService } from '../auth/auth.service';
import { SeHttpRequest } from './http-service.model';
import { SeHttpService } from './http-service.service';
import { SeMessageService } from '../message/message.service';
import { SeSpinnerService } from '../../components/spinner/spinner.service';

describe('SeHttpService', () => {
  let service: SeHttpService;
  let httpMock: HttpTestingController;

  const mockTranslateService = {};
  const mockSeAuthService = {
    getSessionStorageUser: jasmine
      .createSpy('getSessionStorageUser')
      .and.returnValue({}),
    validCookieToken: jasmine
      .createSpy('validCookieToken')
      .and.returnValue(true),
    getCookieToken: jasmine
      .createSpy('getCookieToken')
      .and.returnValue('some-token'),
  };
  const mockSeMessageService = {};
  const mockSeSpinnerService = {
    start: jasmine.createSpy('start').and.returnValue(null),
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        SeHttpService,
        { provide: TranslateService, useValue: mockTranslateService },
        { provide: SeAuthService, useValue: mockSeAuthService },
        { provide: SeMessageService, useValue: mockSeMessageService },
        { provide: SeSpinnerService, useValue: mockSeSpinnerService },
      ],
    });

    service = TestBed.inject(SeHttpService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    // Ensure that no requests are outstanding.
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should perform a GET request', (done) => {
    const mockResponse = '{"data": "mock data for GET"}';
    const mockRequest: SeHttpRequest = {
      baseUrl: 'http://localhost',
      url: '/api/testGet',
      method: 'get',
    };

    service.get(mockRequest).subscribe((response) => {
      expect(response).toEqual({ data: 'mock data for GET' });
      done();
    });

    const req = httpMock.expectOne('http://localhost/api/testGet');
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

  it('should perform a POST request', (done) => {
    const mockResponse = '{"data": "mock data for POST"}';
    const mockRequest: SeHttpRequest = {
      baseUrl: 'http://localhost',
      url: '/api/testPost',
      method: 'post',
    };

    service.post(mockRequest).subscribe((response) => {
      expect(response).toEqual({ data: 'mock data for POST' });
      done();
    });

    const req = httpMock.expectOne('http://localhost/api/testPost');
    expect(req.request.method).toBe('POST');
    req.flush(mockResponse);
  });

  it('should perform a PUT request', (done) => {
    const mockResponse = '{"data": "mock data for PUT"}';
    const mockRequest: SeHttpRequest = {
      baseUrl: 'http://localhost',
      url: '/api/testPut',
      method: 'put',
    };

    service.put(mockRequest).subscribe((response) => {
      expect(response).toEqual({ data: 'mock data for PUT' });
      done();
    });

    const req = httpMock.expectOne('http://localhost/api/testPut');
    expect(req.request.method).toBe('PUT');
    req.flush(mockResponse);
  });

  it('should perform a DELETE request', (done) => {
    const mockResponse = '{"data": "mock data for DELETE"}';
    const mockRequest: SeHttpRequest = {
      baseUrl: 'http://localhost',
      url: '/api/testDelete',
      method: 'delete',
    };

    service.delete(mockRequest).subscribe((response) => {
      expect(response).toEqual({ data: 'mock data for DELETE' });
      done();
    });

    const req = httpMock.expectOne('http://localhost/api/testDelete');
    expect(req.request.method).toBe('DELETE');
    req.flush(mockResponse);
  });
});
