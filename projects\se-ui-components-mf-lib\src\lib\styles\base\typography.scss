@use 'sass:map';

/* Importar Open Sans desde Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600&display=swap");

$font-weights: (
  'thin': 100,
  'extralight': 200,
  'light': 300,
  'regular': 400,
  'medium': 500,
  'semibold': 600,
  'bold': 700,
  'extrabold': 800,
  'black': 900,
);

$font-sizes: (
  '2xs': 12px,
  'xs': 13px,
  'sm': 14px,
  'base': 16px,
  'md': 18px,
  'lg': 20px,
  'xl': 24px,
  '2xl': 28px,
  '3xl': 35px,
  '4xl': 48px,
);

$line-heights: (
  '2xs': 16px,
  'xs': 16px,
  'sm': 20px,
  'base': 24px,
  'md': 26px,
  'lg': 28px,
  'xl': 32px,
  '2xl': 36px,
  '3xl': 44px,
  '4xl': 56px,
);

$font-styles: normal, italic, oblique;

:root {
  --font-primary: "Open Sans", sans-serif;

  // font weights
  @each $name, $weight in $font-weights {
    --font-#{$name}: #{$weight};
  }

  // font sizes
  @each $name, $size in $font-sizes {
    --text-#{$name}: #{$size};
  }

  // line heights
  @each $name, $height in $line-heights {
    --line-#{$name}: #{$height};
  }
}

.font-primary {
  font-family: var(--font-primary);
}

@each $name, $size in $font-sizes {
  .text-#{$name} {
    font-size: var(--text-#{$name});
    line-height: var(--line-#{$name});
  }
}

@each $name in map.keys($line-heights) {
  .line-#{$name} {
    line-height: var(--line-#{$name}) !important;
  }
}

@each $name in map.keys($font-weights) {
  .#{$name} {
    font-weight: var(--font-#{$name});
  }
}

@each $name in $font-styles {
  .#{$name} {
    font-style: #{$name};
  }
}

.text-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.text-nowrap {
  white-space: nowrap;
}

/* --- HEADINGS --- */
h1 {
  font-family: var(--font-primary);
  font-size: var(--text-3xl);
  line-height: var(--line-3xl);
  font-weight: var(--font-semibold);
}

h2 {
  font-family: var(--font-primary);
  font-size: var(--text-3xl);
  line-height: var(--line-3xl);
  font-weight: var(--font-regular);
}

h3 {
  font-family: var(--font-primary);
  font-size: var(--text-2xl);
  line-height: var(--line-2xl);
  font-weight: var(--font-regular);
}

h4 {
  font-family: var(--font-primary);
  font-size: var(--text-xl);
  line-height: var(--line-xl);
  font-weight: var(--font-semibold);
}

h5 {
  font-family: var(--font-primary);
  font-size: var(--text-sm);
  line-height: var(--line-sm);
  font-weight: var(--font-semibold);
}

h6 {
  font-family: var(--font-primary);
  font-size: var(--text-sm);
  line-height: var(--line-sm);
  font-weight: var(--font-regular);
}

/* --- TEXTS AND LINKS --- */

p {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-regular);
}

.se-link {
  font-family: var(--font-primary);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  background-color: transparent;

  &.left .link-icon,
  &.right .link-icon {
    width: auto;
    min-width: fit-content;
    vertical-align: text-bottom;
  }

  &.left {
    flex-direction: row-reverse;

    .link-icon {
      padding-right: 4px;
    }
  }

  &.right {
    flex-direction: row;

    .link-icon {
      padding-left: 4px;
    }
  }

  &.focus {
    outline: none;
  }

  &:focus-visible {
    /* Visible in the full-colour space */
    box-shadow: 0 0 0 1px currentColor;
    border-radius: 2px;

    /* Visible in Windows high-contrast themes */
    outline-color: transparent;
    outline-width: 1px;
    outline-style: solid;
  }

  &.disabled {
    cursor: not-allowed;
  }

  &.primary {
    color: var(--color-pink-500);

    &:hover {
      text-decoration: underline;
    }

    &:active {
      color: var(--color-pink-700);
    }

    &.disabled {
      color: var(--color-gray-400);
      text-decoration: none;
    }
  }

  &.secondary {
    color: var(--color-blue-500);

    &:hover {
      text-decoration: underline;
    }

    &:active {
      color: var(--color-blue-600);
    }

    &.semibold:active {
      color: var(--color-blue-800);
    }

    &.semibold:focus-visible {
      /* Visible in the full-colour space */
      box-shadow: 0 0 0 1px var(--color-blue-800);
    }

    &.disabled {
      color: var(--color-gray-500);
      text-decoration: none;
    }
  }

  &.dashed {
    text-decoration: underline dashed !important;
    text-underline-offset: 2px;
  }

  &.onlyText {
    color: var(--color-gray-600);

    &:hover {
      text-decoration: underline;
    }

    &:active {
      color: var(--color-gray-700);
    }

    &.disabled {
      color: var(--color-gray-400);
    }
  }

  &.inverse {
    color: var(--color-white);

    &:hover,
    &:active,
    &.disabled {
      color: var(--color-gray-400);
      text-decoration: underline;
    }

    &.disabled {
      color: var(--color-gray-400);
    }
  }
}

.wrap-break-word {
  overflow-wrap: break-word;
}
