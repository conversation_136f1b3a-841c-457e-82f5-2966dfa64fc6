import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  forwardRef,
  HostListener,
} from '@angular/core';
import {
  NG_VALUE_ACCESSOR,
  ControlValueAccessor,
  ControlContainer,
  FormControl,
  FormGroup,
} from '@angular/forms';
import { Subscription } from 'rxjs';

@Component({
  selector: 'se-radio',
  template: `
    <div class="radio-container">
      <label [class.disabled]="disabled">
        <input
          type="radio"
          [id]="id"
          [attr.name]="name"
          [value]="value"
          [checked]="isChecked()"
          [disabled]="disabled"
          (change)="handleChange($event)"
          (blur)="onTouched()"
          style="display: none;"
        />
        <div class="radio-icon-container d-flex align-self-start">
          <ng-icon
            [name]="getIconName()"
            class="d-flex align-self-center"
            [class.radio-icon]="true"
            [class.radio-icon-checked]="isChecked()"
            [class.radio-icon-unchecked]="!isChecked()"
            [tabindex]="disabled ? -1 : 0"
          ></ng-icon>
        </div>
        <span class="radio-label text-sm" [ngClass]="{'bold': isChecked()}">{{ label | translate }}</span>
      </label>
      <span [class.disabled]="disabled" class="radio-subtitle text-xs" *ngIf="subtitle" [innerHTML]="subtitle | translate | safeHtml"></span>
    </div>

  `,
  styleUrls: ['./radio.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RadioComponent),
      multi: true,
    },
  ],
})
export class RadioComponent implements ControlValueAccessor, OnInit, OnDestroy {

  @Input() id!: string;
  @Input() label: string = '';
  @Input() subtitle: string | undefined;
  @Input() name: string = '';
  @Input() value: any;
  @Input() formControlName: string | number | null = null;
  @Input() set disabled(value: boolean) {
      this._disabled = value;
  }

  get disabled() { return this._disabled };

  @Output() valueChanged = new EventEmitter<any>();

  control!: FormControl;
  internalValue: any;

  private _disabled: boolean = false;
  private subscription!: Subscription;

  constructor(private controlContainer: ControlContainer) {}

  ngOnInit(): void {
    this.control = this.getFormControl();

    this._disabled = this.control.disabled || this.disabled;

    this.subscription = this.control.valueChanges.subscribe((value) => {
      this.internalValue = value;
    });

    this.control.registerOnDisabledChange((value) => { this._disabled = value; });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  handleChange(event: Event): void {
    if (!this.disabled) {
      this.internalValue = this.value;
      this.onChange(this.internalValue);
      this.onTouched();
      this.valueChanged.emit(this.internalValue);
    }
  }

  @HostListener('keydown', ['$event'])
  handleKeydown(event: KeyboardEvent): void {
    if (event.key === 'Space' || event.key === 'Enter' && !this.disabled) {
      this.handleChange(event);
      event.preventDefault();
    }
  }

  onChange: (value: any) => void = () => {};

  onTouched: () => void = () => {};

  writeValue(value: any): void {
    this.internalValue = value;
  }

  registerOnChange(fn: (value: any) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  getIconName(): string {
    return this.isChecked()
      ? 'matRadioButtonChecked'
      : 'matRadioButtonUnchecked';
  }

  isChecked = (): boolean => { return this.value === this.internalValue }

  private getFormControl() : FormControl {
    if(this.control){
      return this.control;
    }

    return (this.controlContainer.control as FormGroup).get(
      this.formControlName!.toString()
    ) as FormControl;
  }
}
