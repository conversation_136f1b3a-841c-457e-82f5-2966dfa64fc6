{"generatedAt": 1694775960443, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "packageManager": {"type": "npm", "version": "9.6.7"}, "framework": {"name": "@storybook/angular", "options": {}}, "builder": "@storybook/builder-webpack5", "renderer": "@storybook/angular", "storybookVersion": "7.0.27", "storybookVersionSpecifier": "^7.0.27", "language": "typescript", "storybookPackages": {"@storybook/angular": {"version": "7.0.27"}, "@storybook/blocks": {"version": "7.0.27"}, "@storybook/testing-library": {"version": "0.0.14-next.2"}, "storybook": {"version": "7.0.27"}}, "addons": {"@storybook/addon-links": {"version": "7.0.27"}, "@storybook/addon-essentials": {"version": "7.0.27"}, "@storybook/addon-interactions": {"version": "7.0.27"}}}