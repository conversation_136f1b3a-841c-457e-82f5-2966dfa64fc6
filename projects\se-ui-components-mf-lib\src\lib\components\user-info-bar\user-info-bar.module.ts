import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { SeButtonModule } from '../button/button.module';
import { TranslateModule } from '@ngx-translate/core';
import { SeSharedModule } from '../../shared/shared.module';
import { UserInfoBarComponent } from './user-info-bar.component';

@NgModule({
  declarations: [UserInfoBarComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    SeButtonModule,
    SeSharedModule,
  ],
  exports: [UserInfoBarComponent],
})
export class SeUserInfoBarModule {}
