import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { InfoMessagesComponent } from './info-messages.component';
import { SeSharedModule } from '../../shared/shared.module';
import { SeButtonModule } from '../button';

@NgModule({
  declarations: [InfoMessagesComponent],
  imports: [
    CommonModule,
    TranslateModule,
    SeSharedModule,
    SeButtonModule,
  ],
  exports: [InfoMessagesComponent],
})
export class SeInfoMessagesModule {}
