// cell-template.component.ts
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import { CellComponent, CellConfig, FlattenedCell } from '../cells.model';
import { SeTagTooltipPosition } from '../../../tag/models/tag.model';

@Component({
  selector: 'se-default-cell',
  template: `
    <div>
      <se-tag [showTooltipIcon]="getShowTooltipIcon()" [tooltipStyleClass]="'responsive-table-tab'" [tooltipPosition]="getTooltipPosition()" [tooltipText]="getTooltip()" [tagTheme]="cellConfig.tagCell?.tagTheme ?? 'primary'" [closable]="false">
        {{ value | translate }}
      </se-tag>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagCellComponent implements CellComponent {

  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

  getTooltip = (): string => this.cellConfig.tooltip ? (this.cellConfig.tooltipText ?? this.value) : '';
  getShowTooltipIcon = (): boolean => this.cellConfig["showTooltipIcon"]
  getTooltipPosition = (): SeTagTooltipPosition => this.cellConfig["tooltipPosition"] ?? SeTagTooltipPosition.TOP;
}
