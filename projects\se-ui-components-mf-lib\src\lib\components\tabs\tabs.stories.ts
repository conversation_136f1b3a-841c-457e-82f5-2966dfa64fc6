import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { TabViewComponent } from './tab-view/tab-view.component';
import { SeTabsModule } from './tabs.module';

const meta: Meta = {
  title: 'Components/Tabs',
  component: TabViewComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [SeTabsModule],
    }),
  ],
  argTypes: {
    activeTabIndex: {
      description: 'Active tab index.',
      control: { type: 'number' },
      table: {
        defaultValue: { summary: 0 },
      },
    },
  },
  args: {
    activeTabIndex: 7,
  },
  render: (args) => ({
    template: `
      <se-tab-view [activeTabIndex]="activeTabIndex" (onChange)="onChange($event)">
        <se-tab-item
            label="Tab 1"
            [showBadge]="true"
            [badgeTextInsideCircle]="'1'">
          Content 1
        </se-tab-item>
        <se-tab-item label="Tab 2">
          Content 2
        </se-tab-item>
        <se-tab-item label="Tab 3" [disabled]="true">
          Content 3
        </se-tab-item>
        <se-tab-item label="Tab 4">
          Content 4
        </se-tab-item>
        <se-tab-item label="Tab 5">
          Content 5
        </se-tab-item>
        <se-tab-item label="Tab 6">
          Content 6
        </se-tab-item>
        <se-tab-item label="Tab 7">
          Content 7
        </se-tab-item>
        <se-tab-item label="Tab 8" [index]="8">
          Content 8
        </se-tab-item>
      </se-tab-view>`,
      props: {
        ...args,
        onChange: (i: number) => {
          console.log('index',i);
        }
      },
  }),
};

export default meta;
type Story = StoryObj<TabViewComponent>;

export const Default: Story = {};
