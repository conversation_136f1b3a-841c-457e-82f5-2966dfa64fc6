import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { SeSharedModule } from '../../shared/shared.module';
import { SeButtonModule } from '../button/button.module';
import { SeEmptyStateModule } from '../empty-state/empty-state.module';
import { SePaginationModule } from '../pagination/pagination.module';
import { ColumnsModule } from './columns/columns.module';
import { RowsModule } from './rows/rows.module';
import { TableComponent } from './table.component';
import { SeCheckboxModule } from '../checkbox/checkbox.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SePanelModule } from '../panel/panel.module';
import { CellsModule } from './cells/cell.module';

@NgModule({
  declarations: [TableComponent],
  imports: [
    CommonModule,
    SePaginationModule,
    RowsModule,
    ColumnsModule,
    TranslateModule.forChild(),
    SeButtonModule,
    SeSharedModule,
    SeEmptyStateModule,
    SeCheckboxModule,
    FormsModule,
    SePanelModule,
    ReactiveFormsModule,
    CellsModule
  ],
  exports: [TableComponent],
})
export class SeTableModule {}
