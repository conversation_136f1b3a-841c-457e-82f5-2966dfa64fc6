import { provideAnimations } from '@angular/platform-browser/animations';
import {
  Meta,
  StoryObj,
  applicationConfig,
  moduleMetadata,
} from '@storybook/angular';
import { Column } from '../table/columns/column.model';
import { UploadFilesComponent } from './upload-files.component';
import { SeUploadFilesModule } from './upload-files.module';
import { FileFormatsSeparation, UploadAcceptFormats } from './upload-files.model';

const tableColumns: Column[] = [
  {
    header: 'SE_COMPONENTS.FILE_UPLOADER.NAME',
    key: 'name',
    cellComponentName: 'defaultCellComponent',
    resizable: false,
    cellConfig: { tooltip: true, ellipsis: true },
  },
  {
    header: 'SE_COMPONENTS.FILE_UPLOADER.SIZE',
    key: 'size',
    cellComponentName: 'defaultCellComponent',
    resizable: false,
  },
  {
    header: 'SE_COMPONENTS.FILE_UPLOADER.DESCRIPTION',
    key: 'description',
    cellComponentName: 'defaultCellComponent',
    resizable: false,
  },
  {
    header: 'UI_COMPONENTS.ATTACH_FILES.DOCUMENT_TYPE_LABEL',
    key: 'docType',
    cellComponentName: 'defaultCellComponent',
    resizable: false,
  },
];

const modalTableColumns: Column[] = [
  {
    header: 'SE_COMPONENTS.FILE_UPLOADER.NAME',
    key: 'name',
    cellComponentName: 'defaultCellComponent',
    cellConfig: { tooltip: true, ellipsis: true },
    resizable: false,
  },
  {
    header: 'SE_COMPONENTS.FILE_UPLOADER.SIZE',
    key: 'size',
    size: 10,
    cellComponentName: 'defaultCellComponent',
    resizable: false,
  },
  {
    header: 'SE_COMPONENTS.FILE_UPLOADER.DESCRIPTION',
    key: 'description',
    cellComponentName: 'inputCellComponent',
    resizable: false,
    cellConfig: {
      id: 'description',
      label: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
      disabled: false,
      showClear: false,
      editable: false,
      readOnly: false,
      filter: false,
      optionLabel: 'label',
      optionValue: 'id',
      placeholder: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
      required: false,
    },
  },
  {
    header: 'UI_COMPONENTS.ATTACH_FILES.DOCUMENT_TYPE_LABEL',
    key: 'docType',
    cellComponentName: 'dropdownCellComponent',
    resizable: false,
    cellConfig: {
      id: 'docType',
      options: [
        { id: 1, label: 'DocType 1' },
        { id: 2, label: 'DocType 2' },
        { id: 3, label: 'DocType 3' },
        { id: 4, label: 'DocType 4' },
        { id: 5, label: 'DocType 5' },
      ],
      disabled: false,
      showClear: false,
      autoSelect: true,
      editable: false,
      readOnly: false,
      filter: false,
      optionLabel: 'label',
      optionValue: 'id',
      placeholder: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
      required: true,
    },
  },
];

const attachFiles = [
  {
    id: 1,
    name: 'File1.pdf',
    size: 1024,
    description: 'Description for File1',
    docType: 'DocType 1',
  },
  {
    id: 2,
    name: 'File2.jpg',
    size: 2048,
    description: 'Description for File2',
    docType: 'DocType 2',
    emptySize: true,
    rowConfig: {
      hasDelete: false,
      hasDownload: false,
    }
  },
];

const hasModal = true;
const hasActions = true;
const info = 'Mensaje de información';
const title = 'Esto es un titulo';
const accept = [UploadAcceptFormats.pdf, UploadAcceptFormats.jpg, UploadAcceptFormats.zip];
const multiple = true;
const sizeLimitPerFile = 25000;
const groupSizeLimit = 50000;
const maxFiles = 25;
const subtitle = true;
const required = true;

// More on how to set up stories at: https://storybook.js.org/docs/angular/writing-stories/introduction
const meta: Meta<UploadFilesComponent> = {
  title: 'Components/Upload files',
  component: UploadFilesComponent,
  decorators: [
    moduleMetadata({
      imports: [SeUploadFilesModule],
    }),
    applicationConfig({
      providers: [provideAnimations()],
    }),
  ],
  tags: ['autodocs'],
  args: {
    tableColumns,
    modalTableColumns,
    hasModal,
    hasActions,
    info,
    title,
    subtitle,
    accept,
    multiple,
    sizeLimitPerFile,
    groupSizeLimit,
    maxFiles,
    required,
    showInput: true,
    onlyTable: false,
    disabled: false,
    fileFormatSeparation: FileFormatsSeparation.SLASH,
  },
  argTypes: {
    subtitle: {
      description: 'Subtitle',
      control: { type: 'boolean' },
      type: 'boolean',
      table: {
        defaultValue: { summary: true },
      },
    },
    subtitleText: {
      description: 'SubtitleText',
      control: { type: 'string' },
      type: 'string',
      table: {
        defaultValue: { summary: 'undefined' },
      },
    },
    hasModal: {
      description: 'hasModal',
      control: { type: 'boolean' },
      type: 'boolean',
      table: {
        defaultValue: true,
      },
    },
    hasActions: {
      description: 'hasActions',
      control: { type: 'boolean' },
      type: 'boolean',
      table: {
        defaultValue: true,
      },
    },
    files: {
      description: 'files',
      control: { type: 'array' },
      type: 'string',
    },
    info: {
      description: 'info',
      control: { type: 'string' },
      type: 'string',
      table: {
        defaultValue: 'Info',
      },
    },
    title: {
      description: 'title',
      control: { type: 'string' },
      type: 'string',
      table: {
        defaultValue: 'Title',
      },
    },
    accept: {
      description: 'accept',
      control: { type: 'array' },
      type: 'string',
    },
    multiple: {
      description: 'multiple',
      control: { type: 'boolean' },
      type: 'boolean',
      table: {
        defaultValue: true,
      },
    },
    sizeLimitPerFile: {
      description: 'sizeLimitPerFile',
      control: { type: 'number' },
      type: 'number',
      table: {
        defaultValue: 5000,
      },
    },
    groupSizeLimit: {
      description: 'sizeLimitPerFile',
      control: { type: 'number' },
      type: 'number',
      table: {
        defaultValue: 125000,
      },
    },
    maxFiles: {
      description: 'sizeLimitPerFile',
      control: { type: 'number' },
      type: 'number',
      table: {
        defaultValue: 25,
      },
    },
    tableColumns: {
      description: 'tableColumns',
      control: { type: 'array' },
      type: 'string',
      table: {
        defaultValue: { summary: '[]' },
      },
    },
    modalTableColumns: {
      description: 'modalTableColumns',
      control: { type: 'array' },
      type: 'string',
      table: {
        defaultValue: { summary: '[]' },
      },
    },
    required: {
      description: 'required',
      control: { type: 'boolean' },
      type: 'boolean',
      table: {
        defaultValue: true,
      },
    },
    showInput: {
      description: 'showInput',
      control: { type: 'boolean' },
      type: 'boolean',
      table: {
        defaultValue: true,
      },
    },
    onlyTable: {
      description: 'onlyTable',
      control: { type: 'boolean' },
      type: 'boolean',
      table: {
        defaultValue: false,
      },
    },
    disabled: {
      description: 'disabled',
      control: { type: 'boolean' },
      type: 'boolean',
      table: {
        defaultValue: false,
      },
    },
    fileFormatSeparation: {
      description: 'file separation format in droparea subtitle',
      control: { type: 'string' },
      type: 'string',
      table: {
        defaultValue: FileFormatsSeparation.SLASH,
      },
    },
  },
  render: (args) => ({
    props: args,
    template: `
      <se-upload-files
        [hasActions]="hasActions"
        [subtitle]="subtitle"
        [subtitleText]="subtitleText"
        [tableColumns]="tableColumns"
        [modalTableColumns]="modalTableColumns"
        [files]="files"
        [disabled]="disabled"
        [hasModal]="hasModal"
        [showInput]="showInput"
        [onlyTable]="onlyTable"
        [info]="info"
        [title]="title"
        [accept]="accept"
        [required]="required"
        [multiple]="multiple"
        [sizeLimitPerFile]="sizeLimitPerFile"
        [fileFormatSeparation]="fileFormatSeparation"
        [groupSizeLimit]="groupSizeLimit"
        [maxFiles]="maxFiles"
      >
      </se-upload-files>
    `,
  }),
};

export default meta;
type Story = StoryObj<UploadFilesComponent>;

export const Data: Story = {};
export const SubtitleCustom: Story = {
  args: {
    subtitleText: 'Texto Subtitulo Custom',
  },
};
export const TableMode: Story = {
  args: {
    showInput: false,
    files: attachFiles as any,
  },
};
export const OnlyTableMode: Story = {
  args: {
    onlyTable: true,
    files: attachFiles as any,
    title: '',
    info: undefined,
  },
};
export const NoModal: Story = {
  args: {
    hasModal: false,
  },
  render: (args) => ({
    props: {
      ...args,
    },
    template: `
      <se-upload-files
        [hasActions]="hasActions"
        [subtitle]="subtitle"
        [subtitleText]="subtitleText"
        [tableColumns]="modalTableColumns"
        [modalTableColumns]="modalTableColumns"
        [hasModal]="hasModal"
        [info]="info"
        [title]="title"
        [accept]="accept"
        [required]="required"
        [multiple]="multiple"
        [sizeLimitPerFile]="sizeLimitPerFile"
        [groupSizeLimit]="groupSizeLimit"
        [maxFiles]="maxFiles"
      >
      </se-upload-files>
    `,
  }),
};
export const NoMultiple: Story = {
  args: {
    multiple: false,
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
  },
};

export const CommaSeparation: Story = {
  args: {
    fileFormatSeparation: FileFormatsSeparation.COMMA,
  },
};
