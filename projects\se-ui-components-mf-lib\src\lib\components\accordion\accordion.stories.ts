import { Meta, StoryObj, applicationConfig, moduleMetadata } from '@storybook/angular';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
} from '@angular/forms';

import { SeAccordionModule } from './accordion.module';
import { AccordionComponent } from './accordion.component';
import { provideAnimations } from '@angular/platform-browser/animations';

const meta: Meta<AccordionComponent> = {
  title: 'Components/Accordion',
  component: AccordionComponent,
  decorators: [
    moduleMetadata({
      imports: [SeAccordionModule, ReactiveFormsModule]
    }),
    applicationConfig({
      providers: [provideAnimations()],
    }),
  ],
  tags: ['autodocs'],
  args: {
    inputCheckbox: {
      label: 'Test checkbox',
      id: 'checkbox-id',
      value: false,
    },
    inputRadio: {
      label: 'Test radio',
      name: 'radio',
      id: 'radio-id',
      value: '1',
    },
  },
  argTypes: {
    title: {
      description: 'title without input',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'test title' } },
    },
    inputType: {
      description: 'input type checkbox or radio',
      options: ['checkbox', 'radio', undefined],
      control: { type: 'select' },
      table: { defaultValue: { summary: 'checkbox' } },
    },
    tooltip: {
      description: 'is tooltip visible',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    tooltipText: {
      description: 'tooltip message',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
    collapsed: {
      description: 'is accordion collapsed',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    collapsible: {
      description: 'is accordion collapsible',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: true } },
    },
  },
  render: (args) => ({
    props: args,
    template: `
      <se-accordion
        [title]="title"
        [inputType]="inputType"
        [inputCheckbox]="inputCheckbox"
        [inputRadio]="inputRadio"
        [tooltip]="tooltip"
        [tooltipText]="tooltipText"
        [collapsed]="collapsed"
        [collapsible]="collapsible"
      >
        test
      </se-accordion>
    `,
  }),
};

export default meta;
type Story = StoryObj<AccordionComponent>;

export const Title: Story = {
  args: { title: 'Test Label', collapsed: true, collapsible: true },
};
export const Expanded: Story = {
  args: { title: 'Test Label ', collapsed: false, collapsible: true },
};
export const TitleAndTooltip: Story = {
  args: { title: 'Test Label extended over protected', tooltip: true, tooltipText: 'test', collapsed: true,  collapsible: true },
};
export const NoCollapsible: Story = {
  args: { title: 'Test Label', collapsible: false },
};

export const Checkbox: Story = {
  args: {
    inputType: 'checkbox',
    inputCheckbox: {
      label: 'Test checkbox',
      id: 'checkbox-id',
      value: false,
    },
    collapsed: true,
    collapsible: true,
    controlName: 'value',
    tooltip: true, tooltipText: 'test'
  },
  render: (args) => ({
    template: `
      <form [formGroup]="form">
        <se-accordion
          [inputType]="inputType"
          [inputCheckbox]="inputCheckbox"
          [collapsed]="collapsed"
          [tooltip]="tooltip"
          [tooltipText]="tooltipText"
          [controlName]="'value'"
          (onSelectionChange)="onSelectionChange($event)"
          (onCollapsedChange)="onCollapsedChange($event)"
        >
          test
        </se-accordion>
         <se-accordion
          [inputType]="inputType"
          [collapsed]="collapsed"
          [tooltip]="tooltip"
          [tooltipText]="tooltipText"
          [inputCheckbox]="inputCheckbox2"
          [controlName]="'value2'"
          (onSelectionChange)="onSelectionChange($event)"
          (onCollapsedChange)="onCollapsedChange($event)"
        >
          test
        </se-accordion>
      </form>
      {{form.value | json}}
    `,
    props: {
      ...args,
      inputCheckbox2: {
        label: 'Test checkbox 2',
        id: 'checkbox-id2',
        value: false,
        disabled: false,
      },
      form: new FormGroup({
        value: new FormControl(true),
        value2: new FormControl(false),
      }),
      onSelectionChange: (event: any) => {
        console.log('select - ', event);
      },
      onCollapsedChange: (event: any) => {
        console.log('collapsed change - ', event);
      },
    },
  }),
};

export const CheckboxDisabled: Story = {
  args: {
    inputCheckbox: {
      label: 'Test checkbox',
      id: 'checkbox-id',
      value: false,
      disabled: true,
    },
    collapsed: false,
    collapsible: true,
    inputType: 'checkbox',
    controlName: 'value',
  },
  render: (args) => ({
    template: `
      <form [formGroup]="form">
        <se-accordion
          [inputType]="inputType"
          [collapsed]="collapsed"
          [inputCheckbox]="inputCheckbox"
          [controlName]="controlName"
        >
          test
        </se-accordion>
      </form>
    `,
    props: {
      ...args,
      form: new FormGroup({
        value: new FormControl(true),
        value2: new FormControl(false),
      }),
    },
  }),
};


export const Radio: Story = {
  args: {
    inputRadio: {
      label: 'Test radio',
      name: 'radio',
      id: 'radio-id',
      value: '1',
    },
    controlName: 'value',
    inputType: 'radio',
    collapsed: false,
    collapsible: true,
  },
  render: (args) => ({
    template: `
      <form [formGroup]="form">
        <se-accordion
          [inputType]="inputType"
          [inputRadio]="inputRadio"
          [controlName]="controlName"
          [collapsed]="collapsed"
        >
          test
        </se-accordion>
        <se-accordion
          [inputType]="inputType"
          [inputRadio]="inputRadio2"
          [controlName]="controlName"
          [collapsed]="collapsed"
        >
          test
        </se-accordion>
      </form>
      {{form.value | json}}
    `,
    props: {
      ...args,

    inputRadio2: {
      label: 'Test radio 2',
      name: 'radio',
      id: 'radio-id2',
      value: '2',
    },
      form: new FormGroup({
        value: new FormControl(),
      }),
    },
  }),
};

export const RadioDisabled: Story = {
  args: {
    inputRadio: {
      label: 'Test radio',
      name: 'radio',
      id: 'radio-id',
      value: '1',
      disabled: true,
    },
    collapsed: false,
    controlName: 'value',
    inputType: 'radio',
    collapsible: true,
  },
  render: (args) => ({
    template: `
      <form [formGroup]="form">
        <se-accordion
          [inputType]="inputType"
          [inputRadio]="inputRadio"
          [collapsed]="collapsed"
          [controlName]="controlName"
        >
          test
        </se-accordion>
      </form>
    `,
    props: {
      ...args,
      form: new FormGroup({
        value: new FormControl(true),
      }),
    },
  }),
};
