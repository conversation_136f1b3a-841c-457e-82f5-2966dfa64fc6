import { formatNumber } from '@angular/common';
import {
  Component,
  Input,
  OnInit,
  forwardRef,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
  Inject,
  LOCALE_ID,
  TemplateRef,
} from '@angular/core';
import {
  NG_VALUE_ACCESSOR,
  FormControl,
  ControlContainer,
  FormGroup,
  ControlValueAccessor,
} from '@angular/forms';

import type { Nullable } from '../../models';
import { SeInputModeEnum, type SeInputMode } from './input.model';

@Component({
  selector: 'se-input',
  styleUrls: ['./input.component.scss'],
  template: `
    <div class="se-input" [ngClass]="{ 'se-input__inline': inline }">
      <div class="input-label" *ngIf="label || tooltip" [ngClass]="{ 'right-align': labelAlign === 'right', 'center-align': labelAlign === 'center' }">
        <label *ngIf="label" aria-hidden="true">
          {{ label | translate }}
        </label>
        <ng-icon class="tooltip-icon" *ngIf="tooltip" name="matInfo" [pTooltipAccessible]="tooltipText"></ng-icon>
      </div>
      <div class="input-element">
        <ng-icon
          *ngIf="control.touched && control.status === 'VALID' && !readonly && control.value && showValidation"
          class="input-check"
          [ngClass]="{'input-number': type === 'number', 'second-icon': showClear && showValidation}"
          name="matCheckOutline"
        ></ng-icon>
        <ng-icon
          *ngIf="control.value && !readonly && !disabled && showClear"
          class="input-clear"
          name="matCloseOutline"
          (click)="clearField()"
        ></ng-icon>
        <input
          #inputRef
          [ngClass]="{
            'disabled': disabled,
            'invalid': control.touched && control.status === 'INVALID',
            'valid': control.touched && control.status === 'VALID' && control.value && showValidation,
            'readonly': readonly,
            'with-icon': icon,
            'right-align': labelAlign === 'right',
            'center-align': labelAlign === 'center',
            'clear-icon-spacing': showClear && control.touched && !readonly && control.value,
            'second-icon-spacing': showClear && showValidation && control.touched && control.status === 'VALID' && !readonly && control.value,
          }"
          class="input"
          [id]="id"
          [disabled]="disabled || readonly"
          [type]="type"
          [placeholder]="placeholder"
          [value]="value"
          [min]="min"
          [max]="max"
          [ariaLabel]="
            ((ariaLabel ? ariaLabel : label) | translate) +
            (inputRef.value ? ': ' + inputRef.value : '')
          "
          [attr.aria-labelledby]="'hidden-label'"
          [attr.aria-expanded]="ariaExpanded"
          (input)="handleChange($event)"
          (keyup)="handleKeyup()"
          (blur)="handleBlur($event)"
          [title]="ariaLabel ?? ''"
          [autocomplete]="autocomplete"
          [inputMode]="inputmode"
        />
        <label id="hiddenLabel" class="hidden-aria-label">
          {{
            ((ariaLabel ?? label) | translate) +
              (inputRef.value ? ': ' + inputRef.value : '')
          }}
        </label>
        <button
          *ngIf="icon"
          [attr.aria-label]=" ariaLabelButton ?? 'buscar' | translate"
          [ngClass]="[
            disabled ? 'disabled' : '',
            control.touched && showValidation && control.status === 'INVALID' ? 'invalid' : '',
            control.touched && showValidation && control.status === 'VALID' ? 'valid' : '',
            buttonClass
          ]"
          class="input-icon text-xl"
          (click)="handleClick($event)"
          [pTooltipAccessible]="searchButtonTooltip"
          [tooltipPosition]="'bottom'"
        >
          <ng-icon [name]="icon"> </ng-icon>
        </button>
        <div class="error-message">
          <se-error-message
            [control]="control"
            [currency]="{
              isCurrency: currencyMode,
              symbol: currencySymbol,
              decimals,
            }"
          />
        </div>
      </div>
    </div>
  `,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InputComponent),
      multi: true,
    },
  ],
})
export class InputComponent implements OnInit, ControlValueAccessor {

  @ViewChild('inputRef',{static: true}) inputRef!: ElementRef;

  @Input() id!: string;
  @Input() label!: string;
  @Input() readonly = false;
  @Input() icon!: string;
  @Input() placeholder = '';
  @Input() min: number | undefined;
  @Input() max: number | undefined;
  @Input() maxLength: number | undefined;
  @Input() type = 'text';
  @Input() formControlName!: string;
  @Input() inline: boolean = false;
  @Input() currencyMode: boolean = false;
  @Input() currencySymbol: string = '€';
  @Input() decimals: number = 2;
  @Input() tooltip = false;
  @Input() tooltipText: string | TemplateRef<HTMLElement> | undefined;
  @Input() ariaLabel: string | undefined;
  @Input() ariaExpanded: Nullable<boolean>;
  @Input() labelAlign: 'left' | 'center' | 'right' = 'left';
  @Input() showValidation: boolean = true;
  @Input() showClear: boolean = false;
  @Input() autocomplete: Nullable<string> = 'on';
  @Input() inputmode: Nullable<SeInputMode> = SeInputModeEnum.TEXT;
  @Input() buttonClass: string = '';
  @Input() ariaLabelButton: string | undefined;
  @Input() searchButtonTooltip: TemplateRef<HTMLElement> | string | undefined;

  @Input() set disabled(value: boolean) {
    this.setDisabledState(value);

    if(!value) {
      this.getFormControl()?.enable({ emitEvent: true });
    } else {
      this.getFormControl()?.disable({ emitEvent: true });
    }
  }

  get disabled() { return this._disabled };

  value!: string | number | null;
  control!: FormControl;
  currencySymbolRegex!: RegExp;

  private _disabled: boolean = false;

  @Output() onClick: EventEmitter<any> = new EventEmitter<any>();
  @Output() onBlur: EventEmitter<any> = new EventEmitter<any>();
  @Output() onClear: EventEmitter<any> = new EventEmitter<any>();

  constructor(
    private controlContainer: ControlContainer,
    @Inject(LOCALE_ID) private locale: string
  ) {
    // Intencionadamente vacío
  }

  ngOnInit() {
    this.control = this.getFormControl();

    this._disabled = this.control.disabled || this.disabled;

    this.control.registerOnDisabledChange((value) => { this.setDisabledState(value) });

    this.currencySymbolRegex = new RegExp(`[^ ${this.currencySymbol}]`,'g');
  }

  private getFormControl() : FormControl {
    if(this.control){
      return this.control;
    }

    return (this.controlContainer.control as FormGroup).get(
      this.formControlName
    ) as FormControl;
  }

  private onChange: (value: any) => void = () => {};

  handleKeyup() {
    this.checkNumberValue()
    this.checkMaxLength()
  }

  checkNumberValue() {
    if (this.decimals === 0 && this.value && this.type === 'number') {
      // Formateo el valor a solo números y lo machaco en el formControl
      this.value = this.value.toString().replace(/\D/g, '');
      this.getFormControl().setValue(this.value);
    }
  }

  checkMaxLength() {
    if (
      !this.currencyMode &&
      this.maxLength &&
      this.maxLength > -1 &&
      this.value &&
      this.value.toString().length > this.maxLength
    ) {
      this.value = this.value.toString().slice(0, this.maxLength)
      this.getFormControl().setValue(this.value)
    }
  }

  private onTouched: () => void = () => {};

  writeValue(value: string | number | null): void {
    if (this.currencyMode && this.inputRef) {
      this.writeCurrencyValue(value,this.inputRef.nativeElement);
    } else {
      this.value = value;
    }

    this.onTouched();
  }

  private writeCurrencyValue(value: string | number | null, inputRef: HTMLInputElement) {

    const inputValue = typeof value === 'number'
    ? formatNumber(value, this.locale, '1.0-' + (this.decimals ?? 0))
    : value;

    if(this.value === undefined) {
      this.value = inputValue ? `${this.handleCurrencyDecimalValue(inputValue, true) } ${this.currencySymbol}` : '';
      return;
    }

    if (inputValue) {
      this.handleCurrencyValue(inputRef, inputValue, true);
    } else {
      this.handleEmptyValue(inputRef);
    }
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this._disabled = isDisabled;
  }

  handleClick = (event: any): void => {
    if (this.disabled) return;
    this.onClick.emit(event);
  };

  handleChange(event: Event) {
    if (!this.disabled) {
      let value: number | string | null = this.getValue(event.target as HTMLInputElement);

      if(this.currencyMode) {
        value = value ? Number(value) : null;
      } else {
        this.value = value;
      }

      this.onChange(value);
    }
  }

  handleBlur(event: Event) {
    if (!this.disabled && this.currencyMode) {
      let value = this.getValue(event.target as HTMLInputElement, true);

      if(value.startsWith(',')){
        value = '0' + value;
        (event.target as HTMLInputElement).value = `${value} ${this.currencySymbol}`;
      }

      this.onChange(value ? Number(value) : null);
    }

    this.onTouched();
    this.checkMaxLength()
    this.onBlur.emit(this.control.value);
  }

  private getValue (input: HTMLInputElement, blur:boolean = false):string {
    if(this.currencyMode) return this.formatCurrency(input, blur) || "";

    return input.value;
  }

  private formatCurrency(input: HTMLInputElement, blur: boolean = false) {
    // appends currencySymbol to value, validates decimal side
    // and puts cursor back in right position.
    // get input value
    let inputValue = input.value;

    // Set value to 0,00 if value is empty
    if (inputValue === "" || !inputValue?.match(this.currencySymbolRegex)?.length) {
      return this.handleEmptyValue(input);
    }

    return this.handleCurrencyValue(input, inputValue, blur);
  }

  private handleEmptyValue(input: HTMLInputElement): string {
    const value = "";

    input.value = value;
    input.selectionStart = input.selectionEnd = 0;

    return value;
  }

  private handleCurrencyValue (
    input: HTMLInputElement,
    inputValue: string,
    blur: boolean = false
  ): string {
    // original length
    const originalLen = inputValue?.match(this.currencySymbolRegex)?.length ?? 0;

    // initial cursor position
    let cursorPos = input.selectionStart ?? 1;

    // check for decimal
    if (inputValue.indexOf(",") >= 0) {

      inputValue = this.handleCurrencyDecimalValue(inputValue,blur);

    } else {
      // no decimal entered
      // add dots to number
      // remove all non-digits
      inputValue = this.formatNumber(inputValue,true);

      // final formatting
      if (blur && this.decimals > 0) {
        inputValue += ",00";
      }
    }

    // add currency symbol
    inputValue = `${inputValue} ${this.currencySymbol}`
    input.value = inputValue;

    // put cursor back in the right position
    if(!blur)
      this.setInputCursor(input, inputValue, cursorPos, originalLen);

    return inputValue.replace(` ${this.currencySymbol}`,"").replaceAll(".","").replaceAll(",",".");
  }

  private formatNumber(number: string, trimLeftZeros: boolean = false, addThousandPoints: boolean = true) {

    // format number 1000000 to 1.234.567
    // elimino el simbolo por que en currencySymbol con numeros los incluye dentro del numero ejemplo 'm2'
    let formattedNumber = number
      .replace(` ${this.currencySymbol}`, '')
      .replace(/\D/g, '');

    if(addThousandPoints) {
      formattedNumber = formattedNumber.replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    }
    // delete left 0
    if(trimLeftZeros) return formattedNumber.replace(/^0+(?=\d)/,"");

    return formattedNumber;
  }

  private handleCurrencyDecimalValue (inputValue: string, blur: boolean):string {
    // get position of first decimal
    // this prevents multiple decimals from
    // being entered
    const decimalPos = inputValue.indexOf(",");

    // split number by decimal point
    let leftSide = (decimalPos < 0) ? inputValue : inputValue.substring(0, decimalPos);
    let rightSide = (decimalPos < 0) ? '' : inputValue.substring(decimalPos);

    // add commas to left side of number
    leftSide = this.formatNumber(leftSide, true);

    // validate right side
    rightSide = this.formatNumber(rightSide, false, false);

    // Limit decimal to only 2 digits
    rightSide = rightSide.substring(0, this.decimals);

    if(blur){
      rightSide = rightSide.replace(/0+$/, "");
    }

    // On blur make sure 2 numbers after decimal
    if(blur && rightSide.length <= 2 && this.decimals > 0){
      rightSide += "0".repeat(2 - rightSide.length);
    }

    // join number by .
    return (this.decimals === 0)? `${leftSide}` : `${leftSide},${rightSide}`;
  }

  private setInputCursor (
    input: HTMLInputElement,
    inputValue: string,
    cursorPos: number,
    originalLen: number
  ): void {
    if(inputValue.startsWith(',')){
      cursorPos = 0;
   } else {
      let updatedLen = inputValue?.match(this.currencySymbolRegex)?.length ?? 0;
      cursorPos = updatedLen - originalLen + cursorPos;
    }
    input.setSelectionRange(cursorPos, cursorPos);
  }

  clearField() {
    this.value = '';
    this.onChange(null);
    this.onClear.emit();
  }
}
