import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { SeProgressModalModule } from './progress-modal.module';
import { ProgressModalComponent } from './progress-modal.component';
import { TranslateModule } from '@ngx-translate/core';
import { Component, Input } from '@angular/core';
import { SeModalService } from '../modal/modal.service';
import { Subject, takeUntil } from 'rxjs';
import { SeButton } from '../button';

@Component({
  selector: 'progress-modal-service-button',
  template: `
    <button color="primary" (click)="openModal()">Open Modal</button>
  `,
})
class ProgressModalServiceExampleButtonComponent {
  @Input() message: string = '';
  @Input() interval: number = 1; //seconds

  constructor(private modalService: SeModalService) {}

  openModal(): void {
    let sum = 0;
    const customButton: SeButton = {
      label: 'Cancel',
      size: 'small',
      btnTheme: 'secondary',
    };

    const ref = this.modalService.openProgressModal(
      this.interval,
      this.message,
      undefined,
      undefined,
      customButton
    );
    const cancelProgressModal$: Subject<void> = new Subject();

    //SE EJECUTA CADA SEGUNDO
    ref.componentInstance.intervalOutput
      .pipe(takeUntil(cancelProgressModal$))
      .subscribe({
        next: () => {
          sum++;

          if (sum > 5) {
            ref.close();
          } else {
            ref.componentInstance.message = this.message + ' ' + sum;
          }
        },
        complete: () => ref.close(),
      });

    ref.componentInstance.onCustomButton.subscribe(() => {
      cancelProgressModal$.next();
      cancelProgressModal$.complete();
    });
  }
}

const meta: Meta<ProgressModalComponent> = {
  title: 'Components/Progress Modal',
  component: ProgressModalComponent,

  decorators: [
    moduleMetadata({
      imports: [SeProgressModalModule, TranslateModule.forChild()],
      declarations: [ProgressModalServiceExampleButtonComponent],
    }),
  ],
  args: {
    message: 'Action in progress...',
    subtitle: 'Please wait...',
    interval: 10
  },
  argTypes: {
    interval: {
      description: 'Modal progress interval',
      control: { type: 'number' },
      table: { defaultValue: { summary: '0' } },
    },
    message: {
      description: 'Modal progress message',
      control: { type: 'text' },
      table: {
        defaultValue: { summary: '' },
      },
    },
    subtitle: {
      description: 'Modal progress subtitle',
      control: { type: 'text' },
      table: {
        defaultValue: { summary: '' },
      },
    },
    progressValue$: {
      description: 'Observable for progress value',
      control: { type: 'object' },
      table: {
        defaultValue: { summary: 'undefined' },
      },
    },
    customButton: {
      description: 'Custom button configuration',
      control: { type: 'object' },
      table: {
        defaultValue: { summary: 'null' },
      },
    },
  },
  render: (args) => ({
    props: {
      ...args,
      onInterval: () => {
        console.log('++');
      },
    },
    template: `
      <se-progress-modal
        [interval]="interval"
        [message]="message"
        [subtitle]="subtitle"
        [customButton]="customButton"
        [progressValue$]="progressValue$"
        (intervalOutput)="onInterval()">
      </se-progress-modal>
      `,
  }),
};

export default meta;
type Story = StoryObj<ProgressModalComponent>;

export const Default: Story = {};

const progressValue$ = new Subject<number>();
let value = 0;
setInterval(() => {
  value += 5;
  progressValue$!.next(value);
}, 2000);
export const progressBar: Story = {
  args: {
    interval: 2,
    progressValue$: progressValue$
  }
};

export const CustomButton: Story = {
  args: {
    customButton: {
      label: 'Custom Action',
      size: 'small',
      btnTheme: 'secondary',
    },
  },
};

export const ModalService: Story = {
  render: (args) => ({
    props: {
      interval: 1,
      message: 'Action in progress in the modal...',
    },
    template: `
    <progress-modal-service-button
      [interval]="interval"
      [message]="message">
    </progress-modal-service-button>
    `,
  }),
};
