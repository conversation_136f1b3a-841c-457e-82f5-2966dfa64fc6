import { Component, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { Substitutiva } from '../tax-year-declaration.model';
import { TaxYearDeclarationService } from '../services/tax-declaration.service';
import { Column, Row } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-substitutive-panel',
  templateUrl: './substitutive-panel.component.html',
  styleUrls: [],
})
export class SubstitutivePanelComponent {
  @Input() set taxYear(value: string | null) {
    if (value) {
      this.alertInfo = this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.ALERT_INFO',
        { taxYear: value },
      );
    }
  }
  @Input() set substitutiva(value: Substitutiva | null) {
    this.substitutiveIds.push(value ? value.numJustificant : '');
    this.tableData = value
      ? [
          {
            data: {
              numJustificant: { value: value.numJustificant },
              declarant: { value: value.declarant },
              dataPresentacio: { value: value.dataPresentacio },
              estat: { value: value.estat },
            },
          },
        ]
      : [];
  }

  @Input() samePresenter: boolean = true;

  @Output() substitutiveChange: Subject<boolean> = new Subject<boolean>();

  protected alertInfo: string = this.translateService.instant(
    'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.ALERT_INFO',
  );
  protected tableColumns: Column[] =
    this.taxDeclarationService.getSubstitutiveTableColumns();
  protected tableData: Row[] = [];
  protected componentForm: FormGroup;
  protected showDifferentPresenterForm: boolean = false;
  protected substitutiveIds: string[] = [];

  constructor(
    private translateService: TranslateService,
    private fb: FormBuilder,
    private taxDeclarationService: TaxYearDeclarationService,
  ) {
    this.componentForm = this.fb.group({
      substitutiva: [false],
      numJustificant: [''],
      dataPresentacio: [''],
    });
  }

  protected onSubstitutiveChange(event: boolean): void {
    this.substitutiveChange.next(event);
  }

  protected openDifferentPresenterForm(event: boolean): void {
    this.showDifferentPresenterForm = event;
  }

  protected validateDifferentPresenterData(): void {
    // Validate different presenter data logic here
  }
}
