:host {
  td {
    padding: 0;
  }

  se-panel ::ng-deep p-panel {
    .p-panel-header {
      font-size: var(--text-base);
      border-inline: 0;
      border-top: 0;
    }

    .p-toggleable-content {
      display: none;
    }
  }
}

// RESPONSIVE
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

// @media only screen and (max-width: 768px) {
@include media-breakpoint-down(md) {
  :host {
    display: flex;
    flex-direction: column;

    td {
      border-bottom: none;
    }

    se-panel ::ng-deep p-panel {
      .p-panel-header {
        border: 1px solid var(--color-gray-300);
        border-bottom: 0;
      }
    }
  }
}
