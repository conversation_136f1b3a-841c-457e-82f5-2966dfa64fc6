import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import { CellComponent, CellConfig, CellEventTypes, FlattenedCell } from '../cells.model';
import { CellEventService } from '../cell-event.service';

@Component({
  selector: 'edit-cell',
  template: `
    <div class="edit-row-container">
      <button class="action-button" *ngIf="this.cellConfig.editCallback" [title]="(this.cellConfig['editLabel'] ??'UI_COMPONENTS.BUTTONS.EDIT') | translate" (click)="onEdit()">
        <ng-icon name="matEditOutline"></ng-icon>
      </button>
      <button class="action-button" *ngIf="this.cellConfig.deleteCallback" [title]="(this.cellConfig['deleteLabel'] ??'UI_COMPONENTS.BUTTONS.DELETE') | translate" (click)="onDelete()">
        <ng-icon name="matDeleteOutline"></ng-icon>
      </button>
    </div>
  `,
  styles: [
    `
      .edit-row-container {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
      }

      .action-button {
        all: initial;
        font-size: var(--text-lg);
        cursor: pointer;
        color: var(--color-blue-500);
      }

      .action-button:hover {
        color: var(--color-blue-700);
      }

      .action-button:focus {
        box-shadow: none;
        border: 1px solid var(--color-blue-500);
        outline: 2px solid var(--color-primary-link);
        outline-offset: 1px;
        border-radius: 4px;
      }
    `,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditCellComponent implements CellComponent {
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

  constructor(private cellEventService: CellEventService) {}

  async onEdit() {
    if (!this.cellConfig.editCallback) return this.throwEvent(CellEventTypes.EDIT_ROW, 'editCellComponent');
    const { data, apply } = await this.cellConfig.editCallback(this.row, this.cell, this.column);

    if (data) {
      this.throwEvent(CellEventTypes.EDIT_ROW, 'editCellComponent', { newData: data, rowId: this.row.id }, apply);
    }
  }

  async onDelete() {
    if (!this.cellConfig.deleteCallback) return this.throwEvent(CellEventTypes.DELETE_ROW, 'editCellComponent');
    const { delete: remove, apply } = await this.cellConfig.deleteCallback(this.row, this.cell, this.column);

    if (remove) {
      this.throwEvent(CellEventTypes.DELETE_ROW, 'editCellComponent', { row: this.row }, apply);
    }
  }

  private throwEvent(type: CellEventTypes, cellName: string, data?: any, apply = false) {
    this.cellEventService.emitEvent({
      type,
      cellName,
      cell: this.cell,
      data: data ? { ...data, apply } : { apply }
    })
  }
}
