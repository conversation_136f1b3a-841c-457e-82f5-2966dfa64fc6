

.action-button {
  all: initial;
  font-size: var(--text-lg);
  cursor: pointer;
  color: var(--color-blue-500);

  &:hover {
    color: var(--color-blue-700);
  }
  
  &:focus {
    box-shadow: none;
    border: 1px solid var(--color-blue-500);
    outline: 2px solid var(--color-primary-link);
    outline-offset: 1px;
    border-radius: 4px;
  }
  
}
// RESPONSIVE
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

// @media (max-width: 768px) {
@include media-breakpoint-down(sm) {
  .action-button {
    border: 1px solid var(--color-blue-500);
    border-radius: 4px;
    width: 30px;
    height: 30px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }
}