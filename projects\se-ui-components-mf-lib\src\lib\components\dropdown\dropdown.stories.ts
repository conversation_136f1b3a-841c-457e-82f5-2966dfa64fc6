import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Meta, StoryObj, applicationConfig, moduleMetadata } from '@storybook/angular';
import { DropdownComponent } from './dropdown.component';
import { SeDropdownModule } from './dropdown.module';
import { provideAnimations } from '@angular/platform-browser/animations';

const meta: Meta<DropdownComponent> = {
  title: 'Components/Dropdown',
  component: DropdownComponent,
  decorators: [
    moduleMetadata({
      imports: [SeDropdownModule, ReactiveFormsModule],
    }),
    applicationConfig({
      providers: [provideAnimations()],
    }),
  ],
  args: {
    id:'dropdown',
    label:'Select option',
    options: [
      { label: 'New York', id: 'NY'  },
      { label: 'Rome', id: 'RM', disabled: true },
      { label: 'London', id: 'LDN' },
      { label: 'Istanbul', id: 'IST' },
      { label: 'Paris', id: 'PRS' },
      { label: 'Berlin', id: 'BER' },
      { label: 'Madrid', id: 'MAD' },
      { label: 'Tokyo', id: 'TYO' },
      { label: 'Sydney', id: 'SYD' },
      { label: 'Toronto', id: 'TOR' },
      { label: 'Beijing', id: 'BJG' },
      { label: 'Moscow', id: 'MOW' },
      { label: 'Dubai', id: 'DXB' },
      { label: 'Singapore', id: 'SIN' },
      { label: 'Hong Kong', id: 'HKG' },
      { label: 'Bangkok', id: 'BKK' },
      { label: 'Los Angeles', id: 'LAX' },
      { label: 'Chicago', id: 'CHI' },
      { label: 'San Francisco', id: 'SFO' },
      { label: 'Miami', id: 'MIA' }
    ],
    disabled: false,
    showClear: false,
    editable: false,
    readOnly: false,
    filter: false,
    optionLabel: 'label',
    optionValue: 'id',
    placeholder: 'Select an option',
    virtualScroll: false,
    virtualScrollItemSize: 20,
    scrollHeight: '460px',
    autoSelect: false,
  },
  argTypes: {
    id: {
      description: 'Dropdown Id',
      control: { type: 'text' }
    },
    label: {
      description: 'Dropdown label',
      control: { type: 'text' }
    },
    options: {
      description: 'List of options to select',
      control: { type: 'object' }
    },
    disabled: {
      description: 'Determines if the dropdown is disabled or not.',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: 'false' } },
    },
    showClear: {
      description: 'Show clear option button',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: 'false' } },
    },
    editable: {
      description: 'It allows to enter a custom input',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: 'false' } },
    },
    autoSelect: {
      description: 'If true, the first option will be selected automatically',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: 'false' } },
    },
    readOnly: {
      description: 'Shows only the value',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: 'false' } },
    },
    filter: {
      description: 'Shows search bar',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: 'false' } },
    },
    optionLabel: {
      description: 'The property name to represent option label.',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'label' } },
    },
    optionValue: {
      description: 'The property name to represent option value.',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'id' } },
    },
    placeholder: {
      description:
        'The placeholder text for the dropdown when no option is selected.',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'Select an option' } },
    },
    virtualScroll: {
      description: 'VirtualScrolling is an efficient way of rendering large number of options',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: 'false' } },
    },
    virtualScrollItemSize: {
      description: 'Subset size of the virtual scroll',
      control: { type: 'number' },
      table: { defaultValue: { summary: undefined } },
    },
    scrollHeight: {
      description:
        'Height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.',
      control: { type: 'text' },
      table: { defaultValue: { summary: '460px' } },
    },
  },
  tags: ['autodocs'],
  render: (args) => ({
    template: `
      <form [formGroup]="form">
        <div style="min-height: 250px;">
          <se-dropdown
            [id]="id"
            [label]="label"
            [editable]="editable"
            [autoSelect]="autoSelect"
            [options]="options"
            [disabled]="disabled"
            [readOnly]="readOnly"
            [scrollHeight]="scrollHeight"
            [showClear]="showClear"
            [optionLabel]="optionLabel"
            [filter]="filter"
            [tooltip]="tooltip"
            [tooltipText]="tooltipText"
            [placeholder]="placeholder"
            formControlName="value"
            (dropdownOutput)="onDropdownOutput($event)">
          </se-dropdown>
        </div>
      </form>
      <button (click)="form.reset()">Reset</button>
    `,
    props: {
      ...args,
      form: new FormGroup({
        value: new FormControl('ABC',
          Validators.required,
        ),
      }),
      onDropdownOutput: ($event: any) => console.log($event)
    },
  }),
};

export default meta;
type Story = StoryObj<DropdownComponent>;

export const DefaultDropDown: Story = {};
export const DisabledDropDown: Story = {
  args:{
    id:'dropdownDisabled',
    label:'Disabled',
    disabled: true,
  }
}
export const Empty: Story = { args: { options: [] } };
export const WithSearchBar: Story = { args: { filter: true } };
export const Editable: Story = { args: { editable: true } };
export const Autoselect: Story = { args: { autoSelect: true, options: [{ label: 'New York', id: 'NY'  }] } };
export const WithTooltip: Story = { args: { tooltip: true, tooltipText: "Esto es un tooltip" } };
export const ReadOnlyDropDown: Story = {
  args:{
    id:'dropdownReadOnly',
    label:'Option',
    readOnly: true,
  },
  render: (args) => ({
    template: `
    <form [formGroup]="form">
      <div style="min-height: 250px;">
        <se-dropdown
          [id]="id"
          [label]="label"
          [editable]="editable"
          [options]="options"
          [disabled]="disabled"
          [readOnly]="readOnly"
          [showClear]="showClear"
          [filter]="filter"
          [optionLabel]="optionLabel"
          formControlName="value"
          (dropdownOutput)="onDropdownOutput($event)">
        </se-dropdown>
      </div>
    </form>
    `,
    props: {
      ...args,
      form: new FormGroup({
        value: new FormControl('LDN'),
      }),
    },
  }),
}


