.se-breadcrumb {

  &-nav {
    font-family: var(--font-primary);
    display: flex;

    &.breadcrumb {
      border: none;
      margin-left: 0;
    }

    .active {
      font-weight: bold;
      color: var(--color-black);
    }

    .inactive {
      color: var(--color-primary-action);
      text-decoration: none;
      cursor: pointer;

      &:hover {
        color: var(--color-blue-600);;
        text-decoration: underline;
      }
    }
    .separator {
      color: var(--color-primary-action);
      margin: 0 8px;
    }
  }

  &.secondary-theme {
    padding: 1rem;
    background-color: var(--color-gray-200);
  }

  &-title {
    margin-bottom: 0;
  }
}

