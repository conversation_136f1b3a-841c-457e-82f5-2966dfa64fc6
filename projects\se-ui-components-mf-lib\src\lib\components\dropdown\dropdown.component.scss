::ng-deep {

  .se-dropdown {
    margin-bottom: 1rem;
  }

  .p-dropdown-panel {
    border: 1px solid var(--color-gray-300);
    border-radius: 4px;
    margin-top: 4px;
    box-shadow: 0 2px 12px #0000001a;
  
    .p-dropdown-header {
      padding: 0.75rem;

      input.p-dropdown-filter {
        height: 40px;
        font-family: var(--font-primary);
        font-size: var(--text-sm);
        line-height: var(--line-sm);
        color: var(--color-gray-700);
        align-items: center;
        border-radius: 4px !important;
        border: 1px solid var(--color-gray-400);
        background: var(--color-white);
        outline: none;
      }

      .p-inputtext:enabled:focus {
        box-shadow: none;
      }

      .p-dropdown-filter-icon {
        top: 35%;
      } 
    }

    .p-dropdown-items {
      padding: 0;
      margin-bottom: 0;

      .p-dropdown-item,
      .p-dropdown-empty-message {
        font-family: var(--font-primary) !important;
        font-size: var(--text-sm) !important;
        line-height: var(--line-sm);
        padding: 12px 8px !important;
        border-bottom: none !important;

        &.p-highlight {
          color: var(--color-blue-600);
          background: var(--color-blue-200);
        }

        &:not(.p-highlight):not(.p-disabled):hover {
          color: var(--color-back);
          background: var(--color-blue-200);
        }

        &:not(.p-highlight).p-disabled {
          color: var(--color-gray-500);
          opacity: 1;
          pointer-events: auto;

          & span {
            pointer-events: auto;
          }
        }

        &:not(.p-disabled).p-focus {
          color: var(--color-back);
          background-color: var(--color-blue-200);
        }
      }
    }
  }

  p.readonly {
    color: var(--color-black);
    font-family: var(--font-primary);
    font-size: var(--text-sm);
    line-height: 40px;
    opacity: 1 !important;
    padding: 0;
  }

  #dropdown > .p-dropdown {
    width: 100%;
    height: 40px;
    border: 1px solid var(--color-gray-500);

    > .p-element {
      padding: 8px;
    }

    .p-icon-wrapper {
      display: flex; 
    }

    .p-placeholder {
      padding: 8px;
    }

    p-overlay {
      padding: 0 !important;
    }

    &:not(.p-disabled).p-focus {
      box-shadow: none;
      border: 1px solid var(--color-blue-500);
      outline: 2px solid var(--color-primary-link);
      outline-offset: 1px;
      
      .p-dropdown-trigger {
        color: var(--color-blue-500);
      }
    }

    &.p-disabled {
      background: var(--color-gray-200);
      text-decoration: none;
      opacity: 0.8;

      .p-dropdown-trigger,
      .p-dropdown-clear-icon {
        opacity: 0.5;
      }
    }

    .p-dropdown-label {
      font-family: var(--font-primary);
      font-size: var(--text-sm);
      line-height: var(--line-sm);

      &.p-inputtext {
        color: var(--color-black);
        border: none;
      }

      &.p-placeholder {
        color: var(--color-gray-650);
      }
    }

    .p-dropdown-trigger,
    .p-dropdown-clear-icon {
      top: 30%;
      color: var(--color-blue-500);
    }

    .p-dropdown-clear-icon .p-icon-wrapper {
      &:focus-visible {
        color: var(--color-blue-500);
        border: 1px solid var(--color-blue-500);
      }
    }
  }

  .dropdown-invalid {
    border: 1px solid var(--color-red-400) !important;
    .p-dropdown-trigger {
      color: var(--color-red-400) !important;
    }
  }

  .dropdown-invalid.p-dropdown.p-component.p-dropdown-open {
    border: 1px solid var(--color-red-400) !important;
    .p-dropdown-trigger {
      color: var(--color-red-400) !important;
    }
  }

  .modal.show + .p-overlay.p-component {
    z-index: 4099 !important;
  }
}
