import {
  Component,
  EventEmitter,
  HostListener,
  Input,
  Output,
  TemplateRef,
  ViewChild,
  forwardRef
} from '@angular/core';
import {
  ControlContainer,
  ControlValueAccessor,
  FormControl,
  FormGroup,
  NG_VALUE_ACCESSOR,
  ValidatorFn,
  Validators
} from '@angular/forms';
import { InputMask } from 'primeng/inputmask';
import { DeviceService } from '../../services/device/device.service';

@Component({
  selector: 'se-iban',
  template: `
  <div class="se-iban">
    <div class="input-label" *ngIf="label || tooltip">
      <label *ngIf="label" [for]="id" [ngClass]="{ 'right-align': labelAlign === 'right', 'center-align': labelAlign === 'center' }">
        {{ label | translate }}
      </label>
      <ng-icon class="tooltip-icon" *ngIf="tooltip" name="matInfo" [pTooltipAccessible]="tooltipText"></ng-icon>
    </div>
    <div class="iban-element">
      <ng-icon class="input-check" *ngIf="checkValid()" name="matCheckOutline"></ng-icon>
      <!-- Control code -->
      <p-inputMask
        [id]="id"
        [ngClass]="[ disabled ? 'disabled' : '', !control.pristine && control.status === 'INVALID' ? 'invalid' : '', !control.pristine && control.status === 'VALID' ? 'valid' : '']"
        class="iban-input"
        [disabled]="disabled"
        [placeholder]="placeholder"
        [slotChar]="slotChar"
        [mask]="mask"
        [unmask]="true"
        [autoClear]="false"
        [formControl]="control"
        (onInput)="ibanChange($event)">
      </p-inputMask>

    </div>
    <se-error-message [control]="control"></se-error-message>
  </div>
`,
  styleUrls: ['./iban.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => IbanComponent),
      multi: true,
    },
  ]
})
export class IbanComponent implements ControlValueAccessor {
  // Parted inputs
  @ViewChild('ibanMobileInput') ibanMobileInput!: InputMask;

  // Internal form
  ibanValidator: ValidatorFn[] = [Validators.maxLength(24), Validators.minLength(24)];
  
  private _disabled: boolean = false;

  /* Component inputs */
  @Input() id!: string;
  @Input() label!: string;
  @Input() slotChar: string = ' ';
  @Input() mask: string = 'aa99 9999 9999 9999 9999 9999';
  @Input() placeholder: string = 'ES12 1234 1234 1234 1234 1234';
  @Input() formControlName!: string;
  @Input() tooltip = false;
  @Input() tooltipText: string | TemplateRef<HTMLElement> | undefined;
  @Input() labelAlign: 'left' | 'center' | 'right' = 'left';

  @Input() set disabled(value: boolean) {
    this.setDisabledState(value);

    if(!value) {
      this.getFormControl()?.enable({ emitEvent: true });
    } else {
      this.getFormControl()?.disable({ emitEvent: true });
    }
  }

  get disabled() { return this._disabled };

  /* Component outputs */
  @Output() key: EventEmitter<string> = new EventEmitter<string>();

  /* Component internal variables */
  value!: string;
  control!: FormControl

  constructor(
    public deviceService: DeviceService,
    private controlContainer: ControlContainer,
  ) { }

  ngOnInit(): void {
    this.control = (this.controlContainer.control as FormGroup).get(
      this.formControlName
    ) as FormControl;

    this._disabled = this.control.disabled || this.disabled;

    this.control.registerOnDisabledChange((value) => { this.setDisabledState(value) })
  }

  private onChange: (value: any) => void = () => {};
  private onTouched: () => void = () => {};

  writeValue(value: string): void {
    this.value = value;
    this.onTouched();
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this._disabled = isDisabled;
  }

  ibanChange = (event: Event): void => {
    if (!this.disabled) {
      const input = event.target as HTMLInputElement;
      const cursorPos = input.selectionStart;
      const value = input.value?.toUpperCase().trim()?.replace(/[^a-zA-Z0-9]/g, '');
      this.onChange(value);
      this.deviceService.isMobile() ?? this.ibanMobileChanged(event)
      this.key.emit(value);
      this.control.setValue(value, { emitEvent: false });
      input.setSelectionRange(cursorPos, cursorPos);
    }
  }

  // Mobile version: value changed
  ibanMobileChanged = (event: any): void => {
    /**
     * Override PrimeNg default behaviour
     * @description With the default behaviour, the last inputed digit is not validated until the onBlur event is triggered.
     * With this override the last inputed digit is validate on time.
     */
    this.ibanMobileInput.onModelTouched();
    this.ibanMobileInput.checkVal();
    this.ibanMobileInput.updateFilledState();
    if (this.ibanMobileInput.inputViewChild?.nativeElement.value != this.ibanMobileInput.focusText 
      || this.ibanMobileInput.inputViewChild?.nativeElement.value != this.ibanMobileInput.value) {
      this.ibanMobileInput.updateModel(event);
      let event2 = document.createEvent('HTMLEvents');
      event2.initEvent('change', true, false);
      this.ibanMobileInput.inputViewChild?.nativeElement.dispatchEvent(event2);
    }
  }

  // Checks component valid icon display
  checkValid = (): boolean => {
    return this.control?.valid && this.control?.value
      && (this.control?.dirty || this.control?.touched)
  }

  private getFormControl() : FormControl {
    if(this.control){
      return this.control;
    }

    return (this.controlContainer.control as FormGroup).get(
      this.formControlName
    ) as FormControl;
  }

  /**
   * Host listener: device size
   * @description Keep this host listener in order to update the component between the mobile/tablet and desktop version.
   */
  @HostListener('window:resize', ['$event'])
  widnowResized = (event: Event): void => { };

}
