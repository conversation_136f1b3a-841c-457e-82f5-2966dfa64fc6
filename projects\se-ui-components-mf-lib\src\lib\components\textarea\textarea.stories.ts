import {FormControl, FormGroup, ReactiveFormsModule, Validators} from '@angular/forms';
import {Meta, moduleMetadata, StoryObj} from '@storybook/angular';
import {TextareaComponent} from './textarea.component';
import {SeTextareaModule} from './textarea.module';

const meta: Meta<TextareaComponent> = {
  title: 'Components/Textarea',
  component: TextareaComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [SeTextareaModule, ReactiveFormsModule],
    }),
  ],
  args: {
    label: 'Textarea label',
    disabled: false,
    readonly: false,
    maxlength: undefined
  },
  argTypes: {
    disabled: {
      description: 'Determines if the textarea is disabled or not.',
      control: {type: 'boolean'},
      table: {defaultValue: { summary: false }},
    },
    readonly: {
      description: 'Indicates if the textarea is in read-only mode.',
      control: {type: 'boolean'},
      table: {defaultValue: {summary: false}},
    },
    maxlength: {
      description: 'Sets the maximum length of characters',
      control: {type: 'number'},
      table: {defaultValue: {summary: undefined}},
    },
  },
  render: (args) => ({
    template: `
      <form [formGroup]="form">
        <se-textarea
          formControlName="value"
          [label]="label"
          [maxlength]="maxlength"
          [readonly]="readonly"
          [disabled]="disabled">
        </se-textarea>
      </form>
    `,
    props: {
      ...args,
      form: new FormGroup({
        value: new FormControl('', [
          Validators.required,
          Validators.minLength(3),
        ]),
      }),
    },
  }),
};

export default meta;
type Story = StoryObj<TextareaComponent>;

export const Textarea: Story = {};
export const DisabledTextarea: Story = {args: {value: 'Text', disabled: true}};
export const ReadonlyTextarea: Story = {args: {value: 'Text', readonly: true}};
