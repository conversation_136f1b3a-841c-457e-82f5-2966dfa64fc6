import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { RadioComponent } from './radio.component';
import { SeRadioModule } from './radio.module';
import { SeHighlightRadioContainerModule } from '../../directives';

const meta: Meta<RadioComponent> = {
  title: 'Components/Radio',
  component: RadioComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [SeRadioModule, ReactiveFormsModule, SeHighlightRadioContainerModule],
    }),
  ],
  args: {
    label: 'Radio label',
    disabled: false,
    name: 'optionName',
    subtitle: 'Subtitle example'
  },
  argTypes: {
    disabled: {
      description: 'Determines if the radio is disabled or not.',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    label: {
      description: 'Label text for the radio button.',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
    name: {
      description: 'Name of the radio button group.',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
  },
  render: (args) => ({
    template: `
      <form [formGroup]="form">
        <div>
          <se-radio
            id="value_1"
            [label]="label"
            [name]="name"
            [disabled]="disabled"
            [value]="1"
            formControlName="value">
          </se-radio>
        </div>
        <div>
          <se-radio
            id="value_2"
            [label]="label"
            [name]="name"
            [value]="2"
            [subtitle]="subtitle"
            formControlName="value">
          </se-radio>
        </div>
      </form>
    `,
    props: {
      ...args,
      form: new FormGroup({
        value: new FormControl(null, [
          Validators.required,
        ]),
      }),
    },
  }),
};

export default meta;
type Story = StoryObj<RadioComponent>;

export const Default: Story = {};

export const Disabled: Story = { args: { disabled: true } };

export const Inline: Story = {
  render: (args) => ({
    props: { 
      ...args,
      form: new FormGroup({
        value2: new FormControl(null, [
          Validators.required,
        ]),
      }),
    },
    template: `
    <form [formGroup]="form">
        <se-radio
          id="id1"
          [label]="label"
          [name]="name"
          [disabled]="disabled"
          [value]="1"
          formControlName="value2">
        </se-radio>
        <se-radio
          id="id2"
          [label]="label"
          [name]="name"
          [value]="2"
          formControlName="value2">
        </se-radio>
        <se-radio
          id="id3"
          [label]="label"
          [name]="name"
          [value]="3"
          formControlName="value2">
        </se-radio>
    </form>
  `
  }),
};

export const HiglightContainer: Story = {
  render: (args) => ({
    props: { 
      ...args,
      form: new FormGroup({
        value2: new FormControl(null, [
          Validators.required,
        ]),
      }),
    },
    template: `
    <form [formGroup]="form">
      <div seHighlightRadioContainer>
        <se-radio
          id="value_1"
          [label]="label"
          [name]="name"
          [disabled]="disabled"
          [value]="1"
          [subtitle]="subtitle"
          formControlName="value2">
        </se-radio>
      </div>
      <div seHighlightRadioContainer class="mt-4">
        <se-radio
          id="value_2"
          [label]="label"
          [name]="name"
          [value]="2"
          [subtitle]="subtitle"
          formControlName="value2">
        </se-radio>
      </div>
      <div seHighlightRadioContainer class="mt-4">
        <se-radio
          id="value_3"
          [label]="label"
          [name]="name"
          [value]="3"
          formControlName="value2">
        </se-radio>
      </div>
    </form>
  `
  }),
};
