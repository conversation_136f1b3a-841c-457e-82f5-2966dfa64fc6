import { Component, Input, TemplateRef, ViewChild } from '@angular/core';

@Component({
  selector: 'se-tab-item',
  template: `
    <ng-template>
      <ng-content></ng-content>
    </ng-template>
  `,
})
export class TabItemComponent {
  @Input() index: number | undefined;
  @Input() label!: string;
  @Input() disabled = false;
  @Input() showBadge: boolean = false;
  @Input() badgeColor: string = 'purple';
  @Input() badgeTextColor = 'white';
  @Input() badgeTextInsideCircle = '';
  @Input() badgeTheme: 'info' | 'success' | 'warning' | 'error' = 'info';

  @ViewChild(TemplateRef, { static: true }) content!: TemplateRef<any>;
}
