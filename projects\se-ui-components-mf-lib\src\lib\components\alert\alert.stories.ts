import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { AlertComponent } from './alert.component';
import { SeAlertModule } from './alert.module';
import { OrderedListData, SeAlertType } from './alert.model';
import { SeButton } from '../button';
import { onlyText } from '../tag/tag.stories';

const title = '<p>Lorem ipsum dolor sit amet</p>';
const subtitle =
  'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.';
const list = [
  'Lorem ipsum dolor sit amet, consectetur adipiscing elit',
  'Lorem ipsum dolor sit amet, consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore',
  'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
];

const orderedListData: OrderedListData = {
  orderedType: '1',
  items: [
    {
      text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit',
      subListOrderedType: 'letter-parenthesis',
      subList: list,
    },
    {
      text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit',
      subListOrderedType: 'number-parenthesis',
      subList: list,
    },
  ],
};

const collapseBtn: SeButton = {
  label: 'Veure avisos',
  alternateLabel: 'Amagar avís',
  btnTheme: 'trueOnlyText',
};

// More on how to set up stories at: https://storybook.js.org/docs/angular/writing-stories/introduction
const meta: Meta<AlertComponent> = {
  title: 'Components/Alert',
  component: AlertComponent,
  decorators: [
    moduleMetadata({
      imports: [SeAlertModule],
    }),
  ],
  tags: ['autodocs'],
  args: {
    type: SeAlertType.INFO,
    title,
    list,
    closeButton: true,
    titleClass: '',
  },
  argTypes: {
    type: {
      description: 'Changes the alert type.',
      options: ['info', 'error', 'warning', 'success', 'neutral'],
      control: { type: 'select' },
      table: {
        defaultValue: { summary: '-' },
      },
    },
    title: {
      description: 'Alert text',
      control: { type: 'text' },
      type: 'string',
      table: {
        defaultValue: { summary: '-' },
      },
    },
    titleClass: {
      description: 'Title class',
      control: { type: 'text' },
      type: 'string',
      table: {
        defaultValue: { summary: '-' },
      },
    },
    subtitle: {
      description: 'Alert subtitle text',
      control: { type: 'text' },
      type: 'string',
      table: {
        defaultValue: { summary: '-' },
      },
    },
    subtitleClass: {
      description: 'Subtitle class',
      control: { type: 'text' },
      type: 'string',
      table: {
        defaultValue: { summary: '-' },
      },
    },
    list: {
      description: 'Messages list',
      table: {
        defaultValue: { summary: '[]' },
      },
    },
    orderedListData: {
      description: 'Messages ordered list',
      table: {
        defaultValue: { summary: '{}' },
      },
    },
    close: {
      action: 'close',
      description: 'Emitted when the close button is clicked.',
      table: {
        type: {
          summary: 'EventEmitter<Event>',
        },
      },
    },
    closeButton: {
      description: 'Hide alert close button',
      control: { type: 'boolean' },
      type: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
  },
  render: (args) => ({
    props: args,
    template: `
    <div style="max-width: 850px">
      <se-alert
        [title]="title"
        [type]="type"
        [list]="list"
        [closeButton]="closeButton"
        [titleClass]="titleClass"
        (close)="close($event)"
      >
        <p>This is ng-content</p>
      </se-alert>
    </div>
    `,
  }),
};

export default meta;
type Story = StoryObj<AlertComponent>;

export const Info: Story = { args: { type: 'info', title, list } };
export const Warning: Story = {
  args: { type: SeAlertType.WARNING, title, list: [] },
};
export const Error: Story = {
  args: { type: SeAlertType.ERROR, title, list: [] },
};
export const Success: Story = {
  args: { type: SeAlertType.SUCCESS, title, list: [] },
};
export const Neutral: Story = {
  args: { type: SeAlertType.NEUTRAL, title, list: [] },
};
export const HideCloseButton: Story = {
  args: { type: SeAlertType.SUCCESS, title, list: [], closeButton: false },
};
export const TitleClass: Story = { args: { titleClass: 'italic' } };

export const OnlyTitle: Story = {
  render: (args) => ({
    props: { ...args, icon: 'matAddCircleOutlineOutline' },
    template: `<div style="max-width: 850px">
                <se-alert
                  [title]="title"
                  [type]="type"
                  [list]="[]"
                  [closeButton]="closeButton"
                  [titleClass]="titleClass"
                  (close)="close($event)"
                >
                </se-alert>
              </div>`,
  }),
};

export const Subtitle: Story = {
  render: (args) => ({
    props: { ...args, subtitle, orderedListData },
    template: `<div style="max-width: 850px">
                <se-alert
                  [title]="title"
                  [type]="type"
                  [subtitle]="subtitle"
                  [closeButton]="closeButton"
                  [titleClass]="'bold'"
                  [subtitleClass]="'mt-2'"
                  (close)="close($event)"
                >
                </se-alert>
              </div>`,
  }),
};

export const OrderedList: Story = {
  render: (args) => ({
    props: { ...args, subtitle, orderedListData },
    template: `<div style="max-width: 850px">
                <se-alert
                  [title]="title"
                  [type]="type"
                  [subtitle]="subtitle"
                  [orderedListData]="orderedListData"
                  [closeButton]="closeButton"
                  [titleClass]="'bold'"
                  [subtitleClass]="'mt-2'"
                  (close)="close($event)"
                >
                </se-alert>
              </div>`,
  }),
};
export const CollapseButtonTemplate: Story = {
  render: (args) => ({
    props: {
      ...args,
      type: SeAlertType.NEUTRAL,
      subtitle,
      collapseButton: collapseBtn,
    },
    template: `<div style="max-width: 850px">
                <se-alert
                  [title]="title"
                  [type]="type"
                  [collapseButton]="collapseButton"
                  [closeButton]="false"
                  [showAlertIcon]="false"
                  contentClass="flex-column"
                  alertClass="padding-normal"
                  (close)="close($event)"
                >
                {{subtitle}}
                </se-alert>
              </div>`,
  }),
};

export const FilterButtonTemplate: Story = {
  render: (args) => ({
    props: {
      ...args,
      type: SeAlertType.INFO,
      title,
      filterButton: collapseBtn,
      list:['option 1', 'option 2', 'option 3', 'option 4'],
    },
    template: `<div style="max-width: 850px">
                <se-alert
                  [title]="title"
                  [type]="type"
                  [list]="list"
                  [filterButton]="filterButton"
                  [minFilteredListLength]="1"
                  [closeButton]="false"
                  contentClass="flex-column"
                  alertClass="padding-normal"
                  (close)="close($event)"
                >
                </se-alert>
              </div>`,
  }),
};
