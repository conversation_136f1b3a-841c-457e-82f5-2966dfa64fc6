import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ModalComponent } from './modal.component';
import { TranslateModule } from '@ngx-translate/core';
import { SeButtonModule } from '../button/button.module';
import { SeModalService } from './modal.service';
import { SeSharedModule } from '../../shared/shared.module';

@NgModule({
  declarations: [ModalComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule, 
    FormsModule, 
    SeSharedModule,
    TranslateModule.forChild(), 
    SeButtonModule
  ],
  exports: [ModalComponent],
  providers: [SeModalService]
})
export class SeModalModule { }
