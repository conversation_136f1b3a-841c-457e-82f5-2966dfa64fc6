import { Component, Input } from '@angular/core';

@Component({
  selector: 'se-badge',
  template: `
    <div class="badge-container">
      <div class="circle text-2xs" [ngStyle]="circleStyle">
        <ng-container *ngIf="textInsideCircle">{{ textInsideCircle }}</ng-container>
      </div>
      <ng-content></ng-content>
      <span *ngIf="label">{{ label }}</span>
    </div>
  `,
  styleUrls: ['./badge.component.scss'],
})
export class BadgeComponent {
  @Input() color?: string;
  @Input() textColor = 'white';
  @Input() textInsideCircle = '';
  @Input() label?: string;
  @Input() badgeTheme: 'info' | 'success' | 'warning' | 'error' = 'info';

  get circleStyle() {
    let backgroundColor = '';

    if (this.color) {
      backgroundColor = this.color;
    } else {
      switch (this.badgeTheme) {
        case 'info':
          backgroundColor = 'var(--color-blue-600)';
          break;
        case 'success':
          backgroundColor = 'var(--color-green-300)';
          break;
        case 'warning':
          backgroundColor = 'var(--color-orange-300)';
          break;
        case 'error':
          backgroundColor = 'var(--color-red-400)';
          break;
      }
    }

    return {
      'background-color': backgroundColor,
      color: this.textColor,
      'min-width': this.textInsideCircle ? '20px' : '8px',
      'min-height': this.textInsideCircle ? '20px' : '8px',
      width: 'auto',
      height: 'auto',
      padding: '2px 4px',
    };
  }
}
