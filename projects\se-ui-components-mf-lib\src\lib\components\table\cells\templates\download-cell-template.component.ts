import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { Observable, firstValueFrom } from 'rxjs';
import { merge } from 'rxjs/internal/observable/merge';
import { map } from 'rxjs/operators';
import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import { CellEventService } from '../cell-event.service';
import { CellComponent, CellConfig, CellEventTypes, FlattenedCell } from '../cells.model';
import { SeModalService } from '../../../modal/modal.service';

@Component({
  selector: 'download-cell',
  template: `
    <div class="d-flex justify-content-start justify-content-md-end gap-2">
      <button
        *ngIf="hasDownload"
        class="action-button"
        [title]="cellConfig['downloadTitle'] ?? 'UI_COMPONENTS.BUTTONS.DOWNLOAD' | translate"
        (click)="onDownload()">
        <ng-icon name="matSimCardDownloadOutline"></ng-icon>
      </button>
      <button
        class="action-button"
        [title]="cellConfig['deleteTitle'] ?? 'UI_COMPONENTS.BUTTONS.DELETE' | translate"
        (click)="onDelete()">
        <ng-icon name="matDeleteOutline"></ng-icon>
      </button>
    </div>
  `,
  styleUrls: ['../styles/actions-cell-template.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DownloadCellComponent implements CellComponent, OnInit {
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

  hasDownload: boolean = true;
  hasConfirmation: boolean = true;

  constructor(
    private cellEventService: CellEventService,
    private modalService: SeModalService,
  ) { }

  ngOnInit() {
    this.hasDownload = this.cellConfig['hasDownload'] ?? true;
    this.hasConfirmation = this.cellConfig['hasConfirmation'] ?? true;
  }

  async onDownload() {
    if (!this.cellConfig.downloadCallback)
      return this.throwEvent(CellEventTypes.DOWNLOAD_ROW, 'downloadCellComponent');
    await this.cellConfig.downloadCallback(this.row, this.cell, this.column);
  }

  async onDelete() {
    let confirmation = this.hasConfirmation ? await this.getConfirmation() : true;

    if (confirmation) await this.processDelete();
  }

  async processDelete(): Promise<void> {
    if (!this.cellConfig.deleteCallback) return this.throwEvent(CellEventTypes.DELETE_ROW, 'downloadCellComponent', { row: this.row }, true);
    const { delete: remove, apply } = await this.cellConfig.deleteCallback(this.row, this.cell, this.column);

    if (remove) {
      this.throwEvent(CellEventTypes.DELETE_ROW, 'downloadCellComponent', { row: this.row }, apply);
    }
  }

  private async getConfirmation(): Promise<boolean> {

    const modalRef = this.modalService.openModal({
      severity: 'error',
      hideIcon: false,
      closable: true,
      closableLabel: 'UI_COMPONENTS.BUTTONS.CONTINUE',
      secondaryButton: true,
      secondaryButtonLabel: 'UI_COMPONENTS.BUTTONS.CANCEL',
      title: 'UI_COMPONENTS.DELETE_MODAL.DELETE_DOCUMENT_TITLE',
      subtitle: 'UI_COMPONENTS.DELETE_MODAL.DELETE_DOCUMENT_SUBTITLE',
    });
    const confirmation$: Observable<boolean> = merge(
      modalRef?.componentInstance?.modalSecondaryButtonEvent.pipe(
        map(() => false)
      ),
      modalRef?.componentInstance?.modalOutputEvent.pipe(map(() => true))
    ) as Observable<boolean>;
    const response = await firstValueFrom(confirmation$);
    modalRef?.close();
    return response;
  }

  private throwEvent(
    type: CellEventTypes,
    cellName: string,
    data?: any,
    apply = false
  ) {
    this.cellEventService.emitEvent({
      type,
      cellName,
      cell: this.cell,
      data: data ? { ...data, apply } : { apply },
    });
  }
}
