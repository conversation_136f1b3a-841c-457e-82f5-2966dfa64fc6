import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { ConfirmationMessageComponent } from './confirmation-message.component';
import { SeConfirmationMessageModule } from './confirmation.message.module';

const meta: Meta<ConfirmationMessageComponent> = {
  title: 'Components/Confirmation Message',
  component: ConfirmationMessageComponent,
  decorators: [
    moduleMetadata({
      imports: [SeConfirmationMessageModule],
    }),
  ],
  args: {
    data: {
      severity: 'info',
      title: 'Card title',
      subtitle: 'Card subtitle',
      closable: true,
      closableFn: () => {},
      titleParams: {},
      subtitleParams: {},
      collapsible: true,
      collapsibleFn: true,
      trackingId: 'tracking-id',
      titleDescription: 'Title description',
    },
    wrapperClass: ''
  },
  /* argTypes: {
    label: {
      description: 'Label for the input field.',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'Input label' } },
    },
    disabled: {
      description: 'Determines if the input is disabled or not.',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    placeholder: {
      description: 'Placeholder text for the input field.',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'Type...' } },
    },
    type: {
      description: 'The type of the input.',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'text' } },
    },
    id: {
      description: 'Input id',
      control: { type: 'text' },
    },
  }, */
  tags: ['autodocs']
};

export default meta;
type Story = StoryObj<ConfirmationMessageComponent>;

export const DefaultInput: Story = {};
