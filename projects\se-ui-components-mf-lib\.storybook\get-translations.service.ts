import { HttpBackend, HttpRequest, HttpResponse } from "@angular/common/http";
import { Injectable, Inject, isDevMode  } from "@angular/core";
import { TranslateLoader } from "@ngx-translate/core";
import { Observable, catchError, filter, forkJoin, map, of } from "rxjs";
import { InjectionToken } from '@angular/core';

export const TRANSLATION_PATHS = new InjectionToken<string[]>('translationPaths');
const BASE_URL_DEV = 'https://dev.seu2.atc.intranet.gencat.cat';

@Injectable()
export class CustomTranslateLoader implements TranslateLoader {
  constructor(private httpBackend: HttpBackend, @Inject(TRANSLATION_PATHS) private paths: string[]) {}

  getTranslation(lang: string): Observable<any> {
    const requests = this.paths.map((path) => {
      const fullPath = (isDevMode() ? '' : BASE_URL_DEV) + `${path}/${lang}.json`;
      const cachedData = localStorage.getItem(`translation-${lang}-${fullPath}`);
      if (cachedData) {
        return of(JSON.parse(cachedData));
      }

      const req = new HttpRequest("GET", fullPath);
      return this.httpBackend.handle(req).pipe(
        filter((event): event is HttpResponse<any> => event instanceof HttpResponse),
        map((response: HttpResponse<any>) => {
          const data = response.body;
          localStorage.setItem(`translation-${lang}-${fullPath}`, JSON.stringify(data));
          return data;
        }),
        catchError((err) => of({}))
      );
    });

    return forkJoin(requests).pipe(
      map((responses) => Object.assign({}, ...responses)),
      catchError((err) => of({}))
    );
  }
}
