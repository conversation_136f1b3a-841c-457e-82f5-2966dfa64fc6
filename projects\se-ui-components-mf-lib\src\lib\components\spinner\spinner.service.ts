import {
  ApplicationRef,
  ComponentFactoryResolver,
  ComponentRef,
  EmbeddedViewRef,
  Injectable,
  Injector,
} from '@angular/core';
import { SpinnerComponent } from './spinner.component';

@Injectable({
  providedIn: 'root',
})
export class SeSpinnerService {
  componentRef!: ComponentRef<SpinnerComponent>;
  
  private _isSpinnerManualStop: boolean = false;
  
  constructor(
    // Migrate component factory resolver
    // Update your code to use ViewContainerRef.createComponent without the factory resolver.
    // ComponentFactoryResolver has been removed from Router APIs.
    private componentFactoryResolver: ComponentFactoryResolver,
    private appRef: ApplicationRef,
    private injector: Injector
  ) {}

  start(customMessage?: string, customSubtitle?: string): void {
    // Check if there is already a spinner started
    if (!this.isStarted()) {
      // Create a component reference from the component
      this.componentRef = this.componentFactoryResolver
        .resolveComponentFactory(SpinnerComponent)
        .create(this.injector);

      if (this.componentRef?.hostView) {
        // Attach component to the appRef so that it's inside the ng component tree
        this.appRef.attachView(this.componentRef.hostView);

        // Get DOM element from component
        const domElem = (this.componentRef.hostView as EmbeddedViewRef<any>).rootNodes[0] as HTMLElement;
        domElem.id = 'se_spinner';
        // Append DOM element to the body
        document.body?.appendChild(domElem);

        // Set component variables values
        // this.componentRef.instance.blocked = true;
        this.componentRef.instance.message = customMessage ?? '';
        this.componentRef.instance.subtitle = customSubtitle ?? '';
      }
    }
  }

  stop(): void {
    if (this.componentRef) {
      this.appRef.detachView(this.componentRef.hostView);
      this.componentRef.destroy();
      // Forzado eliminar spinner para eliminar problemas de scope
      if(document.getElementById('se_spinner')){
        document.getElementById('se_spinner')?.remove()
      }
    }
  }

  /**
   * Check spinner
   * @description Check if the loading spinner is already present in the html body,
   * so that we do not load multiple spinners at the same time
   */
  isStarted(): boolean {
    return !!document.getElementById('se_spinner');
  }
  
  stopAndDisableManualStopMode(){
    this.disableSpinnerManualStopMode();
    this.stop();
  }

  enableSpinnerManualStopMode() {
    this._isSpinnerManualStop = true;
  }

  disableSpinnerManualStopMode() {
    this._isSpinnerManualStop = false;
  }

  isSpinnerManualStopEnable(): boolean {
    return this._isSpinnerManualStop;
  }
}
