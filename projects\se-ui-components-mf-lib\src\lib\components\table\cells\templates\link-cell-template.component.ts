// cell-template.component.ts
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import { CellComponent, CellConfig, CellEventTypes, FlattenedCell, LinkCellConfig } from '../cells.model';
import { CellEventService } from '../cell-event.service';

@Component({
  selector: 'se-link-cell',
  template: `
    <div [ngClass]="{ 'text-ellipsis': cellConfig.ellipsis, 'text-nowrap': cellConfig.nowrap }" >
        <se-link
            [href]="link.linkConfig.href ?? ''"
            [iconName]="link.linkConfig.iconName ?? ''"
            [disabled]="link.linkConfig.disabled ?? false"
            [iconPosition]="link.linkConfig.iconPosition ?? 'left'"
            [target]="link.linkConfig.target ?? '_blank'"
            [size]="link.linkConfig.size ?? 'regular'"
            [linkTheme]="link.linkConfig.linkTheme ?? 'secondary'"
            [ariaLabel]="link.linkConfig.ariaLabel ?? ''"
            (onClick)="onLinkClick()"
        > {{link.linkConfig.label ?? '' | translate}}</se-link>
    </div>
  `,
  styles: [`
    :host ::ng-deep .text-ellipsis se-link > a {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: block;
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LinkCellComponent implements CellComponent {

  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

    constructor(private cellEventService: CellEventService) {
     //empty constructor
    }

    get link(): LinkCellConfig {
      return this.cellConfig.linkCell!;
    }

    async onLinkClick() {
        if (!this.link.linkCallback) return this.throwEvent(CellEventTypes.LINK_ROW, 'linkCellComponent');
        const { data } = await this.link.linkCallback(this.row, this.cell, this.column);

        if (data) {
          this.throwEvent(CellEventTypes.LINK_ROW, 'linkCellComponent', { newData: data, rowId: this.row.id });
        }
    }

    private throwEvent(type: CellEventTypes, cellName: string, data?: any, apply = false) {
        this.cellEventService.emitEvent({
          type,
          cellName,
          cell: this.cell,
          data: data ? { ...data, apply } : { apply }
        })
      }

}
