import { Injectable } from '@angular/core';
import { FlattenedColumn } from './columns/column.model';
import { TableSortOrder } from './table.model';
import { FlattenedGroupRow, FlattenedRow } from './rows/rows.model';

@Injectable({
  providedIn: 'root',
})
export class TableService {
  currentSortColumn: FlattenedColumn | null = null;
  sortOrder: TableSortOrder = TableSortOrder.ASC;

  constructor() {}

  sortRows(
    column: FlattenedColumn,
    allFlattenedData: FlattenedRow[]
  ): FlattenedRow[] {
    if (this.currentSortColumn === column) {
      this.sortOrder =
        this.sortOrder === TableSortOrder.ASC
          ? TableSortOrder.DESC
          : TableSortOrder.ASC;
    } else {
      this.setCurrentSortColumn(column);
      this.sortOrder = TableSortOrder.ASC;
    }

    const regularRows = allFlattenedData.filter((row) => !this.isGroupRow(row));
    const groupRows = allFlattenedData.filter((row) => this.isGroupRow(row));

    regularRows.sort((a: FlattenedRow, b: FlattenedRow) => {
      const aValue: any = this.getCellValue(a, column);
      const bValue: any = this.getCellValue(b, column);
      const compareResult = this.compareValues(aValue, bValue);
      return this.sortOrder === TableSortOrder.ASC
        ? compareResult
        : -compareResult;
    });

    groupRows.forEach((row) => {
      if (this.isGroupRow(row)) {
        (row as FlattenedGroupRow).children?.sort(
          (a: FlattenedRow, b: FlattenedRow) => {
            const aValue: any = this.getCellValue(a, column);
            const bValue: any = this.getCellValue(b, column);
            const compareResult = this.compareValues(aValue, bValue);
            return this.sortOrder === TableSortOrder.ASC
              ? compareResult
              : -compareResult;
          }
        );
      }
    });

    return [...regularRows, ...groupRows];
  }

  isGroupRow = (row: FlattenedRow): row is FlattenedGroupRow =>
    (row as FlattenedGroupRow).key !== undefined;

  getCellValue(row: FlattenedRow, column: FlattenedColumn): any {
    return row.cells.find((cell) => cell.column.key === column.key)?.value;
  }

  private compareValues(aValue: any, bValue: any): number {
    const a = this.isDate(aValue) ? new Date(aValue) : aValue;
    const b = this.isDate(bValue) ? new Date(bValue) : bValue;

    if (a < b) return -1;
    if (a > b) return 1;
    return 0;
  }

  isDate(value: any): boolean {
    return !isNaN(Date.parse(value));
  }

  setCurrentSortColumn(column: FlattenedColumn): void {
    this.currentSortColumn = column;
  }
}
