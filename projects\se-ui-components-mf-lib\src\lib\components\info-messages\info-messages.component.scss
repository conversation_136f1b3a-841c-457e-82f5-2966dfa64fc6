@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins/breakpoints';

.info-message {

  border-radius: 8px;

  &__primary {
    border: solid 3px var(--color-blue-300);
  }

  &__primary_no_bullets {
    border: solid 3px var(--color-blue-300);
    .info-message__list {
      padding-left: 0;
      padding-top: 8px;
      .text-list {
        list-style-type: none;
      }
    }
  }

  &__secondary {
    border: solid 3px var(--color-pink-200);
  }

  &__secondary_no_bullets {
    border: solid 3px var(--color-pink-200);
    .info-message__list {
      padding-left: 0;
      padding-top: 8px;
      .text-list {
        list-style-type: none;
      }
    }
  }

  .text-list {
    line-height: var(--line-base);
    size: 16px;
  }

  svg,
  ng-icon {
    position: absolute;
  }

  span {
    white-space: pre-line;
  }

  .title {
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    line-height: var(--line-base);
  }

  .subtitle {
    margin-top: 8px;
    font-size: var(--text-base);
    color: var(--color-gray-700);
  }

  &__left-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .calendar-icon {
      color: var(--color-blue-600);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      &.img {
        width: 80px;
        height: 40px;
      }
    }
  }

  @include media-breakpoint-down(md) {
    &__left-content {
      display: none;
    }
  }

  &__content {
    justify-content: center;
    display: flex;
    flex-direction: column;
  }

  &__list {
    list-style-type: disc;
  }

  &__img {
    max-width: 100px;
  }
}
