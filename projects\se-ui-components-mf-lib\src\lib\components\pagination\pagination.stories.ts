import type {<PERSON><PERSON>, StoryObj} from '@storybook/angular';
import {applicationConfig, moduleMetadata} from "@storybook/angular";
import { PaginationComponent } from './pagination.component';
import { SePaginationModule } from './pagination.module';
import { provideAnimations } from '@angular/platform-browser/animations';
import { SeButton, SeButtonThemeEnum, SeButtonSizeEnum } from '../button';

const downloadButton: SeButton = {
  btnTheme: SeButtonThemeEnum.SECONDARY,
  disabled: false,
  icon: 'matFileDownloadOutline',
  iconSize: '20px',
  size: SeButtonSizeEnum.SMALL,
};

// More on how to set up stories at: https://storybook.js.org/docs/angular/writing-stories/introduction
const meta: Meta<PaginationComponent> = {
  title: 'Components/Pagination',
  component: PaginationComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [SePaginationModule],
    }),
    applicationConfig({
      providers: [provideAnimations()],
    }),
  ],
  args: {
    first: 0,
    rows: 10,
    totalRecords: 120,
    rowsPerPageOptions: [10, 20, 30],
    innerText: 'Pàgina',
    prevButtonLabel: 'Anterior',
    nextButtonLabel: 'Pròxim',
    showNumberReportPaginator: true,
    downloadButton,
  },
  argTypes: {
    first: {
      description: 'Page index',
      type: 'number',
      table: {
        defaultValue: {summary: 0},
      },
    },
    rows: {
      description: 'Number of elements to show in a page',
      type: 'number',
      table: {
        defaultValue: {summary: 10},
      },
    },
    rowsPerPageOptions: {
      description: 'Dropdown values to change the number of elements to show in a page',
      table: {
        defaultValue: {summary: [10, 20, 30]},
      },
    },
    innerText: {
      description: 'Text to show on the left',
      type: 'string',
      table: {
        defaultValue: { summary: '' },
      },
    },
    selectedText: {
      description: 'Text to show on the left',
      type: 'string',
      table: {
        defaultValue: { summary: '' },
      },
    },
    prevButtonLabel: {
      description: 'Text to show on the previous button if showNumberReportPaginator = false',
      type: 'string',
      table: {
        defaultValue: {summary: ''},
      },
    },
    nextButtonLabel: {
      description: 'Text to show on the next button if showNumberReportPaginator = false',
      type: 'string',
      table: {
        defaultValue: {summary: ''},
      },
    },
    showNumberReportPaginator: {
      description: 'If its value is true the paginator will have a list of numbers, one per page. If its value is false it will show only two buttons to change pages.',
      type: 'boolean',
      table: {
        defaultValue: {summary: 'true'},
      },
    },
    showRowsPerPage: {
      description: 'If its value is true the paginator will have a dropdown to change the number of elements to show in a page.',
      type: 'boolean',
      table: {
        defaultValue: {summary: 'false'},
      },
    },
  },
  render: (args) => ({
    props: {
      ...args,
      onPageChange: ($event: any) => console.log($event)
    },
    template: `
    <se-pagination
      (onPageChangeEvent)="onPageChange($event)"
      [first]="first"
      [rows]="rows"
      [totalRecords]="totalRecords"
      [rowsPerPageOptions]="rowsPerPageOptions"
      [showRowsPerPage]="showRowsPerPage"
      [innerText]="innerText"
      [selectedText]="selectedText"
      [showNumberReportPaginator]="showNumberReportPaginator"
      [prevButtonLabel]="prevButtonLabel"
      [nextButtonLabel]="nextButtonLabel"
      [downloadButton]="downloadButton">
    </se-pagination>
    `
  }),
};

export default meta;
type Story = StoryObj<PaginationComponent>;

export const numberReportPaginator: Story = {args: {
  first: 0,
  rows: 10,
  totalRecords: 120,
  rowsPerPageOptions: [10, 20, 30],
  innerText: 'Pàgina',
  showNumberReportPaginator: true
}};

export const buttonPaginator: Story = {args: {
  first: 0,
  rows: 10,
  totalRecords: 120,
  rowsPerPageOptions: [10, 20, 30],
  innerText: 'Pàgina',
  prevButtonLabel: 'Anterior',
  nextButtonLabel: 'Pròxim',
  showNumberReportPaginator: false
}};

export const paginatorWithRowsPerPage: Story = {
  args: {
    first: 0,
    rows: 3,
    showRowsPerPage: true,
    selectedText: 'Numero de elementos seleccionados 1 de 10',
    rowsPerPageOptions: [3, 5, 10, 15, 20, 50],
    showNumberReportPaginator: true,
  },
};
