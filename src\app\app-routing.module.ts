import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AppRoutes } from '@core/models/app-routes.enum';

const routes: Routes = [
  {
    path: '',
    redirectTo: AppRoutes.PARTICIPANTS,
    pathMatch: 'full',
  },
  {
    path: AppRoutes.ROOT,
    redirectTo: AppRoutes.PARTICIPANTS,
    pathMatch: 'full',
  },
  {
    path: AppRoutes.PARTICIPANTS,
    loadChildren: () =>
      import(`./modules/participants/participants.module`).then(
        (module) => module.ParticipantsModule,
      ),
  },
  {
    path: AppRoutes.YEAR_DECLARATION,
    loadChildren: () =>
      import(`./modules/tax-year-declaration/tax-year-declaration.module`).then(
        (module) => module.TaxYearDeclarationModule,
      ),
  },
  {
    path: AppRoutes.SUMMARY,
    loadChildren: () =>
      import(`./modules/summary/summary.module`).then(
        (module) => module.SummaryModule,
      ),
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
