import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { SeSharedModule } from '../../shared/shared.module';
import { TagComponent } from './tag.component';

@NgModule({
  imports: [SeSharedModule, CommonModule, TranslateModule],
  declarations: [TagComponent],
  exports: [TagComponent],
})
export class SeTagModule {}
