import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SeButton } from '../button';

@Component({
  selector: 'se-empty-state',
  template: `
    <div
      class="se-empty-state"
      [ngClass]="['se-empty-state--' + backgroundTheme]"
    >
      <svg-empty-document *ngIf="icon === 'document'"></svg-empty-document>
      <svg-empty-search *ngIf="icon === 'search'"></svg-empty-search>
      <svg-empty-info *ngIf="icon === 'info'"></svg-empty-info>
      <div class="se-empty-state__container">
        <span
          class="se-empty-state__label text-sm"
          [ngClass]="{ 'with-small-message': !!subMessage }"
          [innerHTML]="message | translate | safeHtml"
        >
        </span>
        <small
          *ngIf="subMessage"
          class="se-empty-state__small text-xs"
          [innerHTML]="subMessage | translate | safeHtml"
        >
        </small>
      </div>
      <se-button
        *ngIf="actionButton"
        (onClick)="onClickButton()"
        [btnTheme]="actionButton.btnTheme ?? 'secondary'"
        [disabled]="!!actionButton.disabled"
        [size]="actionButton.size ?? 'small'"
        [icon]="actionButton.icon"
        [iconPosition]="actionButton.iconPosition ?? 'left'"
        [iconSize]="actionButton.iconSize ?? '20px'"
        [tooltipText]="actionButton.tooltipText"
        [tooltipPosition]="actionButton.tooltipPosition"
      >
        {{ (actionButton.label ?? '') | translate }}
      </se-button>
    </div>
  `,
  styleUrls: ['./empty-state.component.scss'],
})
export class EmptyStateComponent {
  @Input() backgroundTheme: 'primary' | 'default' = 'default';
  @Input() icon: 'document' | 'search' | 'info' | undefined;
  @Input() message = 'SE_COMPONENTS.TABLE.NO_DATA';
  @Input() subMessage: string = '';
  @Input() actionButton: SeButton | undefined;

  @Output() actionButtonEvent: EventEmitter<void> = new EventEmitter<void>();

  protected onClickButton = (): void => this.actionButtonEvent.emit();
}
