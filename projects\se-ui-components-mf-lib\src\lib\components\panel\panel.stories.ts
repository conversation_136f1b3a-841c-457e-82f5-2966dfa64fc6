import type { <PERSON><PERSON>, StoryObj } from '@storybook/angular';
import { applicationConfig, moduleMetadata } from '@storybook/angular';
import { provideAnimations } from '@angular/platform-browser/animations';

import { PanelComponent } from './panel.component';
import { SePanelModule } from './panel.module';
import { SeButtonModule } from '../button/button.module';
import { SeTableModule } from '../table/table.module';

// More on how to set up stories at: https://storybook.js.org/docs/angular/writing-stories/introduction
const meta: Meta<PanelComponent> = {
  title: 'Components/Panel',
  component: PanelComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [SePanelModule, SeButtonModule, SeTableModule],
    }),
    applicationConfig({
      providers: [provideAnimations()],
    }),
  ],
  args: {
    title: 'Panel Title',
    subtitle: 'Subtitle',
    panelTheme: 'default',
    colapsible: false,
    collapsed: false,
    actionButton: { label: 'Afegir' },
    id: undefined,
  },
  argTypes: {
    id: {
      description: 'Custom id which solves accessible issues',
      control: { type: 'text' },
      type: 'string',
      table: {
        defaultValue: { summary: undefined },
      },
    },
  },
  render: (args) => ({
    props: args,
    template: `
    <se-panel
      [id]="id"
      [title]="title"
      [subtitle]="subtitle"
      [colapsible]="colapsible"
      [collapsed]="collapsed"
      [panelTheme]="panelTheme"
      [actionButton]="actionButton"
      [tooltip]="tooltip"
      [tooltipText]="tooltipText"
    >
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
        consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
      </p>
    </se-panel>
    `,
  }),
};

export default meta;
type Story = StoryObj<PanelComponent>;

export const Default: Story = {
  args: {
    title: 'Panel Title',
    colapsible: false,
    collapsed: false,
    actionButton: { label: 'Afegir' },
  },
};



export const Primary: Story = {
  args: {
    title: 'Panel Title',
    colapsible: false,
    collapsed: false,
    actionButton: { label: 'Afegir' },
    panelTheme: 'primary'
  },
};

export const PrimaryWithToolTip: Story = {
  args: {
    title: 'Panel Title',
    colapsible: false,
    collapsed: false,
    actionButton: { label: 'Afegir' },
    tooltip: true,
    tooltipText: 'Esto es un Tooltip',
    panelTheme: 'primary'
  },
};

export const PrimaryWithTooltipOnButton: Story = {
  args: {
    title: 'Panel Title',
    panelTheme: 'primary',
    colapsible: false,
    collapsed: false,
    actionButton: {
      tooltipText: 'Tooltip action button',
      tooltipPosition: 'top',
      label: 'hola',
    },
  },
};

export const Secondary: Story = {
  args: {
    title: 'Panel Title',
    panelTheme: 'secondary',
    colapsible: false,
    collapsed: false,
  },
};

export const Empty: Story = {
  args: {
    title: 'Panel Title',
    colapsible: false,
    collapsed: false,
    actionButton: { label: 'Afegir' },
    empty: true,
  },
  render: (args) => ({
    props: {
      ...args
    },
    template: `
    <se-panel
      [id]="id"
      [title]="title"
      [subtitle]="subtitle"
      [empty]="empty"
      [panelTheme]="panelTheme"
      [actionButton]="actionButton"
      [tooltip]="tooltip"
      [tooltipText]="tooltipText"
    >
    </se-panel>
    `
  })
};

export const Colapsible: Story = {
  args: {
    title: 'Panel Title',
    colapsible: true,
    collapsed: false,
    id: 'collapsible',
  },
};

export const ColapsibleCollapsed: Story = {
  args: {
    title: 'Panel Title',
    colapsible: true,
    collapsed: true,
    id: 'collapsed',
  },
};

export const CustomButtons: Story = {
  args: {
    title: 'Panel Title',
    colapsible: false,
    collapsed: false,
    actionButton: { label: 'Afegir' },
  },
  render: (args) => ({
    props: {
      ...args,
      onCustomClick: () => console.log('Secondary Button'),
    },
    template: `
    <se-panel
      [title]="title"
      [colapsible]="colapsible"
      [panelTheme]="panelTheme"
      [actionButton]="actionButton"
      [customActions]="customButton"
    >
    <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
        consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
      </p>
    </se-panel>

    <ng-template #customButton>
      <se-button
        class="button-align"
        [btnTheme]="'secondary'"
        (onClick)="onCustomClick()"
        [size]="'small'"
        > Secondary Button
      </se-button>
    </ng-template>
    `,
  }),
};

export const NoPadding: Story = {
  args: {
    title: 'Panel Title',
    colapsible: false,
  },
  render: (args) => ({
    props: {
      ...args,
      title: 'No padding',
      tableColumns: [
        {
          header: 'Name',
          key: 'name',
          resizable: true,
          size: 10,
        },
        {
          header: 'Age',
          key: 'age',
          tooltip: true,
          tooltipText: 'Lorem Itsum',
          resizable: true,
          size: 10,
        },
      ],
      tableRows: [
        {
          data: {
            name: { value: 'name' },
            age: { value: 'age' },
          },
        },
        {
          data: {
            name: { value: 'name' },
            age: { value: 'age' },
          },
        },
      ],
    },
    template: `
    <se-panel
      class="panel-0-padding"
      [title]="title"
      [colapsible]="colapsible"
      [panelTheme]="panelTheme"
    >
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
        consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
      </p>
      <se-table [columns]="tableColumns" [data]="tableRows">
      </se-table>
    </se-panel>
    `,
  }),
};
