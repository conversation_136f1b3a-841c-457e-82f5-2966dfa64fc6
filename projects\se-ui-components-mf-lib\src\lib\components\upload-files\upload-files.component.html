<div class="se-upload-files">
  <div class="se-upload-files__alerts mb-4" *ngIf="info || listErrors?.length">
    <se-alert
      *ngIf="info"
      [title]="info"
      [type]="'info'"
      [closeButton]="true"
      (close)="closeInfo()"
    >
    </se-alert>
    <se-alert
      *ngIf="listErrors?.length"
      [title]="'SE_COMPONENTS.FILE_UPLOADER.ERRORS.TITLE' | translate"
      [titleClass]="'bold'"
      [list]="listErrors"
      [type]="'error'"
      [closeButton]="true"
      (close)="closeErrors()"
    >
    </se-alert>
  </div>

  <div class="drop-area__container">
    <div class="drop-area__header" *ngIf="title">
      <p class="text-sm semi-bold font-primary">
        {{ title | translate }}
        <span class="italic">
          {{ labelRequired | translate }}
        </span>
      </p>
    </div>
    <div
      #se_upload_drop_area
      class="drop-area drop-area--border"
      [id]="'se_upload_drop_area'"
      [ngClass]="{ 
        'd-none': !isDropAreaVisible,
        'disabled': disabled,
      }"
    >
      <form class="se-upload-files__form">
        <input
          #se_upload_file_input
          [id]="'se_upload_file_input'"
          type="file"
          [disabled]="disabled"
          class="se-upload-files__inputfile d-none"
          [accept]="allowedFileExtensions"
          [multiple]="multiple"
          (change)="handleSelection($event)"
        />
        <div *ngIf="!isHoverActive">
          <div class="d-flex justify-content-center d-md-none mb-2">
            <se-button
              [size]="'small'"
              [btnTheme]="'secondary'"
              [type]="'button'"
              (onClick)="onClick($event)"
            >
              {{ "SE_COMPONENTS.FILE_UPLOADER.XS_UPLOAD_BUTTON" | translate }}
            </se-button>
          </div>
          <p
            class="m-0 text-sm semi-bold font-primary flex-row justify-content-center d-none d-md-flex"
          >
            <ng-icon class="blue-icon" name="matAttachFileOutline"></ng-icon>
            <span
              [ngClass]="{ 
                'link-text-disabled': disabled,
              }"
            >
              {{
                dropAreaTitlePreLinkText ??
                  "SE_COMPONENTS.FILE_UPLOADER.ARROSSEGUEU" | translate
              }}
            </span>
            <se-link
              class="clickable ml-1 mr-1"
              size="semibold"
              [disabled]="disabled"
              (onClick)="onClick($event)"
            >
              {{
                dropAreaTitleLinkText ?? "SE_COMPONENTS.FILE_UPLOADER.CLICK"
                  | translate
              }}
            </se-link>
            <span
              [ngClass]="{ 
                'link-text-disabled': disabled,
              }"
            >
              {{
                dropAreaTitlePostLinkText ??
                  "SE_COMPONENTS.FILE_UPLOADER.CARGAR" | translate
              }}
            </span>
          </p>
          <p
            *ngIf="subtitle"
            class="m-0 subtitle text-2xs semi-bold font-primary text-center"
          >
            {{
              subtitleText ?? "SE_COMPONENTS.FILE_UPLOADER.SUBTITLE"
                | translate : { format, groupSize, fileSize, maxFiles }
            }}
          </p>
        </div>
        <div *ngIf="isHoverActive">
          <p class="m-0 text-sm blue semi-bold font-primary">
            {{ "SE_COMPONENTS.FILE_UPLOADER.DRAG_TITLE" | translate }}
          </p>
        </div>
      </form>
    </div>
  </div>
  <div
    class="se-upload-files__table"
    *ngIf="filesCount"
    [ngClass]="{ 'mt-4': isDropAreaVisible }"
  >
    <se-panel
      *ngIf="!onlyTable; else tableFiles"
      class="panel-0-padding"
      [title]="'SE_COMPONENTS.FILE_UPLOADER.DOCUMENTATION' | translate"
      [panelTheme]="'secondary'"
    >
      <ng-container *ngTemplateOutlet="tableFiles"></ng-container>
    </se-panel>
  </div>
</div>
<ng-template #tableFiles>
  <se-table
    [columns]="tableColumns"
    [data]="tableRows"
    [customTrackById]="customTrackById"
    [reloadTable$]="reloadTable$"
    (onCellEvent)="tableChanged($event)"
  >
  </se-table>
</ng-template>
