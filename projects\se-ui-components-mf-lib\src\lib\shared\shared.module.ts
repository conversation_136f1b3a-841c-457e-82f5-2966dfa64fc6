import { NgModule } from '@angular/core';
import { NgIconsModule } from '@ng-icons/core';
import { TooltipModule } from 'primeng/tooltip';
import { NG_ICONS } from './shared.model';
import { SeTooltipAccessibleModule } from '../directives';
import { SafeHtmlPipe } from '../pipes';

@NgModule({
  imports: [
    NgIconsModule.withIcons(NG_ICONS),
    TooltipModule,
    SeTooltipAccessibleModule,
    SafeHtmlPipe,
  ],
  exports: [NgIconsModule, TooltipModule, SeTooltipAccessibleModule, SafeHtmlPipe],
})
export class SeSharedModule {}
