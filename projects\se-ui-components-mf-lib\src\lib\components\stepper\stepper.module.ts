import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { StepperComponent } from './stepper.component';
import { SeSharedModule } from '../../shared/shared.module';
import { RouterModule } from '@angular/router';
import { ProgressBarModule } from 'primeng/progressbar';
import { CardModule } from 'primeng/card';
import { SeAriaLabelModule } from '../../directives';

@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [StepperComponent],
  imports: [
    CommonModule,
    SeSharedModule,
    RouterModule,
    ProgressBarModule,
    CardModule,
    SeAriaLabelModule,
  ],
  exports: [StepperComponent],
})
export class SeStepperdModule {}
