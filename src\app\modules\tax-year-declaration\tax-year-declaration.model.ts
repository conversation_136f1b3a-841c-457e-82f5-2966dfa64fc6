export interface ContestableActDocument {
  type: string;
  subtype?: string;
  name?: string;
  description?: string;
  required?: boolean;
  allowedFiles?: string[];
  allowedSize?: number;
}

export interface DeclarationTypes {
  model: string;
  descripcio: string;
  codiImpost: string;
}

export interface Substitutiva {
  numJustificant: string;
  declarant: string;
  dataPresentacio: Date;
  estat: string;
}

export interface File {
  idPadoct: string;
  idDocument: string;
  nom: string;
  pes: number;
  descripcio: string;
  tipusDocument: string;
}

export interface PutTaxDeclarationData {
  model: string;
  exercici: string;
  substitutiva: Substitutiva;
  codiImpost: string;
  codiAsseguradora?: string;
  fitxer: File;
}

export type EstatFitxer = 
    | "NO_VALIDAT" 
    | "VALIDANT" 
    | "VALIDAT" 
    | "VALIDACIO_ERROR" 
    | "CANCELLAT";


export interface ValidationProgress {
  estat: EstatFitxer,
  percentatgeProgres: number
}
