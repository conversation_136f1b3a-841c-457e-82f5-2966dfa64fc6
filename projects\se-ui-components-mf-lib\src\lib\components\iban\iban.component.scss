::ng-deep {
  .se-iban {
    position: relative;
    margin-bottom: 1rem;
  
    .iban-element {
      position: relative;

      .iban-input {
        position: relative;

        .p-inputmask {
          font-family: var(--font-primary);
          font-size: var(--text-sm);
          line-height: var(--line-sm);
          color: var(--color-gray-700);
          display: flex;
          height: 40px;
          width: 100%;
          padding: 0px 8px;
          align-items: center;
          border-radius: 4px !important;
          border: 1px solid var(--color-gray-400);
          background: var(--color-white);
          outline: none;
        }

        &.disabled {
          .p-inputmask {
            cursor: not-allowed;
            background: var(--color-gray-200);
            text-decoration: none;
            opacity: 0.8;
          }
        }

        &.invalid {
          .p-inputmask {
            border-color: var(--color-red-400);
          }
        }

        &.valid {
          .p-inputmask {
            border-color: var(--color-green-300);
          }
        }

        &.readonly {
          .p-inputmask {
            border: none;
            background-color: transparent;
            font-size: 14px;
            opacity: 1 !important;
            padding: 0;
            height: auto;
          }
        }

        &:enabled:focus {
          box-shadow: none;
          border-color: var(--color-blue-500);

          &.invalid {
            .p-inputmask {
              border-color: var(--color-red-400);
            }
          }
          &.valid {
            .p-inputmask {
              border-color: var(--color-green-300);
            }
          }
        }
      }

      .input-check {
        color: var(--color-green-300);
        position: absolute;
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
        font-size: var(--text-xl);
        z-index: 1;
      }
    }
  }

  //FOR ALL
  .p-inputtext:enabled:focus {
    box-shadow: none;
    border-color: var(--color-blue-500);
  }
}
