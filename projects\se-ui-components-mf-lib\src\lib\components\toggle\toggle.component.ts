import { Component, EventEmitter, forwardRef, Input, Output } from '@angular/core';
import { NG_VALUE_ACCESSOR } from "@angular/forms";
import { SeToggleOption } from "./toggle.model";

@Component({
  selector: 'se-toggle',
  styleUrls: ['./toggle.component.scss'],
  template: `
    <div class="toggle-container">
      <div *ngFor="let option of optionsList" class="toggle-label"
             [ngClass]="[size, disabled ? 'disabled' : '']"
             [class.active]="option.active"
             (click)="handleClick(option)"
             (keydown.enter)="handleClick(option)"
             (keydown.space)="handleClick(option)">
          <p [ngClass]="option.active ? 'active' : 'inactive'">
            {{ option.title }}
          </p>
      </div>
    </div>
  `,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ToggleComponent),
      multi: true,
    },
  ],
})
export class ToggleComponent {
  @Input() optionsList: SeToggleOption[] = [];
  @Input() disabled: boolean = true;
  @Input() size: 'large' | 'default' | 'small' = 'default';

  @Output() onClick = new EventEmitter<void>();

  value!: string;

  handleClick(option: SeToggleOption) {
    if (!this.disabled){
      this.onClick.emit();
      this.value = option.id;
      this.activeToogle(this.value);
      this.onChange(this.value);
    }
  }

  activeToogle(id: string): void{
    this.optionsList.forEach(toggleOption => {
      toggleOption.active = toggleOption.id === id;
    });
  }

  onChange = (value: string): void => {}

  onTouched = (): void => {}

  writeValue(value: string): void {
    this.value = value;
    this.activeToogle(value);
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

}
