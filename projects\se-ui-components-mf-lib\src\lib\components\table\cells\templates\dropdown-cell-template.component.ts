import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import { CellEventService } from '../cell-event.service';
import {
  CellComponent,
  CellEventTypes,
  DropdownCellConfig,
  FlattenedCell,
} from '../cells.model';

@Component({
  selector: 'se-dropdown-cell',
  template: `
    <div class="se-dropdown-cell">
      <form [formGroup]="form">
        <se-dropdown
          formControlName="value"
          id="{{ id }}-dropdown-cell-value"
          [editable]="cellConfig.editable"
          [autoSelect]="cellConfig.autoSelect"
          [options]="cellConfig.options"
          [disabled]="cellConfig.disabled"
          [appendTo]="'body'"
          [scrollHeight]="'200px'"
          [readOnly]="cellConfig.readOnly"
          [showClear]="cellConfig.showClear"
          [optionLabel]="cellConfig.optionLabel"
          [optionValue]="cellConfig.optionValue"
          [filter]="cellConfig.filter"
          [tooltip]="cellConfig.tooltip ?? false"
          [tooltipText]="cellConfig.tooltipText ?? ''"
          (dropdownOutput)="onDropdownOutput($event)">
      </se-dropdown>
    </form>
  </div>`,
  styleUrls: ['../styles/dropdown-cell-template.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DropdownCellComponent implements CellComponent {
  @Input() value: any;
  @Input() id!: string;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: DropdownCellConfig;

  form!: FormGroup;

  constructor(
    private fb: FormBuilder,
    private cellEventService: CellEventService
  ) { }

  ngOnInit(): void {
    this.id = this.cellConfig['id'];
    this.form = this.fb.group({
      value: [this.value, this.cellConfig.validators ?? []],
    });
  }

  onDropdownOutput(value: any): void {
    let newData: { [key: string]: any } = {};
    newData[this.column.key] = value;
    this.throwEvent(CellEventTypes.EDIT_ROW, 'dropdownCellComponent', { newData, rowId: this.row.id });
  }

  private throwEvent(type: CellEventTypes, cellName: string, data?: any) {
    this.cellEventService.emitEvent({
      type,
      cellName,
      cell: this.cell,
      data: { ...data, apply: true }
    })
  }
}
