import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import { CellComponent, DropdownButtonCellConfig, FlattenedCell } from '../cells.model';
import { MenuItem } from 'primeng/api';
import { SeButton } from '../../../button';

@Component({
  selector: 'dropdown-button-cell',
  template: `
    <div class="button-row-container">
      <se-button-dropdown
        [items]="dropdownItems"
        [buttonOptions]="dropdownButtonOptions"
      >
        {{ dropdownButtonOptions.label || '' | translate }}
      </se-button-dropdown>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DropdownButtonCellComponent implements CellComponent {
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: DropdownButtonCellConfig;

  get dropdownItems(): MenuItem[] {
    return this.cellConfig.dropdownButtonCell?.items || [];
  }

  get dropdownButtonOptions(): SeButton {
    return this.cellConfig.dropdownButtonCell?.buttonOptions ?? { size: 'small', btnTheme: 'secondary' };
  }
}
