import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SeSharedModule } from '../../shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { PanelComponent } from './panel.component';
import { PanelModule } from 'primeng/panel';
import { SeButtonModule } from '../button/button.module';
import { SeAriaLabelModule, SeCustomIdModule } from '../../directives';

@NgModule({
  declarations: [PanelComponent],
  imports: [
    CommonModule,
    PanelModule,
    SeSharedModule,
    TranslateModule.forChild(),
    SeButtonModule,
    SeAriaLabelModule,
    SeCustomIdModule,
  ],
  exports: [PanelComponent],
})
export class SePanelModule {}
