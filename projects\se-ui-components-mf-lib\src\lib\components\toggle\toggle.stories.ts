import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import {ToggleComponent} from "./toggle.component";
import {SeToggleModule} from "./toggle.module";
import {FormControl, FormGroup, ReactiveFormsModule, Validators} from "@angular/forms";

const meta: Meta<ToggleComponent> = {
  title: 'Components/Toggle',
  component: ToggleComponent,
  decorators: [
    moduleMetadata({
      imports: [SeToggleModule, ReactiveFormsModule],
    }),
  ],
  tags: ['autodocs'],
  args: {
    size: 'default',
    disabled: true,
    optionsList: [
      { id: '1', title: 'Toggle 1', active: true },
      { id: '2', title: 'Toggle 2', active: false },
      { id: '3', title: 'Toggle 3', active: false }
    ],
  },
  argTypes: {
    size: {
      description: 'Changes the button size.',
      options: ['large', 'default', 'small'],
      control: { type: 'select' },
      table: {
        defaultValue: { summary: 'default' },
      },
    },
    disabled: {
      description: 'Disable and stop the click event propagation',
      type: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
  },
  render: (args) => ({
    props: {
      ...args,
      form: new FormGroup({
        value: new FormControl({ value: '2', disabled: args.disabled }),
      }),
    },
    template: `
      <form [formGroup]="form">
      <se-toggle
        formControlName="value"
        [size]="size"
       [optionsList]="optionsList">
      </se-toggle>
      </form>
    `,
  }),
};

export default meta;
type Story = StoryObj<ToggleComponent>;

export const Default: Story = {
  args: {
    disabled: false,
    size: 'default',
    optionsList: [
      { id: '1', title: 'Toggle 1', active: true },
      { id: '2', title: 'Toggle 2', active: false },
      { id: '3', title: 'Toggle 3', active: false }
    ],
  }
};
