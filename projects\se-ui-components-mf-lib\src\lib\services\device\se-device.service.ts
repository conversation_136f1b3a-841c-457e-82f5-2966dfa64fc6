import { Injectable } from '@angular/core';
import { DeviceDetectorService, DeviceInfo, OS } from 'ngx-device-detector';
import { fromEvent, map } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SeDeviceService {

  public device : DeviceInfo;

  constructor(private devSrv : DeviceDetectorService) {
    this.device = this.devSrv.getDeviceInfo();
   }

  /**
   * Check: mobile
   * @description Check if the device is a mobile
   * @returns {boolean} Returns true/false depending whether the device is a mobile o not.
   */
  isMobile: () => boolean = (): boolean => this.devSrv.isMobile()  || window.innerWidth < 768;


  /**
   * Observable that emits a boolean indicating whether the current device is considered a mobile device.
   *
   * This observable listens for the window 'resize' event using the fromEvent operator and maps each event
   * to the result of the isMobile() method. It allows components and services to subscribe and react to changes
   * in the device's display characteristics (e.g., adapting layouts for mobile environments).
   */
  isMobile$ = fromEvent(window, 'resize').pipe(map(() => this.isMobile()));

  /**
   * Check: tablet
   * @description Check if the device is a tablet
   * @returns {boolean} Returns true/false depending whether the device is a tablet o not.
   */
  isTablet: () => boolean = (): boolean => this.devSrv.isTablet() || window.innerWidth >= 768 && window.innerWidth < 992;

  /**
   * Observable that indicates if the current viewport qualifies as a tablet.
   *
   * This observable listens for window resize events and maps each event to the result of the
   * isTablet() method, which determines whether the device's screen dimensions fit the tablet criteria.
   */
  isTablet$ = fromEvent(window, 'resize').pipe(map(() => this.isTablet()));

  /**
   * Check: mobile or tablet
   * @description Check if the device is a mobile or tablet
   * @returns {boolean} Returns true/false depending whether the device is a mobile/tablet o not.
   */
  isMobileOrTablet: () => boolean = (): boolean => this.devSrv.isMobile() || this.devSrv.isTablet() || window.innerWidth < 992;

  /**
   * Observable that emits a boolean indicating whether the current device is considered a mobile or tablet.
   *
   * This observable listens for the window 'resize' event using the fromEvent operator and maps each event
   * to the result of the isMobileOrTablet() method. It allows components and services to subscribe and react
   * to changes in the device's display characteristics (e.g., adapting layouts for mobile/tablet environments).
   */
  isMobileOrTablet$ = fromEvent(window, 'resize').pipe(map(() => this.isMobileOrTablet()));

  /**
   * Check: desktop
   * @description Check if the device is a PC (desktop)
   * @returns {boolean} Returns true/false depending whether the device is a PC (desktop) o not.
   */
  isDesktop: () => boolean = (): boolean => this.devSrv.isDesktop() || window.innerWidth >= 992;

  /**
   * Observable that emits a boolean indicating whether the current device is considered a desktop.
   *
   * This observable listens for window resize events and maps each event to the result of the
   * isDesktop() method, which determines whether the device's screen dimensions fit the desktop criteria.
   */
  isDesktop$ = fromEvent(window, 'resize').pipe(map(() => this.isDesktop()));

  /**
   * Check: Android
   * @description Check if the device is an Android
   * @returns {boolean} Returns true/false depending whether the device is an Android o not.
   */
  isAndroid: () => boolean = (): boolean => this.device.os === OS.ANDROID;

  /**
   * Check: IOS
   * @description Check if the device is an IOS
   * @returns {boolean} Returns true/false depending whether the device is an IOS o not.
   */
  isIOS: () => boolean = (): boolean => this.device.os === OS.IOS;

    /**
   * Check: Edge
   * @description Check if the device is an IOS
   * @returns {boolean} Returns true/false depending whether the device is on Edge Explorer.
   */
    isEdge: () => boolean = (): boolean => /Edg/.test(navigator.userAgent);
}
