import { Type } from '@angular/core';
import { RowComponent } from './rows.model';
import { DefaultRowComponent } from './templates/default-row.component';
import { PanelRowComponent } from './templates/panel-row.component';

interface RowComponentRegistry {
  defaultRow: Type<RowComponent>;
  panelRow: Type<RowComponent>;
}

export const RowComponentMap: Record<string, Type<RowComponent>> = {
  defaultRow: DefaultRowComponent,
  panelRow: PanelRowComponent,
};

export type RowComponentKeys = keyof RowComponentRegistry;
