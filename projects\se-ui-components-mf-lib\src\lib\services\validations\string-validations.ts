import {
  AbstractControl,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';

/**
 * Validator: equals
 * @description Validates if a field value (validateField) is equal to another field value (originalField).
 * The validation is originally implemented at a FormGroup level, but this validation function changes it to a formControl level.
 * @param originalField formControlName of the original field
 * @param validateField formControlName of the field which value has to be checked
 * @returns {ValidatorFn} Return a validator function
 */
export const validatorEquals = (
  originalField: string,
  validateField: string,
  translateKey?: string
): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    const form = control as FormGroup;

    if (form.get(originalField) && form.get(validateField)) {
      // Get fields values
      const originalFieldValue = form.get(originalField)!.value;
      const validateFieldValue = form.get(validateField)!.value;

      if (originalFieldValue?.toString() === validateFieldValue?.toString()) {
        // The 2 fields have the same value -> remove error
        let currentErrors = form.get(validateField)!.errors;
        if (currentErrors) {
          delete currentErrors['equals'];
          if (Object.keys(currentErrors).length === 0) {
            currentErrors = null;
          }
        }
        form.get(validateField)!.setErrors(currentErrors);
      } else if (
        // One of the 2 fields have a value
        (originalFieldValue || validateFieldValue) &&
        // The 2 values are different
        originalFieldValue?.toString() !== validateFieldValue?.toString()
      ) {
        // The 2 fields have different values -> add error
        let currentErrors = form.get(validateField)!.errors;
        if (!currentErrors) {
          // If there are no validations errors -> Create and add the "equals" error
          currentErrors = {
            equals: translateKey || 'UI_COMPONENTS.VALIDATIONS_ERRORS.equals',
          };
        } else {
          // If there are other validations errors -> Remove only the "equals" error
          currentErrors['equals'] =
            translateKey || 'UI_COMPONENTS.VALIDATIONS_ERRORS.equals';
        }
        form.get(validateField)!.setErrors(currentErrors);
      }
    }
    return null;
  };
};

export const validatorNotEqualsValue = (value: string[]): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    if (control?.value === value) {
      return {
        notEqualsValue: {
          translateKey: 'UI_COMPONENTS.VALIDATIONS_ERRORS.notEqualsValue',
          translateParams: { notEqualsValue: value },
        },
      };
    }
    return null;
  };
};

export const validatorNotEqualsList = (values: string[], translateKey?: string): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    let same = values.some(value => value?.toLowerCase() === control?.value?.toString().toLowerCase())
    if (same) {
        return {
          notEqualsList: {
            translation: translateKey ?? 'UI_COMPONENTS.VALIDATIONS_ERRORS.notEqualsList',
          },
        };
      }
    return null;
  };
};


export const validatorReferenciaCatastral: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  if (c.value && c.value.toString().trim() !== '') {
    // Valor por el que se debe multiplicar cada posición de cada subcadena
    const pesoPosicion = [13, 15, 12, 5, 4, 17, 9, 21, 3, 7, 1];
    const letraDc = 'MQWERTYUIOPASDFGHJKLBZX';
    const referenciaCatastral = c.value.toUpperCase();

    // Sólo se comprueban las referencias catastrales con 20 carácteres alfanuméricos,
    // los dos últimos corresponden a los dígitos de control.
    if (!referenciaCatastral || referenciaCatastral.length !== 20) {
      return {
        pattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.referenciaCatastral',
      };
    }

    // Para calcular cada dígito de control se utilizan siguientes subcadenas
    // Local o piso
    const cadenaLocalPiso = referenciaCatastral.substring(14, 18);
    // Finca o parcela
    const cadenaPrimerDC =
      referenciaCatastral.substring(0, 7) + cadenaLocalPiso;
    // Hoja del plano
    const cadenaSegundoDC =
      referenciaCatastral.substring(7, 14) + cadenaLocalPiso;

    const cadenasDC = [cadenaPrimerDC, cadenaSegundoDC];
    let dcCalculado = '';

    cadenasDC.forEach((cadena) => {
      let sumaDigitos = 0;
      /*
				Para el cálculo de cada dígito de control, se deben de sumar cada
				uno de los carácteres de cada cadena.
				Si el carácter no es numérico el valor corresponde de la siguiente
				manera: A = 1, B = 2, ..., Z = 27.
			*/
      cadena.split('').forEach((caracter: any, posicion: any) => {
        let valorCaracter = caracter;

        if (caracter >= 'A' && caracter <= 'N') {
          valorCaracter = caracter.charCodeAt() - 64;
        } else if (caracter === 'Ñ') {
          valorCaracter = 15;
        } else if (caracter > 'N') {
          valorCaracter = caracter.charCodeAt() - 63;
        }

        sumaDigitos =
          (sumaDigitos + valorCaracter * pesoPosicion[posicion]) % 23;
      });

      //Valor del dígito de control calculado
      dcCalculado += letraDc.charAt(sumaDigitos);
    });

    if (dcCalculado === referenciaCatastral.substring(18, 20)) {
      return null;
    } else {
      return {
        pattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.referenciaCatastral',
      };
    }
  }

  return null;
};

export const validatorDigitLength = (lengthValue: number): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    if (String(control.value)?.length !== lengthValue) {
      return {
        digitLength: {
          translateKey: 'UI_COMPONENTS.VALIDATIONS_ERRORS.digitLength',
          translateParams: { digitLength: lengthValue },
        },
      };
    }
    return null;
  };
};

export const validatorDigitRange = (
  minLength: number,
  maxLength: number
): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    if (
      String(control.value)?.length < minLength ||
      String(control.value)?.length > maxLength
    ) {
      return {
        digitRange: {
          translateKey: 'UI_COMPONENTS.VALIDATIONS_ERRORS.digitRange',
          translateParams: {
            digitRangeMin: minLength,
            digitRangeMax: maxLength,
          },
        },
      };
    }
    return null;
  };
};

export const validatorSearchLength = (minLength: number): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    if (control.value?.length < minLength && control.value?.length > 0) {
      return {
        searchLength: {
          translateKey: 'UI_COMPONENTS.VALIDATIONS_ERRORS.searchLength',
          translateParams: { searchLength: minLength },
        },
      };
    }
    return null;
  };
};
