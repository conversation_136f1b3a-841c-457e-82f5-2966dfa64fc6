import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { SeSharedModule } from '../../../shared/shared.module';
import { SePanelModule } from '../../panel/panel.module';
import { CellsModule } from '../cells/cell.module';
import { RowTemplateDirective } from './row-template.directive';
import { DefaultRowComponent } from './templates/default-row.component';
import { PanelRowComponent } from './templates/panel-row.component';

@NgModule({
  imports: [
    CommonModule,
    CellsModule,
    SeSharedModule,
    SePanelModule,
    TranslateModule.forChild(),
  ],
  declarations: [DefaultRowComponent, PanelRowComponent, RowTemplateDirective],
  exports: [RowTemplateDirective],
})
export class RowsModule {}
