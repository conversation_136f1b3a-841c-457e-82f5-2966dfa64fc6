import { formatDate as ngFormatDate } from '@angular/common';
import { Inject, Injectable, LOCALE_ID } from '@angular/core';

import type { Nullable } from '../../models';
import { SortType } from './sort-type.enum';

/**
 * Servicio de utilidades para el formateo y parseo de fechas.
 */
@Injectable({
  providedIn: 'root',
})
export class DateUtilsService {
  constructor(@Inject(LOCALE_ID) private readonly locale: string) {
    // Intenciondamente vacío
  }

  /**
   * Formatea una fecha en formato ISO8601 básico. Según las opciones que se le
   * pasen al método, el resultado puede contener más o menos componentes de
   * información en la cadena resultante (ver ejemplos al final).
   *
   * @param value Una de estas opciones:
   *  - Fecha en formato cadena de texto.
   *  - Milisegundos transcurridos desde el 01/01/1970.
   *  - Un objeto {@linkcode window.Date} nativo.
   * @param options Opciones del formato de salida:
   *  - `includeTime`: si debe incluir la marca de tiempo en la cadena
   *  resultante o no. Por defecto: `false`.
   *  - `includeMilliseconds`: si debe incluir las fracciones de
   *  segundo (milisegundos) en la cadena resultante. Por defecto: `false`.
   *  - `includeTimezone`: si debe incluir la diferencia de tiempo de la
   *  zona horaria. Por defecto: `false`.
   *  - `locale`: el código de ubicación para usar las reglas de formato de
   *  dicha ubicación. Por defecto: el `LOCALE_ID` actual (`'ca'`).
   *  - `timezone`: La diferencia de tiempo de la zona horaria. Una diferencia
   *  de tiempo de GMT (p. Ej. `'+0430'`), o una abreviatura de zona horaria
   *  estándar UTC/GMT o US continental. Si no se especifica, usa los ajustes
   *  del sistema anfitrión.
   * @returns La fecha formateada en formato ISO8601 básico o `null` si
   *  `value` es _{@link https://mzl.la/3SdBwnW Falsy}_.
   *
   * ### Ejemplos:
   *
   * @example
   *  formatDateISO(null) // null
   *  formatDateISO(undefined) // null
   *  formatDateISO('') // null
   *
   *  formatDateISO(new Date()) // Ej. '2024-07-19'
   *  formatDateISO('2024-07-19T12:27:35.624+0200') // '2024-07-19'
   *  formatDateISO(
   *    '2024-07-19TT12:27:35.624',
   *    { includeTimezone:true }
   *  ) // '2024-07-19T12:27:35+0200'
   *
   *  formatDateISO(
   *    new Date(),
   *    { includeTimezone: true } // Por defecto usa el timezone del sistema anfitrión
   *  ) // Ej. '2024-07-19T12:27:35+0200'
   *
   *  // Aunque también se puede formatear la fecha con otra zona horaria:
   *  formatDateISO(
   *    new Date(),
   *    {
   *      timezone: '+0000', // Establecemos la zona horaria central (UTC)
   *      includeTimezone: true // Incluimos el timezone para ver la salida
   *    }
   *  ) // Ej. '2024-07-19T12:27:35+0000'
   *
   *  formatDateISO(
   *    new Date(),
   *    { includeTimezone: true, includeMilliseconds: true }
   *  ) // Ej. '2024-07-19T12:27:35.624+0200'
   *
   *  formatDateISO(
   *    new Date(),
   *    { includeMilliseconds: true }
   *  ) // Ej. '2024-07-19T12:27:35.624'
   *
   *  formatDateISO(
   *    new Date(),
   *    { includeTime: true }
   *  ) // Ej. '2024-07-19T12:27:35'
   *
   */
  formatDateISO(
    value: Nullable<string | number | Date>,
    options: {
      includeTime?: boolean;
      includeMilliseconds?: boolean;
      includeTimezone?: boolean;
      locale?: string;
      timezone?: string;
    } = {
      includeTime: false,
      includeMilliseconds: false,
      includeTimezone: false,
      locale: this.locale,
      timezone: undefined,
    }
  ): Nullable<string> {
    if (!value || (typeof value === 'string' && !value.trim())) {
      return null;
    }

    const {
      includeTime,
      includeMilliseconds,
      includeTimezone,
      locale,
      timezone,
    } = options;

    const dateFormat = 'yyyy-MM-dd';
    const timeFormat = 'THH:mm:ss';
    const millisFormat = '.SSS';
    const zoneFormat = 'Z';

    if (includeTimezone && includeMilliseconds) {
      const format = dateFormat + timeFormat + millisFormat + zoneFormat;
      return this.formatDate(value, format, locale, timezone);
    }

    if (includeTimezone) {
      const format = dateFormat + timeFormat + zoneFormat;
      return this.formatDate(value, format, locale, timezone);
    }

    if (includeMilliseconds) {
      const format = dateFormat + timeFormat + millisFormat;
      return this.formatDate(value, format, locale, timezone);
    }

    if (includeTime) {
      const format = dateFormat + timeFormat;
      return this.formatDate(value, format, locale, timezone);
    }

    return this.formatDate(value, dateFormat, locale, timezone);
  }

  /**
   *
   * @param value Una de estas opciones:
   *  - Fecha en formato cadena de texto.
   *  - Milisegundos transcurridos desde el 01/01/1970.
   *  - Un objeto {@linkcode window.Date} nativo.
   * @param format Las partes de la cadena de fecha-hora que incluir.
   *  Ver {@linkcode https://bit.ly/3LspDGS Angular - Datepipe} para los
   *  detalles.
   * @param locale El código de ubicación para usar las reglas de formato de
   *  dicha ubicación. Por defecto: el `LOCALE_ID` actual (`'ca'`).
   * @param timezone La zona horaria. Una diferencia de tiempo de
   *  GMT (p. Ej. `'+0430'`), o una abreviatura de zona horaria
   *  estándar UTC/GMT o US continental. Si no se especifica, usa los ajustes
   *  del sistema anfitrión.
   *
   * @returns La cadena de fecha formateada o `null` si `value`
   *  es _{@link https://mzl.la/3SdBwnW Falsy}_.
   */
  formatDate(
    value: Nullable<string | number | Date>,
    format: string,
    locale = this.locale,
    timezone?: string
  ): Nullable<string> {
    if (!value || (typeof value === 'string' && !value.trim())) {
      return null;
    }
    try {
      return ngFormatDate(value, format, locale, timezone);
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  /**
   * Compara el día, mes y año de dos fechas. Se le puede indicar si queremos
   * comparar también las horas, minutos, segundos y milisegundos. Por defecto,
   * comparar el tiempo está deshabilitado.
   *
   * @param dateA Fecha para comparar con `dateB`.
   * @param dateB Fecha para comprar con `dateA`.
   * @param options Opciones de comparación. Permite comparar también las
   * horas, minutos, segundos y milisegundos. Por defecto, comparar el tiempo
   * está deshabilitado.
   * @returns `true` si las fechas son iguales. `false` en caso contrario.
   */
  equalDates(
    dateA: Nullable<Date>,
    dateB: Nullable<Date>,
    options = { compareTime: false }
  ): boolean {
    if (!dateA && !dateB) return true;

    const isSameDate =
      dateA?.getFullYear() === dateB?.getFullYear() &&
      dateA?.getMonth() === dateB?.getMonth() &&
      dateA?.getDay() === dateB?.getDay();

    if (!options?.compareTime) return isSameDate;

    const isSameTime =
      dateA?.getHours() === dateB?.getHours() &&
      dateA?.getMinutes() === dateB?.getMinutes() &&
      dateA?.getSeconds() === dateB?.getSeconds() &&
      dateA?.getMilliseconds() === dateB?.getMilliseconds();

    return isSameDate && isSameTime;
  }

  /**
   * Convierte una cadena de fecha
   * en {@link https://mzl.la/3y59moi formato de cadena de fecha-hora} a un
   * objeto {@linkcode window.Date} nativo. A menos que se indique lo
   * contrario, la fecha resultante incluirá la diferencia horaria atual del
   * sistema.
   *
   * @param dateTimeString Cadena de fecha
   *  en {@link https://mzl.la/3y59moi formato de cadena de fecha-hora}.
   * @param options Opciones de conversión:
   *  - `includeSystemTimezoneOffset`: si debe añadir la diferencia horaria del
   *    sistema o no. Por defecto: `true`.
   * @returns La fecha que representa la cadena de entrada `dateTimeString`
   *  como un objeto nativo {@linkcode window.Date}.
   */
  parseDate(
    dateTimeString: Nullable<string>,
    options = { includeHostSystemTimezoneOffset: true }
  ): Nullable<Date> {
    if (!dateTimeString) return null;
    const timeRegexp = /T\d{2}(?::\d{2}){2}(?:\.000)?(?:Z|[+-]\d{2}:?\d{2})?$/;
    if (timeRegexp.test(dateTimeString)) return new Date(dateTimeString);
    return new Date(
      options?.includeHostSystemTimezoneOffset
        ? `${dateTimeString}T00:00:00`
        : dateTimeString
    );
  }

  sortDates(
    a: Date | string,
    b: Date | string,
    sortType: SortType = SortType.Descending
  ): number {
    const dateA = new Date(a);
    const dateB = new Date(b);

    return sortType === SortType.Descending
      ? dateB.getTime() - dateA.getTime()
      : dateA.getTime() - dateB.getTime();
  }

  /**
   * A partir de una fecha crea una nueva con la cantidad de meses hacia el
   * futuro o hacia el pasado que se indique mediante `monthsOffset`. Si la
   * fecha resultante al desplazar la cantidad de meses cae en un día que no
   * existe (un día bisiesto o el día 31 de un mes que solo tiene 30 días)
   * entonces el resultado será el último día del mes anterior.
   *
   * @param baseDate La fecha base a partir de la cual obtener la nueva.
   * @param monthsOffset La cantidad de meses a desplazar la fecha base. Valores positivos hacia el futuro. Negativos hacia el pasado.
   * @returns La fecha resultante de desplazar `monthsOffset` cantidad de meses desde `baseDate`.
   */
  crateDateWithMonthsOffset(
    baseDate: Nullable<number | string | Date>,
    monthsOffset: number
  ): Nullable<Date> {
    if (
      !baseDate ||
      !['number', 'string', 'object'].includes(typeof baseDate)
    ) {
      return null;
    }

    const baseDateObj = new Date(baseDate);
    if (!baseDateObj) return null;

    const resultDate = new Date(baseDateObj);
    resultDate.setMonth(resultDate.getMonth() + monthsOffset);

    const referenceDate = new Date(
      `${baseDateObj.getFullYear()}-${String(
        baseDateObj.getMonth() + 1
      ).padStart(2, '0')}-01`
    );
    referenceDate.setMonth(referenceDate.getMonth() + monthsOffset);
    const referenceMonth = referenceDate.getMonth();

    if (resultDate.getMonth() > referenceMonth) {
      resultDate.setDate(0);
    }

    return resultDate;
  }

  /**
   * Ajusta la fecha al siguiente lunes si cae en fin de semana.
   *
   * Si la fecha cae en sábado, se ajusta al lunes sumando 2 días.
   * Si la fecha cae en domingo, se ajusta al lunes sumando 1 día.
   *
   * @param date - La fecha a ajustar. Si es `null` o `undefined`, no se realiza
   * ninguna acción.
   */
  setToMondayIfWeekend(date: Nullable<Date>): void {
    if (!date) return;
    const dayOfTheWeek = date.getDay();
    if (dayOfTheWeek === 6) date.setDate(date.getDate() + 2); // sábado
    if (dayOfTheWeek === 0) date.setDate(date.getDate() + 1); // domingo
  }
}
