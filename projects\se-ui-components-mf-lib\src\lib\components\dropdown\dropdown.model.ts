import { TemplateRef } from "@angular/core";

export interface SeDropdown {
  id?: string;
  label?: string;
  ariaLabel?: string;
  autoSelect?: boolean;
  virtualScroll?: boolean;
  virtualScrollItemSize?: number;
  appendTo?: string;
  options?: SeDropdownOption[];
  optionLabel?: string;
  optionValue?: string;
  placeholder?: string;
  emptyMessage?: string;
  showClear?: boolean;
  editable?: boolean;
  readOnly?: boolean;
  formControlName?: string;
  filter?: boolean;
  tooltip?: boolean;
  tooltipText?: string | TemplateRef<HTMLElement>;
  scrollHeight?: string;
  disabled?: boolean;
}

export interface SeDropdownOption {
	id: string | number | boolean;
	label: string;
	selected?: boolean;
	disabled?: boolean;
	icon?: string;
	// Add option tooltip
	tooltipText?: string | TemplateRef<HTMLElement> | undefined;
	tooltipCallback?: () => void;
	tooltipPosition?: 'top' | 'bottom' | 'left' | 'right',
	tooltipStyleClass?: string;
	[key: string]: any;
}
