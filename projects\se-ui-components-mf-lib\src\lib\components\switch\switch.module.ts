import {NgModule} from "@angular/core";
import {SwitchComponent} from "./switch.component";
import {CommonModule} from "@angular/common";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {SeSharedModule} from "../../shared/shared.module";
import {TranslateModule} from "@ngx-translate/core";

@NgModule({
  imports: [CommonModule, ReactiveFormsModule, FormsModule, SeSharedModule, TranslateModule.forChild()],
  declarations: [SwitchComponent],
  exports: [SwitchComponent]
})
export class SeSwitchModule {
}
