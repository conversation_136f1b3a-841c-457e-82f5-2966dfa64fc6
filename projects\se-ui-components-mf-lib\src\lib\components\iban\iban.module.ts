import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SeSharedModule } from '../../shared/shared.module';
import { SeFormControlErrorModule } from '../form-control-error/form-control-error.module';
import { IbanComponent } from './iban.component';
import { InputMaskModule } from 'primeng/inputmask';
import { TranslateModule } from '@ngx-translate/core';
import { SeInputUpperCaseModule } from '../../directives';

@NgModule({
  declarations: [IbanComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    SeSharedModule,
    SeFormControlErrorModule,
    InputMaskModule,
    TranslateModule,
    SeInputUpperCaseModule
  ],
  exports: [IbanComponent],
})
export class SeIbanModule {}
