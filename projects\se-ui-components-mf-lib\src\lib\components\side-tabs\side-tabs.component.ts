import {
  Component,
  ContentChildren,
  ElementRef,
  Input,
  QueryList,
  TemplateRef,
  ViewChild,
  AfterViewInit,
  ChangeDetectorRef,
  Output,
  EventEmitter,
  HostListener,
  SimpleChanges
} from '@angular/core';
import { SeSideTabsSection } from './side-tabs.model';
import { SideTabContentDirective } from './side-tabs-content.directive';
import { SeDeviceService } from '../../services';
@Component({
	selector: 'se-side-tabs',
	template: `
  <div class="se-side-tabs row">
  <!-- LEFT NAVIGATION COLUMN -->
  <div class="col-md-3 col-12 se-side-tabs--container">
	<se-button *ngIf="showPrevButton" class="scroll-button scroll-button--prev" icon="matChevronLeftOutline" btnTheme="onlyText" (onClick)="scrollPrev()" />
	<div #navigation class="se-side-tabs--nav" (scroll)="checkScrollbar()">
	<button
		*ngFor="let section of sections"
		class="se-side-tabs--button"
		[ngClass]="{'active' : section.id === selected}"
		[id]="section.id"
		(click)="onSelect(section.id)">
      {{section.title | translate}}
    </button>
	</div>
	<se-button *ngIf="showNextButton" class="scroll-button scroll-button--next" icon="matChevronRightOutline" btnTheme="onlyText" (onClick)="scrollNext()" />
  </div>
  <!-- RIGHT PANEL CONTENT -->
  <div class="col-md-9 col-12 se-side-tabs--content" *ngIf="contentVisible" #contentContainer>
    <div *ngFor="let section of sections"
      [hidden]="section.id !== selected"
    >
      <h2 *ngIf="showTitles" class="se-side-tabs--title mb-4">{{(section?.contentTitle ? section?.contentTitle! : section.title! ) | translate}}</h2>
      <h3 *ngIf="section.description" class="se-side-tabs--subtitle mb-3" [innerHTML]="section.description | translate | safeHtml"></h3>
    </div>
    <ng-container *ngIf="contentTemplate && selected && contentVisible">
      <ng-container *ngTemplateOutlet="contentTemplate"></ng-container>
    </ng-container>
  </div>
</div>`,

	styleUrls: ['./side-tabs.component.scss'],
})
export class SeSideTabsComponent implements AfterViewInit {

	@ViewChild('contentContainer') contentContainer!: ElementRef;
	@ViewChild('navigation') navigation!: ElementRef;

	@Input() sections!: SeSideTabsSection[];

  @Input() set activeTabIndex(value: string | undefined) {
    if (value !== this.activeTabIndex && value !== undefined) {
      const isValidTabId = this.sections?.some(section => section.id === value);

      if (!isValidTabId) {
        return;
      }

      this._activeTabIndex = value;
      this.updateSelectedTab(value);
      this.onSelect(value, true);
    }
  }

  @Input() showTitles: boolean = true;

  @Input() selectMode: boolean = false;

  @Output() onSelected: EventEmitter<string> = new EventEmitter<string>();

	@Output() onChange:EventEmitter<string> = new EventEmitter<string>();

	@ContentChildren(SideTabContentDirective) templates!: QueryList<SideTabContentDirective>;

	@HostListener('window:resize')
	onWindowResize() {
		this.checkScrollbar();
	}

	readonly scrollAmount: number = 250;

	contentTemplate!: TemplateRef<any>;
	contentVisible = true;
	isEdge = false;
	showPrevButton: boolean = false;
	showNextButton: boolean = false;
  selected: string | undefined = undefined;

  private _activeTabIndex: string | undefined = undefined;
  get activeTabIndex(): string | undefined {
    return this._activeTabIndex;
  }

	constructor(
    private cdr: ChangeDetectorRef,
    private deviceService: SeDeviceService,
  ) {}

	ngAfterContentInit(): void {
    if(this.activeTabIndex !== undefined) {
      this.updateSelectedTab(this.activeTabIndex);
    }

    if (this.sections?.some(section => section.selected === true)) {
			let section = this.sections.find(section => section.selected === true);
			this.setContentTemplate(section?.id!);
			return;
		}

		this.selected = this.templates.first?.name?.toString();
		this.contentTemplate = this.templates.first?.template;
	}

	ngAfterViewInit(): void {
		setTimeout(() => this.checkScrollbar(), 0);
		this.detectBrowser();
	}

	private detectBrowser(): void {
		this.isEdge = this.deviceService.isEdge();
	}

  private updateSelectedTab(selectedId: string): void {
    this.sections = this.sections?.map((section) => ({
      ...section,
      selected: section.id === selectedId
    }));
  }

  setContentTemplate(id: string): void {
		this.selected = id;
		this.contentTemplate = this.templates?.find(template => template.name?.toString() === id)?.template!;
	}

	onSelect(id: string, change = false): void {

    if(this.selectMode !== undefined && !change) {
      this.onSelected.emit(id);
      return;
    }

		if (this.isEdge) {
			this.contentVisible = false;
			this.cdr.detectChanges();
		}

		this.setContentTemplate(id);

		setTimeout(() => {
			if (this.isEdge) {
				this.contentVisible = true;
				this.cdr.detectChanges();
			}

			// Focus the first focusable element
		  const contentContainerElement = this.contentContainer.nativeElement;
		  const focusableElement = contentContainerElement.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
		  if (focusableElement) {
			focusableElement.focus();
		  }
		});

		this.onChange.emit(id);
	}

	checkScrollbar(): void {
		if (!this.hasNavScrollbar) {
			this.showPrevButton = false;
			this.showNextButton = false;
			return;
		}

		this.updateButtonsVisibility();
	}

	scrollPrev(): void {
		this.scrollByAmount(-this.scrollAmount);
	}

	scrollNext(): void {
		this.scrollByAmount(this.scrollAmount);
	}

	private get hasNavScrollbar(): boolean {
		const nav = this.navigation.nativeElement;
		const innerWidth = window.innerWidth;
		return nav.scrollWidth > nav.clientWidth && innerWidth < 768;
	}

	private updateButtonsVisibility(): void {
		const nav = this.navigation.nativeElement;
		const isPositionStart = nav.scrollLeft === 0;
		const isPositionEnd =
			nav.scrollLeft + nav.clientWidth >= (nav.scrollWidth);

		this.showPrevButton = !isPositionStart;
		this.showNextButton = !isPositionEnd;
	}

	private scrollByAmount(amount: number): void {
		const nav = this.navigation.nativeElement;
		nav.scrollBy({ left: amount, behavior: 'smooth' });
		setTimeout(() => this.updateButtonsVisibility(), 50);
	}
}
