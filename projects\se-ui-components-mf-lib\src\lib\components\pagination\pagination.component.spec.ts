import { ComponentFixture, TestBed } from "@angular/core/testing";
import { PaginatorState } from "primeng/paginator";
import { PaginationComponent } from "./pagination.component";

describe('PaginationComponent', () => {
  let component: PaginationComponent;
  let fixture: ComponentFixture<PaginationComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [PaginationComponent]
    });
    fixture = TestBed.createComponent(PaginationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.first).toBe(0);
    expect(component.rows).toBe(0);
    expect(component.totalRecords).toBe(0);
    expect(component.innerText).toBe('');
    expect(component.prevButtonLabel).toBe('');
    expect(component.nextButtonLabel).toBe('');
    expect(component.showRowsPerPage).toBe(false);
    expect(component.showNumberReportPaginator).toBe(true);
    expect(component.currentPage).toBe(1);
    expect(component.totalPages).toBe(0);
  });

  it('should initialize with provided input values', () => {
    component.first = 5;
    component.rows = 10;
    component.totalRecords = 100;
    component.innerText = 'Test';
    component.prevButtonLabel = 'Prev';
    component.nextButtonLabel = 'Next';
    component.showRowsPerPage = true;
    component.showNumberReportPaginator = false;
    fixture.detectChanges();

    expect(component.first).toBe(5);
    expect(component.rows).toBe(10);
    expect(component.totalRecords).toBe(100);
    expect(component.innerText).toBe('Test');
    expect(component.prevButtonLabel).toBe('Prev');
    expect(component.nextButtonLabel).toBe('Next');
    expect(component.showRowsPerPage).toBe(true);
    expect(component.showNumberReportPaginator).toBe(false);
    expect(component.currentPage).toBe(6);
    expect(component.totalPages).toBe(10);
  });

  it('should handle page change correctly', () => {
    const paginatorState: PaginatorState = { first: 10, rows: 10, page: 1 };
    spyOn(component.onPageChangeEvent, 'emit');

    component.handlePageChange(paginatorState);

    expect(component.first).toBe(10);
    expect(component.rows).toBe(10);
    expect(component.currentPage).toBe(2);
    expect(component.onPageChangeEvent.emit).toHaveBeenCalledWith(paginatorState);
  });

  it('should render correct number of pages', () => {
    component.totalRecords = 100;
    component.rows = 10;
    fixture.detectChanges();

    const paginatorElement = fixture.nativeElement.querySelector('p-paginator');
    expect(paginatorElement).toBeTruthy();
    expect(component.totalPages).toBe(10);
  });

  it('should update template when inputs change', () => {
    component.totalRecords = 200;
    component.rows = 20;
    fixture.detectChanges();

    const counterElement = fixture.nativeElement.querySelector('.paginator-container__counter');
    expect(counterElement.textContent).toContain('10 de 10');
  });
});