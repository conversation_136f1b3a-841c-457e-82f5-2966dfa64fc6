import { Subscription } from 'rxjs';
import { SeHttpResponse } from '../http/http-service.model';
import { ValidatorFn } from '@angular/forms';

/**
 * 	VALIDATIONS
 */

export enum FieldValidations {
  required = 'required',
  pattern = 'pattern',
  validatePattern = 'validatePattern', // Primeng validation
  dniPattern = 'dniPattern',
  dniLetter = 'dniLetter',
  cifPattern = 'cifPattern',
  cifLetter = 'cifLetter',
  cifNumber = 'cifNumber',
  niePattern = 'niePattern',
  nieLetter = 'nieLetter',
  equals = 'equals',
  lessThan = 'lessThan',
  greaterThan = 'greaterThan',
  equalsOrLessThan = 'equalsOrLessThan',
  equalsOrGreaterThan = 'equalsOrGreaterThan',
  fieldRequired = 'fieldRequired',
  ibanPattern = 'ibanPattern',
}
export type FieldValidationsType = keyof typeof FieldValidations;

// Masks (RegExp)
export enum FieldMasks {
  any = 'any',
  cif = 'cif',
  nif = 'nif',
  email = 'email',
  phone = 'phone',
}
export type FieldMasksT = keyof typeof FieldMasks;

/**
 * 	DYNAMIC DEPENDENCIES
 */

export interface SeDynamicDependencies {
  // Set a field as required
  required?: string[];
  // Disable a field based on conditions (other fields values, etc.)
  disabled?: string[];
  // Set a field visibility based on conditions (other fields values, etc.)
  visibility?: string[];
}

export interface L3Subscription {
  // Campo que tiene el listener
  [x: string]: {
    // Listener
    subscription: Subscription;
    // Campos que tienen dependencias con este campo
    dependencies: { [x: string]: SeDynamicDependencies };
  };
}

export interface ScoringResponse {
  match?: boolean;
  nif?: string;
  fullName?: string;
  percentageSim?: number;
}

export interface IScoringResponse extends SeHttpResponse {
  content: ScoringResponse;
}


export interface TranslateError {
  validator: ValidatorFn, 
  translation?: string,
  translateParams?: { [key: string]: string | number }
  translationByKey?: { [key: string]: string | number };
  translationByKeyParams?: {[key: string]:{[key: string]: string | number}};
}