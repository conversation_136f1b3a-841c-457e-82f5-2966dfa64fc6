import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { Observable, Subject, take } from 'rxjs';
import { SeDocumentsService, SeModalService } from '../../services';
import { CellEvent, FlattenedCell } from '../table/cells/cells.model';
import { Column } from '../table/columns/column.model';
import { FlattenedRow } from '../table/rows/rows.model';
import { Row } from '../table/table.model';
import { AttachFileTableComponent } from './modal-table/attach-modal.component';
import {
  AttachFile,
  SeAttachFileModal,
} from './modal-table/attach-modal.model';
import { FileFormatsSeparation, FileUploaderError, MIMEFormats } from './upload-files.model';
import { UploadFilesService } from './upload-files.service';
import { generateUniqueId } from '../../shared/utils/generate-unique-id/generate-unique-id';
import { Nullable } from '../../models';

@Component({
  selector: 'se-upload-files',
  templateUrl: 'upload-files.component.html',
  styleUrls: ['./upload-files.component.scss'],
})
export class UploadFilesComponent {
  @ViewChild('se_upload_file_input') input: ElementRef | undefined;
  @ViewChild('se_upload_drop_area') drop_area: ElementRef | undefined;

  private _tableColumns!: Column[];

  uploadedFiles: AttachFile[] = [];

  allowedFileExtensions = '*';

  isHoverActive = false;

  _filesCount: number = 0;

  set filesCount(value: number) {
    this._filesCount = value;
  }

  get filesCount() {
    return this._filesCount;
  }

  tableRows: Row[] = [];

  listErrors: string[] = [];

  @Input() hasActions = true;

  @Input() set tableColumns(data: Column[]) {
    if (!data) return;

    this._tableColumns = [...data];

    if (
      this.hasActions &&
      !this.tableColumns.find(
        (column: Column) => column.key === 'download'
      )
    ) {
      this._addActionsColumn();
    }
  }

  @Input() modalTableColumns!: Column[];

  get tableColumns(): Column[] {
    return this._tableColumns;
  }

  @Input() info: string = '';

  @Input() showInput: boolean = true;

  @Input() onlyTable: boolean = false;

  @Input() hasModal: boolean = false;

  @Input() modalData!: SeAttachFileModal | undefined;

  @Input() dropAreaTitlePreLinkText: string | undefined;
  @Input() dropAreaTitleLinkText: string | undefined;
  @Input() dropAreaTitlePostLinkText: string | undefined;

  @Input() subtitle: boolean = true;

  @Input() subtitleText: string | undefined;

  @Input() title!: string;

  @Input() set files(data: AttachFile[] | undefined) {
    if (!data || data?.length === 0) {
      this.uploadedFiles = [];
      this.filesCount = 0;
      this.tableRows = [];
    } else {
      this.uploadedFiles = data;
      this.filesCount = data.length;
      this.tableRows = [...this.uploadFileService.addTableRows(data)];
    }
  }

  format: string = '';
  _accept: string[] = ['*'];

  @Input() set accept(value: string[]) {
    this.setAcceptFormats(value);
  }

  get accept(): string[] {
    return this._accept;
  }

  get groupSize(): string {
    return (this.groupSizeLimit / 1000).toFixed(0);
  }

  get fileSize(): string {
    return (this.sizeLimitPerFile / 1000).toFixed(0);
  }

  @Input() fileFormatSeparation: FileFormatsSeparation = FileFormatsSeparation.SLASH;

  @Input() multiple: boolean = true;

  /**
   * Size limit per file using Kbs
   * @default 5000
   */
  @Input() sizeLimitPerFile: number = 5000;

  /**
   * Size limit for the sum of all the files using Kbs
   * @default 125000
   */

  @Input() groupSizeLimit: number = 125000;

  @Input() maxFiles: number = 25;

  @Input() set required(value: boolean) {
    this.labelRequired = value ? "SE_COMPONENTS.FILE_UPLOADER.REQUIRED" : "SE_COMPONENTS.FILE_UPLOADER.OPTIONAL";
  }

  @Input() customTrackById: Nullable<
    (index: number, row: FlattenedRow) => string
  >;
  @Input() set disabled(value: boolean) {
    this._disabled = value;
    if(value) {
      this.removeDragAndDropListeners();
    } else {
      this.addDragAndDropListeners()
    }
  };
  
  private _disabled: boolean = false;

  get disabled(): boolean {
    return this._disabled;
  }

  @Input() useFileNameAsDescription: boolean = false;

  @Output() filesLoaded: EventEmitter<AttachFile[]> = new EventEmitter();

  @Output() editFile: EventEmitter<AttachFile> = new EventEmitter();

  @Output() deleteFile: EventEmitter<string> = new EventEmitter();

  @Output() downloadFile: EventEmitter<string> = new EventEmitter();

  @Output() errors: EventEmitter<FileUploaderError> = new EventEmitter();

  get isDropAreaVisible(): boolean {
    return this.showInput && !this.onlyTable && this.maxFiles > this.filesCount
  }

  labelRequired: string = "SE_COMPONENTS.FILE_UPLOADER.OPTIONAL";
  reloadTable$: Subject<void> = new Subject();

  constructor(
    private translateService: TranslateService,
    private seDocumentsService: SeDocumentsService,
    private modalService: SeModalService,
    private uploadFileService: UploadFilesService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['accept'] && changes['accept'].currentValue) {
      this.setAcceptFormats(changes['accept'].currentValue);
    }
  }

  ngAfterViewInit(): void {
    if(!this.disabled) {
      this.addDragAndDropListeners();
    }
  }

  private _preventDefaultsListener = (e: Event) => {
    e.preventDefault();
    e.stopPropagation();
  };

  private _highlightListener = this._highlight.bind(this) as EventListener;
  private _unHighlightListener = this._unHighlight.bind(this) as EventListener;
  private _handleDropListener = this.handleDrop.bind(this) as EventListener;

  private addDragAndDropListeners(): void {
    const dropArea = this.drop_area?.nativeElement as HTMLInputElement;
    if (!dropArea) {
      return;
    }
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach((eventName) => {
      dropArea.addEventListener(eventName, this._preventDefaultsListener, false);
    });

    ['dragenter', 'dragover'].forEach((eventName) =>
      dropArea.addEventListener(
        eventName as keyof HTMLElementEventMap,
        this._highlightListener,
        false
      )
    );

    ['dragleave', 'drop'].forEach((eventName) =>
      dropArea.addEventListener(
        eventName as keyof HTMLElementEventMap,
        this._unHighlightListener,
        false
      )
    );

    dropArea.addEventListener('drop', this._handleDropListener, false);
  }

  private removeDragAndDropListeners(): void {
    const dropArea = this.drop_area?.nativeElement as HTMLInputElement;
    if (!dropArea) {
      return;
    }
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach((eventName) => {
      dropArea.removeEventListener(eventName, this._preventDefaultsListener, false);
    });

    ['dragenter', 'dragover'].forEach((eventName) =>
      dropArea.removeEventListener(
        eventName as keyof HTMLElementEventMap,
        this._highlightListener,
        false
      )
    );

    ['dragleave', 'drop'].forEach((eventName) =>
      dropArea.removeEventListener(
        eventName as keyof HTMLElementEventMap,
        this._unHighlightListener,
        false
      )
    );

    dropArea.removeEventListener('drop', this._handleDropListener, false);
  }

  ngOnDestroy(): void {
    this.reloadTable$.complete();
  }

  /**
   * Handle drop file selection
   */
  handleDrop(e: DragEvent): void {
    const dt = e.dataTransfer;
    if (dt) {
      const files = Array.from(dt.files);
      this.handleFiles(files);
    }
  }

  /**
   * Close the info alert.
   */
  closeInfo(): void {
    this.info = '';
  }

  /**
   * Opens the attach modal.
   */
  private openAttachModal(files: AttachFile[]): Observable<AttachFile[]> {
    const modalData: SeAttachFileModal = this.modalData
      ? this.modalData
      : {
          tableColumns: this.modalTableColumns,
          files,
          hasActions: this.hasActions,
          title: 'SE_COMPONENTS.FILE_UPLOADER.MODAL.TITLE',
          subtitle: 'SE_COMPONENTS.FILE_UPLOADER.MODAL.SUBTITLE',
          secondaryButtonLabel: 'UI_COMPONENTS.BUTTONS.CANCEL',
          centered: true,
          closable: false,
          secondaryButton: true,
          titleTextWeight: 'regular',
          backdrop: 'static',
          size: 'xl',
          component: AttachFileTableComponent,
        };

    const modalRef: NgbModalRef = this.modalService.openModal(modalData);
    modalRef.componentInstance.useFileNameAsDescription = this.useFileNameAsDescription;

    return modalRef.componentInstance.list.pipe(take(1));
  }

  /**
   * Close the error alert.
   */
  closeErrors(): void {
    this.listErrors = [];
  }

  /**
   * Handle input selection
   */
  handleSelection(event: Event): void {
    const element = event.currentTarget as HTMLInputElement;
    const fileList: FileList | null = element.files;
    if (fileList) {
      this.handleFiles(Array.from(fileList));
    }
    // Remove selected files, so browsers doesn't stop us from upload the same file again
    const inputElement: HTMLInputElement = this.input?.nativeElement as HTMLInputElement;
    if (inputElement) {
      inputElement.value = '';
    }
  }

  handleFiles(fileList: AttachFile[]): void {
    this.closeErrors();
    let fileListToAdd = this._checkMultipleFiles(fileList);

    const correctGroupFileSize = this._validateGroupSize(
      fileListToAdd,
      this.groupSizeLimit
    );

    const filesCount = this.filesCount + fileListToAdd.length;

    fileListToAdd = fileListToAdd
      .filter((file) => this._checkFilesType(file))
      .filter((file) => this._validateFileSize(file, this.sizeLimitPerFile))
      .filter((file) => this._fileIsNotEmpty(file));

    if (fileListToAdd.length > 0 && filesCount <= this.maxFiles && correctGroupFileSize) {
      this._openAttachedModalOrAddFiles(fileListToAdd);
    } else {
      this._handleErrorsAdd(fileListToAdd)
    }
  }

  getFileSize(sizeInKb: number): string {
    return this.seDocumentsService.convertSize(sizeInKb);
  }

  onClick(event: Event) {
    const inputElement: HTMLInputElement = this.input?.nativeElement as HTMLInputElement;
    event.stopPropagation();
    inputElement.click();
  }

  onKeydown(event: KeyboardEvent) {
    if(event.code === "Enter" || event.code === "Space") {
      const inputElement: HTMLInputElement = this.input?.nativeElement as HTMLInputElement;
      event.stopPropagation();
      inputElement.click();
    }
  }

  private _checkMultipleFiles(files: File[]): File[] {
    if (!this.multiple && files.length > 1) {
      this.errors.emit({
        message: this.translateService.instant(
          'SE_COMPONENTS.FILE_UPLOADER.ERRORS.MULTIPLE_FILES'
        ),
      });
      this.listErrors.unshift(
        this.translateService.instant(
          'SE_COMPONENTS.FILE_UPLOADER.ERRORS.MULTIPLE_FILES'
        )
      );
      return [files[0]];
    }

    return files;
  }

  private _checkFilesType(file: File): boolean {
    const fileType = file.type;
    const acceptedFileTypes: string[] = this.accept.map((el) => MIMEFormats[el])
    if (acceptedFileTypes.includes(fileType) || this.accept.includes('*')) {
      return true;
    } else {
      this.errors.emit({
        message: this.translateService.instant(
          'SE_COMPONENTS.FILE_UPLOADER.ERRORS.WRONG_FILE_TYPE',
          { file: file.name }
        ),
      });
      this.listErrors.unshift(
        this.translateService.instant(
          'SE_COMPONENTS.FILE_UPLOADER.ERRORS.WRONG_FILE_TYPE',
          { file: file.name }
        )
      );
      return false;
    }
  }

  private _fileIsNotEmpty(file: File) {
    const isFileEmpty = !file.size;
    if (isFileEmpty) {
      this.errors.emit({
        message: this.translateService.instant(
          'SE_COMPONENTS.FILE_UPLOADER.ERRORS.EMPTY_FILE',
          { file: file.name }
        ),
      });
      this.listErrors.unshift(
        this.translateService.instant(
          'SE_COMPONENTS.FILE_UPLOADER.ERRORS.EMPTY_FILE',
          { file: file.name }
        )
      );
    }
    return !isFileEmpty;
  }

  private _validateGroupSize(
    filesToAdd: AttachFile[],
    maxGroupSize: number
  ): boolean {
    const totalItemsSizeInBytes = this.uploadFileService.getTotalFileSize(this.uploadedFiles);
    const newFilesSize = this.uploadFileService.getTotalFileSize(filesToAdd);
    const totalInKbs = this.uploadFileService.getFileSizeInKb(newFilesSize + totalItemsSizeInBytes);

    if (totalInKbs > maxGroupSize) {
      const message = this.translateService.instant(
        'SE_COMPONENTS.FILE_UPLOADER.ERRORS.MAX_GROUP_SIZE',
        { size: this.groupSize + ' MB' }
      );

      this.errors.emit({  message });
      this.listErrors.unshift(message);
    }
    return totalInKbs <= this.groupSizeLimit;
  }

  private _validateFileSize(file: File, size: number): boolean {
    // Check the file size in kilobytes
    const sizeInKB = this.uploadFileService.getFileSizeInKb(file.size);
    if (sizeInKB > size) {
      this.errors.emit({
        message: this.translateService.instant(
          'SE_COMPONENTS.FILE_UPLOADER.ERRORS.MAX_FILE_SIZE',
          { file: file.name, size: this.getFileSize(size) }
        ),
      });
      this.listErrors.unshift(
        this.translateService.instant(
          'SE_COMPONENTS.FILE_UPLOADER.ERRORS.MAX_FILE_SIZE',
          { file: file.name, size: this.getFileSize(size) }
        )
      );
      this.info = this.translateService.instant(
        'SE_COMPONENTS.FILE_UPLOADER.INFO_CUSTOM',
        { groupSize: this.groupSize, fileSize: this.fileSize } 
      );
      return false;
    }
    return true;
  }

  private _openAttachedModalOrAddFiles(fileListToAdd: AttachFile[]) {
    if(this.hasModal) {
      this.openAttachModal(fileListToAdd)
        .pipe(take(1))
        .subscribe((list: AttachFile[]) => {
          this._handleAddFiles(list);
        });
    } else {
      this._handleAddFiles(fileListToAdd);
    }
  }

  private _handleErrorsAdd(
    fileListToAdd: AttachFile[]
  ) : boolean {
    const filesCount = this.filesCount + fileListToAdd.length;
    let isFileCountWithinLimit = true;
    if (filesCount > this.maxFiles) {
      isFileCountWithinLimit = false;
      this.errors.emit({
        message: this.translateService.instant(
          'SE_COMPONENTS.FILE_UPLOADER.ERRORS.FILES_LIMIT',
          { fileList: this.filesCount, maxFiles: this.maxFiles }
        ),
      });
      this.listErrors.unshift(
        this.translateService.instant(
          'SE_COMPONENTS.FILE_UPLOADER.ERRORS.FILES_LIMIT',
          { fileList: this.filesCount, maxFiles: this.maxFiles }
        )
      );
    }

    return isFileCountWithinLimit;
  }

  private _handleAddFiles(
    fileListToAdd: AttachFile[]
  ) : void {
    if (
      fileListToAdd.length &&
      this._handleErrorsAdd(fileListToAdd)
    ) {
      fileListToAdd.forEach((file) => { file.id = generateUniqueId() });
      this.tableRows = [...this.tableRows, ...this.uploadFileService.addTableRows(fileListToAdd, this.useFileNameAsDescription)];
      this.filesLoaded.emit(fileListToAdd);
      this.uploadedFiles = [...this.uploadedFiles, ...fileListToAdd];
      this.filesCount += fileListToAdd.length;
    }
  }

  private _addActionsColumn = (): void => {
    this._tableColumns.push({
      header: '',
      key: 'download',
      size: 10,
      resizable: false,
      cellComponentName: 'actionsCellComponent',
      cellConfig: {
        hasDownload: true,
        hasConfirmation: true,
        hasEdit: false,
        modalService: this.modalService,
        deleteCallback: (
          row: FlattenedRow,
          cell: FlattenedCell,
          column: Column
        ) => {
          return new Promise(() => {
            this.removeFileFromTable(cell);
            this.deleteFile.emit(cell?.rowData?.id?.value);
            if (this.input) {
              this.input.nativeElement.value = '';
            }
          });
        },
        editCallback: (
          row: FlattenedRow,
          cell: FlattenedCell,
          column: Column
        ) => {
          return new Promise(() => {
            // TODO: Implement edit file
            let attachFile: AttachFile = cell?.rowData?.file.value;
            attachFile.id =  cell?.rowData?.id.value;
            this.openAttachModal([attachFile])
            .pipe(take(1))
            .subscribe((list: AttachFile[]) => {
              //Remove the edited file from the list
              this.removeFileFromTable(cell)
              this._handleAddFiles(list);
            });
          });
        },
        downloadCallback: (
          row: FlattenedRow,
          cell: FlattenedCell,
          column: Column
        ) => {
          return new Promise(() => {
            this.downloadFile.emit(cell?.rowData?.id?.value);
          });
        },
      },
    });
  }

  setAcceptFormats = (value: string[]): void => {
    if (!value) return;

    this._accept = value;

    this.allowedFileExtensions = value
      .map((el) => MIMEFormats[el])
      .join(', ');

    this.format = this.uploadFileService.setFileFormat(value,this.fileFormatSeparation);
  }

  removeFileFromTable = (cell: FlattenedCell): void => {
    this.closeErrors();
    const id = cell?.rowData?.id?.value;
    this.filesCount--;
    this.uploadedFiles = this.uploadedFiles.filter(
      (file) => file.id !== id
    );
    this.tableRows = this.tableRows.filter(
      (row) => row.data['id'].value !== id
    );
  }

  tableChanged = (event: CellEvent): void => {
    const newData = this.uploadFileService.setFilesDataFromRows(
      this.uploadedFiles,
      this.tableRows
    );
    this.uploadedFiles = [...newData];
    const file = this.uploadedFiles.find(
      (file) => file.id === event.cell.rowData.id.value
    );
    this.editFile.emit(file);

    // if(event.cellName === 'dropdownCellComponent') {
    //   this.reloadTable$.next();
    // }
  }

  //ACCESIBILITY
  private _highlight(): void {
    const dropArea = this.drop_area?.nativeElement as HTMLInputElement;
    dropArea.classList.add('drop-area--highlight');
    this.isHoverActive = true;
  }

  private _unHighlight(): void {
    const dropArea = this.drop_area?.nativeElement as HTMLInputElement;
    dropArea.classList.remove('drop-area--highlight');
    this.isHoverActive = false;
  }

}
