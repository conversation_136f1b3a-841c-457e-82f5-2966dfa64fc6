import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import {
  SeAccordionModule,
  SeAlertModule,
  SeBadgeModule,
  SeBreadcrumbdModule,
  SeButtonDropdownModule,
  SeButtonModule,
  SeCaptchaModule,
  SeCheckboxModule,
  SeConfirmationMessageModule,
  SeDatepickerModule,
  SeDropdownFilterModule,
  SeDropdownModule,
  SeEmptyStateModule,
  SeHeaderInfoModule,
  SeIbanModule,
  SeInputModule,
  SeLinkModule,
  SeModalModule,
  SeMultiselectModule,
  SePanelModule,
  SeProgressModalModule,
  SeRadioModule,
  SeRangeFilterModule,
  SeSelectiveCardModule,
  SeSideTabsModule,
  SeSpinnerModule,
  SeStepperdModule,
  SeSwitchModule,
  SeTableModule,
  SeTabsModule,
  SeTagModule,
  SeTextareaModule,
  SeToggleModule,
  SeUploadFilesModule,
  SeUserInfoBarModule,
  SeTreeModule,
  SeInfoMessagesModule
} from './components';
import { SeUiComponentsMfLibComponent } from './se-ui-components-mf-lib.component';

const componentModules = [
  SeAccordionModule,
  SeAlertModule,
  SeRangeFilterModule,
  SeBadgeModule,
  SeBreadcrumbdModule,
  SeButtonDropdownModule,
  SeButtonModule,
  SeCaptchaModule,
  SeCheckboxModule,
  SeConfirmationMessageModule,
  SeDatepickerModule,
  SeDropdownFilterModule,
  SeDropdownModule,
  SeEmptyStateModule,
  SeHeaderInfoModule,
  SeIbanModule,
  SeInputModule,
  SeLinkModule,
  SeModalModule,
  SeMultiselectModule,
  SePanelModule,
  SeRadioModule,
  SeSelectiveCardModule,
  SeSideTabsModule,
  SeSpinnerModule,
  SeStepperdModule,
  SeSwitchModule,
  SeTableModule,
  SeTabsModule,
  SeTagModule,
  SeTextareaModule,
  SeToggleModule,
  SeUploadFilesModule,
  SeUserInfoBarModule,
  SeProgressModalModule,
  SeTreeModule,
  SeInfoMessagesModule,
];

@NgModule({
  declarations: [SeUiComponentsMfLibComponent],
  imports: [
    CommonModule,
    BrowserModule,
    BrowserAnimationsModule,
    ...componentModules,
  ],
  exports: [...componentModules],
})
export class SeUiComponentsMfLibModule {}
