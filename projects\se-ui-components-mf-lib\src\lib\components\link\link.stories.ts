import type { <PERSON><PERSON>, StoryObj } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';
import { LinkComponent } from './link.component';
import { SeLinkModule } from './link.module';

// More on how to set up stories at: https://storybook.js.org/docs/angular/writing-stories/introduction
const meta: Meta<LinkComponent> = {
  title: 'Components/Link',
  component: LinkComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [SeLinkModule],
    }),
  ],
  args: {
    iconName: '',
    href: '',
    size: 'regular',
    linkTheme: 'primary',
    disabled: false,
    dashed: false,
    iconPosition: 'right',
    tooltipText: '',
  },
  argTypes: {
    href: {
      description: 'Set the URL to navigate from the link',
      type: 'string',
      table: {
        defaultValue: { summary: false },
      },
    },
    size: {
      description: 'Changes the link size.',
      options: ['regular', 'semibold'],
      control: { type: 'select' },
      table: {
        defaultValue: { summary: 'default' },
      },
    },
    tooltipText: {
      description: 'Tooltip text.',
      options: ['regular', 'semibold'],
      table: {
        defaultValue: { summary: '' },
      },
    },
    iconPosition: {
      description: 'Changes the icon position.',
      options: ['left', 'right'],
      control: { type: 'select' },
      table: {
        defaultValue: { summary: 'default' },
      },
    },
    linkTheme: {
      description: 'Changes the link theme.',
      options: ['primary', 'secondary', 'onlyText', 'inverse'],
      control: { type: 'select' },
      table: {
        defaultValue: { summary: 'primary' },
      },
    },
    iconName: {
      description: 'Set the name of a icon',
      type: 'string',
      table: {
        defaultValue: { summary: false },
      },
    },
    disabled: {
      description: 'Disable and stop the click event propagation',
      type: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
    dashed: {
      description: 'add dashed underline',
      type: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
  },
  render: (args) => ({
    props: args,
    template: `
    <se-link
    [disabled]="disabled"
    [href]="href"
    [iconName]="iconName"
    [size]="size"
    [dashed]="dashed"
    [iconPosition]="iconPosition"
    [tooltipText]="tooltipText"
    [linkTheme]="linkTheme">Web link</se-link>
    `,
  }),
};

export default meta;
type Story = StoryObj<LinkComponent>;

export const Primary: Story = {
  args: {
    size: 'regular',
    linkTheme: 'primary',
    href: '#',
    target: '_self',
  },
};
export const Secondary: Story = { args: { linkTheme: 'secondary' } };
export const PrimaryWithTooltip: Story = {
  args: {
    tooltipText: 'Esto es un tooltip',
  },
};
export const Icon: Story = {
  args: { iconName: 'matOpenInNewOutline', href: '#' },
};
