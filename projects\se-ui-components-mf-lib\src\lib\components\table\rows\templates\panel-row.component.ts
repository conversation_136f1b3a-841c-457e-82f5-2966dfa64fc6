import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { FlattenedGroupRow, FlattenedRow, RowComponent } from '../rows.model';

@Component({
  selector: 'tr[panel-row]',
  template: `
    <td [colSpan]="colSpan">
      <se-panel
        class="panel-0-padding"
        [id]="row.key"
        [title]="row.name"
        [colapsible]="true"
        [collapsed]="row.isCollapsed"
        (collapsedChange)="rowClick.emit(row)"
        panelTheme="primary"
      />
    </td>
  `,
  styleUrls: ['../styles/panel-row.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PanelRowComponent implements RowComponent {
  @Input() row!: FlattenedGroupRow;

  @Output() rowClick = new EventEmitter<FlattenedRow>();

  get colSpan(): number {
    return this.row.rowConfig.colSpan || 1;
  }
}
