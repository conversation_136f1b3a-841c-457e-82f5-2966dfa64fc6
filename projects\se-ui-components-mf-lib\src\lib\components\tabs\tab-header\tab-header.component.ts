import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'se-tab-header',
  template: `
    <div
      class="tab-label text-sm"
      [tabindex]="disabled ? '-1' : '0'"
      role="tab"
      [attr.aria-selected]="isActive"
      [class.active]="isActive"
      [class.disabled]="disabled"
      (click)="onClick($event)"
      (keydown.enter)="onClick($event)"
      (keydown.space)="onClick($event)"
    >
      <span>{{ label }}</span>
      <se-badge
        *ngIf="showBadge"
        [color]="badgeColor"
        [badgeTheme]="badgeTheme"
        [textColor]="badgeTextColor"
        [textInsideCircle]="badgeTextInsideCircle">
      </se-badge>
    </div>
  `,
  styleUrls: ['./tab-header.component.scss'],
})
export class TabHeaderComponent {
  @Input() label!: string;
  @Input() isActive!: boolean;
  @Input() disabled = false;
  @Input() showBadge: boolean = false;
  @Input() badgeColor: string = 'purple';
  @Input() badgeTextColor = 'white';
  @Input() badgeTextInsideCircle = '';
  @Input() badgeTheme: 'info' | 'success' | 'warning' | 'error' = 'info';
  
  @Output() onChange = new EventEmitter<void>();

  onClick(event: Event) {
    event.stopPropagation();

    if (!this.disabled) {
      this.isActive = true;
      this.onChange.emit();
    }
  }
}
