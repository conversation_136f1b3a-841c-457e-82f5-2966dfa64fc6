import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
// Libs
import { TranslateModule } from '@ngx-translate/core';
// App
import { SeSharedModule } from '../../shared/shared.module';
import { SeButtonModule } from '../button/button.module';
import { SideTabContentDirective } from './side-tabs-content.directive';
import { SeSideTabsComponent } from './side-tabs.component';

@NgModule({
  declarations: [SeSideTabsComponent, SideTabContentDirective],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    SeSharedModule,
    TranslateModule.forChild(),
    SeButtonModule,
  ],
  exports: [SeSideTabsComponent, SideTabContentDirective],
})
export class SeSideTabsModule {}
