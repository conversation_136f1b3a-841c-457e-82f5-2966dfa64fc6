@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins/breakpoints';

.tag {
  font-family: var(--font-primary);
  color: var(--color-white);
  font-weight: var(--font-semibold);
  line-height: var(--line-xs);
  display: inline-flex;
  padding: 4px 8px;
  align-items: center;
  gap: 4px;
  border-radius: 4px;

  &.primary {
    background: var(--color-primary-action);
    color: var(--color-white);
    border: 1px solid var(--color-primary-action);
  }

  &.secondary {
    border: 1px solid var(--color-primary-action);
    background: var(--color-white);
    color: var(--color-primary-action);

    .close-button {
      color: var(--color-primary-action);

      &:hover {
        border: 1px solid var(--color-primary-action);
      }
    }
  }

  &.onlyText {
    background: transparent;
    color: var(--color-black);
    border: 1px solid transparent;

    .close-button {
      color: var(--color-black);

      &:hover {
        border: 1px solid var(--color-black);
      }
    }
  }

  &.danger {
    background: var(--color-red-100);
    color: var(--color-red-500);
    border: 1px solid var(--color-red-500);

    .close-button {
      color: var(--color-red-500);

      &:hover {
        border: 1px solid var(--color-red-500);
      }
    }
  }

  &.success {
    background: #dcefe3;
    color: var(--color-green-400);
    border: 1px solid var(--color-green-400);

    .close-button {
      color: var(--color-green-400);

      &:hover {
        border: 1px solid var(--color-green-400);
      }
    }
  }

  &.warning {
    background: #ffebcc;
    color: #7a4b00;
    border: 1px solid #7a4b00;

    .close-button {
      color: #7a4b00;

      &:hover {
        border: 1px solid #7a4b00;
      }
    }
  }

  &.pink {
    background: var(--color-pink-200);
    border: 1px solid var(--color-pink-200);
    color: var(--color-gray-650);

    .close-button {
      color: var(--color-gray-650);

      &:hover {
        border: 1px solid var(--color-gray-650);
      }
    }
  }

  &.purple {
    background: var(--color-purple-100);
    border: 1px solid var(--color-purple-400);
    color: var(--color-purple-400);

    .close-button {
      color: var(--color-gray-600);

      &:hover {
        border: 1px solid var(--color-gray-600);
      }
    }
  }

  &.info {
    background: var(--color-blue-200);
    border: 1px solid var(--color-blue-600);
    color: var(--color-blue-600);

    .close-button {
      color: var(--color-gray-600);

      &:hover {
        border: 1px solid var(--color-gray-600);
      }
    }
  }

    &.gray {
    background: var(--color-gray-200);
    border: 1px solid var(--color-gray-600);
    color: var(--color-gray-600);

    .close-button {
      color: var(--color-gray-600);

      &:hover {
        border: 1px solid var(--color-gray-600);
      }
    }
  }

  .close-button {
    color: var(--color-white);
    text-align: center;
    display: flex;
    justify-content: center;
    width: 16px;
    height: 16px;
    background: transparent;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    padding: 0;
    border: 1px solid transparent;

    &:hover {
      border: 1px solid var(--color-white);
    }
  }

  .info-icon {
    --ng-icon__size: 1rem !important;
  }
}

@include media-breakpoint-down(md) {
  ::ng-deep.responsive-table-tab {
    .p-tooltip-text {
      max-width: 200px;
    }
  }
}