import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import {
  Subject,
  debounceTime,
  distinctUntilChanged,
  skip,
  takeUntil,
} from 'rxjs';
import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import { CellEventService } from '../cell-event.service';
import { CellComponent, CellEventTypes, FlattenedCell, InputCellConfig } from '../cells.model';

@Component({
  selector: 'se-input-cell',
  template: ` <div class="se-input-cell">
    <form [formGroup]="form">
      <se-input
        formControlName="value"
        [currencyMode]="!!cellConfig.currencyMode"
        [currencySymbol]="cellConfig.currencySymbol ?? ''"
        [readonly]="!!cellConfig.readonly"
        [labelAlign]="cellConfig.align ?? 'left'"
        [decimals]="cellConfig.decimals ?? 2"
        [id]="'input-value-' + row.id"
        (onBlur)="onBlurEvent($event)"
        [ariaLabel]="((cellConfig.ariaLabel || column.header) | translate) + ' input'"
        [maxLength]="cellConfig.maxLength"
      >
      </se-input>
    </form>
  </div>`,
  styleUrls: ['../styles/input-cell-template.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InputCellComponent implements CellComponent {
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: InputCellConfig;

  form!: FormGroup;

  private unsubscribe: Subject<void> = new Subject();

  constructor(
    private fb: FormBuilder,
    private cellEventService: CellEventService
  ) {}

  ngOnInit(): void {
    this.form = this.fb.group({
      value: [this.value, this.cellConfig.validators ?? []],
    });
    if (!this.cellConfig.onBlurEvent) {
      this.form
        .get('value')
        ?.valueChanges.pipe(
          skip(1),
          distinctUntilChanged(),
          debounceTime(200),
          takeUntil(this.unsubscribe)
        )
        .subscribe((value) => {
          this.handleEvent(value);
        });
    }
  }

  onBlurEvent(value: string) {
    if (this.cellConfig.onBlurEvent) {
      this.handleEvent(value);
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  handleEvent(value: string): void {
    if (this.form.valid) {
      this.throwEvent(CellEventTypes.EDIT_ROW, 'InputCellComponent', value);
    }
  }

  private throwEvent(type: CellEventTypes, cellName: string, value: string) {
    let newData: { [key: string]: any } = {};
    newData[this.column.key] = value;

    this.cellEventService.emitEvent({
      type,
      cellName,
      cell: this.cell,
      data: { newData, rowId: this.row.id, apply: true },
    });
  }
}
