<div
  class="se-table-component"
  [ngClass]="styleClass"
  *ngIf="data?.length || !showEmptyState; else emptyState"
>
  <div class="se-table-container">
    <form
      *ngIf="formGroup && showSelectAll && selectable"
      [formGroup]="formGroup"
      class="se-table-container__select-all mb-4 d-block d-md-none"
      [ngClass]="{
        'summary-mode-mobile-select-all':
          mobileLayoutMode === TableMobileLayoutMode.SUMMARY_MODE && isMobile()
      }"
    >
      <se-checkbox
        [label]="selectAllLabel | translate"
        id="select-all-items-mobile"
        (onClick)="onHeaderCheckboxChange()"
        formControlName="checkbox"
        [ariaLabel]="'UI_COMPONENTS.SELECT_ROW.ALL' | translate"
      >
      </se-checkbox>
    </form>

    <table class="se-table">
      <thead class="se-table__header">
        <tr class="se-table__row">
          <ng-container *ngIf="columns?.length">
            <th
              *ngFor="let column of flattenedColumns"
              [columnResize]="resizable && column.resizable"
              [columns]="flattenedColumns"
              [columnSize]="column.size"
              [column]="column"
              class="se-table__header-cell text-sm"
              [ngStyle]="{
                'text-align': column.cellConfig?.align ?? 'left',
                'justify-items': column.cellConfig?.align ?? 'left'
              }"
              [ngClass]="{ sortable: column.sortable }"
              (click)="onHeaderClick(column)"
              (keydown)="handleColumHeaderKeyEvent($event, column)"
              [tabindex]="column.sortable ? 0 : -1"
            >
              <div class="d-flex align-items-start">
                <span
                  [title]="column.header | translate"
                  [attr.aria-label]="column.header | translate"
                  [innerHTML]="column.header | translate | safeHtml"
                >
                </span>
                <div *ngIf="column.tooltip">
                  <ng-icon
                    class="tooltip-icon"
                    name="matInfo"
                    [pTooltipAccessible]="column.tooltipText"
                  ></ng-icon>
                </div>
                <div *ngIf="column.sortable">
                  <ng-icon
                    class="sortable-icon"
                    name="matSwapVertSharp"
                    [ngClass]="
                      currentSortColumn && currentSortColumn === column.key
                        ? 'sorted-column'
                        : ''
                    "
                  ></ng-icon>
                </div>
              </div>
              <div
                *ngIf="column.key === 'checkbox' && selectable"
                class="se-table-header-checkbox-container"
              >
                <form [formGroup]="formGroup">
                  <se-checkbox
                    formControlName="checkbox"
                    [id]="'checkbox'"
                    label=""
                    [ariaLabel]="'UI_COMPONENTS.SELECT_ROW.ALL' | translate"
                    (change)="onHeaderCheckboxChange()"
                  ></se-checkbox>
                </form>
              </div>
            </th>
          </ng-container>
        </tr>
      </thead>
      <tbody
        class="se-table__body"
        *ngIf="data?.length && renderFlag"
        [ngClass]="{ 'clickable-rows': clickableRows }"
      >
        <!-- Mobile SUMMARY_MODE: flex row, unified checkbox logic -->
        <ng-container
          *ngIf="
            mobileLayoutMode === TableMobileLayoutMode.SUMMARY_MODE &&
              isMobile();
            else defaultLayout
          "
        >
          <tr
            *ngFor="let row of flattenedData; trackBy: trackById.bind(this)"
            class="se-table-summary-row"
          >
            <td
              class="se-table-summary-content-cell"
              [attr.colspan]="selectable ? null : 2"
            >
              <div [ngClass]="selectable ? 'summary-content-relative' : 'standard-padding-left'">
                <!-- Place the auto-generated checkbox cell at the start, if selectable -->
                <ng-container *ngIf="selectable">
                  <ng-container *ngFor="let cell of row.cells">
                    <ng-container *ngIf="cell.column.key === 'checkbox'">
                      <div class="summary-checkbox-block">
                        <ng-container
                          cellTemplate
                          [cell]="cell"
                          [row]="row"
                        ></ng-container>
                      </div>
                    </ng-container>
                  </ng-container>
                </ng-container>
                <!-- Custom summary toggle button using se-button -->
                <se-button
                  *ngIf="hasExpandableColumns(row)"
                  class="summary-chevron-btn"
                  btnTheme="onlyText"
                  [icon]="
                    isSummaryRowExpanded(row.id)
                      ? 'matExpandLessOutline'
                      : 'matExpandMoreOutline'
                  "
                  [iconSize]="'1.4rem'"
                  [ariaLabel]="'UI_COMPONENTS.TABLE.VIEW_MORE' | translate"
                  (onClick)="toggleSummaryRow(row.id)"
                  (keydown.enter)="toggleSummaryRow(row.id)"
                  type="button"
                >
                </se-button>
                <div class="summary-row-content">
                  <ng-container *ngFor="let cell of row.cells">
                    <ng-container
                      *ngIf="
                        cell.column.key !== 'checkbox' &&
                        (cell.column.mobileCellConfig?.summary !== false ||
                          isSummaryRowExpanded(row.id))
                      "
                    >
                      <div
                        class="summary-cell-block"
                        [ngClass]="{ 'non-selectable': !selectable }"
                      >
                        <header
                          *ngIf="cell.column.mobileCellConfig?.header !== false"
                          class="se-table__cell__mobile__header"
                          [innerHTML]="
                            cell.column.header | translate | safeHtml
                          "
                        ></header>
                        <ng-container
                          cellTemplate
                          [cell]="cell"
                          [row]="row"
                        ></ng-container>
                      </div>
                    </ng-container>
                  </ng-container>
                </div>
              </div>
            </td>
          </tr>
        </ng-container>
        <!-- All other modes -->
        <ng-template #defaultLayout>
          <ng-container
            *ngFor="let row of flattenedData; trackBy: trackById.bind(this)"
          >
            <ng-container
              *ngIf="!row.isChild || (row.isChild && !row.isCollapsed)"
              [rowTemplate]="row"
              (rowClick)="emitRowClick($event)"
            />
            <ng-container *ngIf="isGroupRow(row) && !row.isCollapsed">
              <ng-container
                *ngFor="let childRow of row.children"
                [rowTemplate]="childRow"
                (rowClick)="emitRowClick($event)"
              />
            </ng-container>
          </ng-container>
        </ng-template>
      </tbody>
    </table>
  </div>
  <!-- PAGINATION -->
  <div
    *ngIf="showPagination && (showRowsPerPage || totalRecords > itemsPerPage)"
    class="se-table-component__pagination"
  >
    <se-pagination
      (onPageChangeEvent)="changePage($event)"
      [first]="first"
      [rows]="itemsPerPage"
      [totalRecords]="totalRecords"
      [showNumberReportPaginator]="true"
      [prevButtonLabel]="'UI_COMPONENTS.BUTTONS.PREVIOUS' | translate"
      [nextButtonLabel]="'UI_COMPONENTS.BUTTONS.NEXT' | translate"
      [innerText]="paginationInnerText"
      [selectedText]="paginationSelectedText"
      [showRowsPerPage]="showRowsPerPage"
      [rowsPerPageOptions]="rowsPerPageOptions"
      [downloadButton]="paginationDownloadButton"
      (downloadClick)="paginationDownloadClick.emit()"
    ></se-pagination>
  </div>
</div>
<ng-template #emptyState>
  <se-empty-state
    [message]="emptyMessage"
    [icon]="emptyIcon"
    [actionButton]="emptyButton"
    (actionButtonEvent)="onClickEmptyButton()"
  ></se-empty-state>
</ng-template>
