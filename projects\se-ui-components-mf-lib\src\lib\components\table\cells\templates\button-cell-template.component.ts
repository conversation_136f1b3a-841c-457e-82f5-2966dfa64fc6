import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import { ButtonCellConfig, CellComponent, CellConfig, CellEventTypes, FlattenedCell } from '../cells.model';
import { CellEventService } from '../cell-event.service';

@Component({
  selector: 'button-cell',
  template: `
    <div class="button-row-container">
      <se-button class="button-align"
        [btnTheme]="button.buttonConfig.btnTheme ?? 'secondary'"
        [disabled]="!!button.buttonConfig.disabled"
        [icon]="button.buttonConfig.icon ?? ''"
        [iconSize]="button.buttonConfig.iconSize ?? ''"
        [iconPosition]="button.buttonConfig.iconPosition ?? 'left'"
        [type]="button.buttonConfig.type ?? 'button'"
        (onClick)="onActionButton()"
        [size]="'small'">
        {{ button.buttonConfig.label || '' | translate }}
      </se-button>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ButtonCellComponent implements CellComponent {
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

  get button(): ButtonCellConfig {
    return this.cellConfig.buttonCell!;
  }

  constructor(private cellEventService: CellEventService) {
  }

  async onActionButton() {
    if (!this.button.buttonCallback) return this.throwEvent(CellEventTypes.ACTION_BUTTON_ROW, 'buttonCellComponent');
    const { data } = await this.button.buttonCallback(this.row, this.cell, this.column);

    if (data) {
      this.throwEvent(CellEventTypes.ACTION_BUTTON_ROW, 'buttonCellComponent', { newData: data, rowId: this.row.id });
    }
  }

  private throwEvent(type: CellEventTypes, cellName: string, data?: any, apply = false) {
    this.cellEventService.emitEvent({
      type,
      cellName,
      cell: this.cell,
      data: data ? { ...data, apply } : { apply }
    })
  }
}
