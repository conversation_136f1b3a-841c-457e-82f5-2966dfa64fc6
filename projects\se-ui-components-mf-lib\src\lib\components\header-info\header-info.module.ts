import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { SeSharedModule } from '../../shared/shared.module';
import { SeButtonModule } from '../button/button.module';
import { SeTagModule } from '../tag';
import { HeaderInfoComponent } from './header-info.component';
import { SeButtonDropdownModule } from '../button-dropdown';
import { SeLinkModule } from '../link/link.module';

@NgModule({
  imports: [
    CommonModule,
    SeSharedModule,
    TranslateModule,
    SeButtonModule,
    SeTagModule,
    SeButtonDropdownModule,
    SeLinkModule
],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [HeaderInfoComponent],
  declarations: [HeaderInfoComponent],
})
export class SeHeaderInfoModule {}
