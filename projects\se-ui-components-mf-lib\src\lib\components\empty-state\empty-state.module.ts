import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EmptyStateComponent } from './empty-state.component';
import { SeSharedModule } from '../../shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { SvgEmptyDocumentComponent } from './svg/empty-document.svg';
import { SvgEmptySearchComponent } from './svg/empty-search.svg';
import { SeButtonModule } from '../button/button.module';
import { SvgEmptyInfoComponent } from './svg/empty-info.svg';

@NgModule({
  imports: [
    SeSharedModule,
    CommonModule,
    TranslateModule.forChild(),
    SeButtonModule,
  ],
  declarations: [
    EmptyStateComponent,
    SvgEmptySearchComponent,
    SvgEmptyDocumentComponent,
    SvgEmptyInfoComponent
  ],
  exports: [EmptyStateComponent],
})
export class SeEmptyStateModule {}
