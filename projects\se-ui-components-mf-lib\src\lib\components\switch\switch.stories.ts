import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { SwitchComponent } from './switch.component';
import { SeSwitchModule } from './switch.module';

const meta: Meta<SwitchComponent> = {
  title: 'Components/Switch',
  component: SwitchComponent,
  decorators: [
    moduleMetadata({
      imports: [SeSwitchModule, ReactiveFormsModule],
    }),
  ],
  args: {
    id: 'test',
    title: 'Switch title',
    label: 'Switch label',
    disabled: false,
    labelPosition: 'right',
  },
  argTypes: {
    disabled: {
      description: 'Determines if the switch is disabled or not.',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    labelPosition: {
      description: 'Determines the position of the label, left or right.',
      options: ['left', 'right'],
      control: { type: 'radio' },
      table: { defaultValue: { summary: 'right' } },
    },
    tooltip: {
      description: 'tooltip options',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    tooltipText: {
      description: 'tooltip message',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
  },
  tags: ['autodocs'],
  render: (args) => ({
    template: `
      <form [formGroup]="form">
        <se-switch
          [id]="id"
          [title]="title"
          [subtitle]="subtitle"
          [label]="label"
          [labelPosition]="labelPosition"
          [disabled]="disabled"
          [tooltip]="tooltip"
          [tooltipText]="tooltipText"
          formControlName="value">
        </se-switch>
      </form>
    `,
    props: {
      ...args,
      form: new FormGroup({
        value: new FormControl(false),
      }),
    },
  }),
};

export default meta;
type Story = StoryObj<SwitchComponent>;

export const Switch: Story = {};
export const Disabled: Story = { args: { disabled: true } };
export const InnerHtml: Story = { args: { label: 'Este texto <b>tiene negrita</b>' } };
export const WithTooltip: Story = { args: { tooltip: true, tooltipText: "Esto es un tooltip" } };
export const WithSubtitle: Story = { args: { tooltip: true, subtitle: "Esto es un subtitulo" } };
