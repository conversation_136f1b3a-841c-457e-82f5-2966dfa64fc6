import { Meta, Typeset } from "@storybook/blocks";
import { Table } from "./components/table.mdx";

<Meta title="Core/Typography" />

export const createData = (prefix, values) =>
  values.map((value) => ({ variable: prefix + value, value: prefix + value }));

export const fontWeightsValues = [
  "thin",
  "extralight",
  "light",
  "regular",
  "medium",
  "semibold",
  "bold",
  "extrabold",
  "black",
];
export const textSizesValues = [
  "xs",
  "sm",
  "base",
  "md",
  "lg",
  "xl",
  "2xl",
  "3xl",
  "4xl",
];
export const lineHeightsValues = [
  "xs",
  "sm",
  "base",
  "md",
  "lg",
  "xl",
  "2xl",
  "3xl",
  "4xl",
];

export const fontWeightsData = createData("--font-", fontWeightsValues);
export const textSizeData = createData("--text-", textSizesValues);
export const lineHeightData = createData("--line-", lineHeightsValues);

export const typography = {
  type: {
    primary: "var(--font-primary)",
  },
  weight: {
    thin: "100",
    extralight: "200",
    light: "300",
    regular: "400",
    medium: "500",
    semibold: "600",
    bold: "700",
    extrabold: "800",
    black: "900",
  },
  size: {
    base: 16,
    xs: 12,
    sm: 14,
    md: 18,
    lg: 20,
    xl: 24,
    "2xl": 28,
    "3xl": 35,
    "4xl": 48,
  },
  line: {
    base: 24,
    xs: 16,
    sm: 20,
    md: 26,
    lg: 28,
    xl: 32,
    "2xl": 36,
    "3xl": 44,
    "4xl": 56,
  },
};

export const SampleText =
  "Lorem ipsum dolor sit amet, consectetur adipiscing elit.";

# Typography

## Heading

**Font:** Open Sans
**Weights:** 100 (thin), 200 (extralight), 300 (light), 400 (regular), 500 (medium), 600 (semibold), 700 (bold), 800 (extrabold), 900 (black)

<Typeset
  fontSizes={[
    Number(typography.size["3xl"]),
    Number(typography.size["2xl"]),
    Number(typography.size.xl),
    Number(typography.size.lg),
    Number(typography.size.sm),
    Number(typography.size.xs),
  ]}
  fontWeight={typography.weight.regular}
  sampleText={SampleText}
  fontFamily={typography.type.primary}
/>

## Text

Utilice el cuerpo del texto para presentar la mayor parte del contenido de una página. El tamaño del cuerpo se mantiene constante en todos los puntos de ruptura.

<Typeset
  fontSizes={[
    Number(typography.size.base),
    Number(typography.size.sm),
    Number(typography.size.md),
    Number(typography.size.lg),
    Number(typography.size.xl),
    Number(typography.size["2xl"]),
    Number(typography.size["3xl"]),
    Number(typography.size["4xl"]),
  ]}
  fontWeight={typography.weight.regular}
  sampleText={SampleText}
  fontFamily={typography.type.primary}
/>

# Typography Variables

## Text Sizes

<Table
  headers={["Variable", "Value"]}
  data={textSizeData}
  isCssVariables={true}
/>

## Line Heights

<Table
  headers={["Variable", "Value"]}
  data={lineHeightData}
  isCssVariables={true}
/>

## Font Weights

<Table
  headers={["Variable", "Value"]}
  data={fontWeightsData}
  isCssVariables={true}
/>
