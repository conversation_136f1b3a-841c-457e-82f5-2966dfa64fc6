import { EventEmitter, TemplateRef, Type } from '@angular/core';
import { ValidatorFn } from '@angular/forms';
import { MenuItem } from 'primeng/api';
import { SeButton } from '../../button/button.model';
import { SeDropdownOption } from '../../dropdown/dropdown.model';
import { SeTagTheme } from '../../tag';
import { Column } from '../columns/column.model';
import { FlattenedRow } from '../rows/rows.model';
import { CellComponentKeys } from './cell-component-registry';
import { MenuItemCommandEvent } from 'primeng/api/menuitem';
import { SeLink } from '../../link/link.model';

export type DeleteCallbackReturn = { delete: boolean; apply?: boolean };
export type EditCallbackReturn = {
  data: { [x: string]: any };
  apply?: boolean;
};
export type DownloadCallbackReturn = {
  data: { [x: string]: any };
  apply?: boolean;
};
export type ShowCallbackReturn = {
  data: { [x: string]: any };
  apply?: boolean;
};
export type ButtonCallbackReturn = {
  data: { [x: string]: any };
  apply?: boolean;
};
export type LinkCallbackReturn = {
  data: { [x: string]: any };
  apply?: boolean;
};

export interface FlattenedCell {
  rowData: any;
  column: Column;
  id: string;
  value: string;
  cellConfig: CellConfig;
  component: Type<CellComponent>;
}

export interface CellEvent {
  cell: FlattenedCell;
  cellName: string;
  type: CellEventTypes;
  data: CelleEventData;
}

export interface CelleEventData {
  apply?: boolean;
  newData: any;
  rowId: string;
  row?: FlattenedRow;
}

export enum CellEventTypes {
  DELETE_ROW = 'deleteRow',
  EDIT_ROW = 'editRow',
  CHECKBOX = 'checkboxChange',
  SHOW_ROW = 'showRow',
  DOWNLOAD_ROW = 'downloadRow',
  ACTION_BUTTON_ROW = 'actionButtonRow',
  LINK_ROW = 'linkRow',
}

export type CellConfig<T = {}> = {
  align?: 'left' | 'right' | 'center';
  tooltip?: boolean;
  tooltipText?: string | TemplateRef<HTMLElement> | undefined;
  ellipsis?: boolean;
  nowrap?: boolean;
  //CALBACKS
  deleteCallback?: (
    row: FlattenedRow,
    cell: FlattenedCell,
    column: Column
  ) => DeleteCallbackReturn | Promise<DeleteCallbackReturn>;
  editCallback?: (
    row: FlattenedRow,
    cell: FlattenedCell,
    column: Column
  ) => EditCallbackReturn | Promise<EditCallbackReturn>;
  downloadCallback?: (
    row: FlattenedRow,
    cell: FlattenedCell,
    column: Column
  ) => DownloadCallbackReturn | Promise<DownloadCallbackReturn>;
  showCallback?: (
    row: FlattenedRow,
    cell: FlattenedCell,
    column: Column
  ) => ShowCallbackReturn | Promise<ShowCallbackReturn>;
  buttonCell?: ButtonCellConfig;
  tagCell?: TagCellConfig;
  linkCell?: LinkCellConfig;
  [key: string]: any;
  cellComponentName?: CellComponentKeys;
} & T;

export interface CellComponent {
  templateRef?: TemplateRef<any>;
  cellConfig: CellConfig;
  value: any;
  cell: FlattenedCell;
  column: Column;
  row: FlattenedRow;
  onCellEvent?: EventEmitter<CellEvent>;
}

export interface ButtonCellConfig {
  buttonCallback: (
    row: FlattenedRow,
    cell: FlattenedCell,
    column: Column
  ) => ButtonCallbackReturn | Promise<ButtonCallbackReturn>;
  buttonConfig: SeButton;
}

export interface DateCellConfig extends StylableCellConfig {
  dateFormat?: string;
}

export interface TagCellConfig extends CellConfig {
  tagTheme: SeTagTheme;
}

export interface InputCellConfig extends CellConfig {
  readonly?: boolean;
  currencyMode?: boolean;
  currencySymbol?: string;
  decimals?: number;
  ariaLabel?: string;
  onBlurEvent?: boolean;
  validators?: ValidatorFn[];
  maxLength?: number;
}

export interface DropdownCellConfig extends CellConfig {
  id: string;
  label: string;
  options: SeDropdownOption[];
  optionLabel: string;
  optionValue: string;
  placeholder: string;
  showClear: boolean;
  autoSelect: boolean;
  editable: boolean;
  readOnly: boolean;
  formControlName: string;
  filter: boolean;
  disabled: boolean;
  validators?: ValidatorFn[];
  dropdownCallBack?: (
    row: FlattenedRow,
    cell: FlattenedCell,
    column: Column
  ) => void;
}

export interface DropdownButtonCellConfig extends CellConfig {
  dropdownButtonCell: {
    items: MenuItem[];
    buttonOptions: SeButton;
  }
}

export interface IconActionsCellConfig extends CellConfig {
  iconActions: {
    icons?: IconAction[];
    button?: SeButton;
    buttonActions?: MenuItem[];
  };
}

export interface IconAction {
  name: string;
  title: string;
  label: string; // para responsive se usará
  ariaLabel?: string;
  command?(row: any): void;
}

export interface LinkCellConfig {
  linkConfig: SeLink;
  linkCallback?: (
    row: FlattenedRow,
    cell: FlattenedCell,
    column: Column
  ) => LinkCallbackReturn | Promise<LinkCallbackReturn>;
}

export interface StylableCellConfig extends CellConfig {
  /**
   * Establece una o más propiedades de estilo, especificadas como parejas
   * de clave-valor separardas por dos puntos. La clave es un nombre de un
   * estilo, con un sufijo opcional `.<unidad>` (como 'top.px',
   * 'font-style.em'). El valor es una expresión a evaluar. El valor cuyo
   * resultado no sea nulo, expresado en la unidad especificada, se asigna
   * a la propiedad de estilo dada. Si el resultado de la evaluación es
   * `null`, la correspondiente propiedad se elimina.
   *
   * Ejemplo: Si `calculatedWidth` es `null`, la propiedad `max-width` no
   * se aplicará. Si es un número, se aplicará con la unidad `px`.
   *
   * ```typescript
   * cellConfig: {
   *   ngStyle: {
   *     color: 'red',
   *     'max-width.px': calculatedWidth,
   *   }
   * }
   * ```
   *
   * @see https://v16.angular.io/api/common/NgStyle
   */
  ngStyle?: Record<string, any>;
}


/**
 * @see https://v16.angular.io/api/common/DecimalPipe
 */
export interface NumberCellConfig extends StylableCellConfig {
  /**
   * La representación decimal del valor escrita en el siguiente formato:
   *
   * `{minDigitosEnteros}.{minDigitosFraccionarios}-{maxDigitosFraccionarios}`
   *
   * - `minDigitosEnteros`: Número mínimo de dígitos enteros. Por
   *    defecto: 1.
   * - `minDigitosFraccionarios`: Número mínimo de dígitos
   *    fraccionarios. Por defecto: 2.
   * - `maxDigitosFraccionarios`: Número máximo de dígitos fraccionarios.
   *    Por defecto: 2.
   *
   * Si el valor formateado se trunca será redondeado usando el método
   * del "más cercano".
   *
   * Ejemplo:
   *
   * ```html
   * {{3.6 | number: '1.0-0'}}
   * <!--mostrará '4'-->
   *
   * {{-3.6 | number: '1.0-0'}}
   * <!--mostrará '-4'-->
   * ```
   */
  digitsInfo?: string;
  /**
   * Determina las reglas de localización de formateo del valor. Determina
   * el tamaño de los grupos de miles y el separador, el caracter de
   * separación de decimales, y otras configuraciones específicas de
   * localización.
   *
   * Si no se especifica, por defecto se usará el valor de `LOCALE_ID`. Si
   * `LOCALE_ID` no está especificado, se usará la constante
   * `LOCALE_DEFAULT` de esta clase.
   *
   * @see https://v16.angular.io/guide/i18n-common-locale-id
   */
  locale?: string; // 'en-US', 'ca-ES', etc. Por defecto: 'ca-ES'.
}

/**
 * @see https://v16.angular.io/api/common/CurrencyPipe
 */
export interface CurrencyCellConfig extends NumberCellConfig {
  /**
   * El código {@link https://en.wikipedia.org/wiki/ISO_4217 ISO 4217},
   * como `USD` para dólar estadounidense y `EUR` para el euro.
   *
   * Opcional. Por defecto es `EUR`.
   */
  currencyCode?: string;
  /**
   * El formato para el indicador de moneda. Uno de los siguientes:
   *
   * - `'code'`: Mostrará el código de moneda (por ejemplo, `USD`).
   * - `'symbol'`: Mostrará el símbolo de moneda (por ejemplo, `$`).
   * - `'symbol-narrow'`: Utiliza este formato para localizaciones que
   *   tienen dos símbolos para su moneda. Por ejemplo, el dólar Canadiense
   *   (CAD) tiene el símbolo `$` y el símbolo `CA$`. Si la localización
   *   no tiene un símbolo _narrow_, se usará el símbolo estándar para la
   *   localización.
   *
   * Opcional. Por defecto es `'symbol'`.
   */
  display?: string;
}

export interface CellComponentConfig {
  cellComponent?: Type<CellComponent>;
  cellConfig?: CellConfig;
  cellComponentName?: CellComponentKeys;
}
