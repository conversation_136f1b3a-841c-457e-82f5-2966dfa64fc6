import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

/**
 * Masks: regexp
 * @description Export a series of predefined regexp
 */
export const masks = {
  nif: /^\d{8}[a-zA-Z]{1}$/,
  cif: /^([ABCDEFGHJKLMNPQRSUVW])(\d{7})([0-9A-J])$/,
  email:
    /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
  phone: /^[0-9]{9}$/,
  postalCode: /^(?:0[1-9]|[1-4]\d|5[0-2])\d{3}$/,
  any: /.*/,
  numbers: /^[0-9]*$/,
  letters: /^[a-zA-Z]*$/,
  registration: /^[a-zA-Z0-9_]{1,15}$/
};

/**
 * Validator: email
 * @description Validates if a field value has a valid email format
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorEmail: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  if (
    c.value &&
    c.value.toString().trim() !== '' &&
    !masks.email.test(c.value)
  ) {
    return { pattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.pattern' };
  }
  return null;
};

/**
 * Validator: phone
 * @description Validates if a field value has a valid phone number format
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorPhone: ValidatorFn | null = (
  c: AbstractControl
): ValidationErrors | null => {
  if (
    c.value &&
    c.value.toString().trim() !== '' &&
    !masks.phone.test(c.value)
  ) {
    return { pattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.pattern' };
  }
  return null;
};

/**
 * Validator: postal code
 * @description Validates if a field value has a valid postal code format
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorPostalCode: ValidatorFn | null = (
  c: AbstractControl
): ValidationErrors | null => {
  if (
    c.value &&
    c.value.toString().trim() !== '' &&
    !masks.postalCode.test(c.value)
  ) {
    return { pattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.pattern' };
  }
  return null;
};

/**
 * Validator: numbers
 * @description Validates if a field value has only numbers
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorNumbers: ValidatorFn | null = (
  c: AbstractControl
): ValidationErrors | null => {
  if (
    c.value &&
    c.value.toString().trim() !== '' &&
    !masks.numbers.test(c.value)
  ) {
    return { pattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.onlyNumbers' };
  }
  return null;
};

/**
 * Validator: letters
 * @description Validates if a field value has only letters
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorLetters: ValidatorFn | null = (
  c: AbstractControl
): ValidationErrors | null => {
  if (
    c.value &&
    c.value.toString().trim() !== '' &&
    !masks.letters.test(c.value)
  ) {
    return { pattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.onlyLetters' };
  }
  return null;
};

/**
 * Validator: no whitespaces
 * @description Validates if a field value has no whitespaces
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorNoWhitespaces: ValidatorFn | null = (
  c: AbstractControl
): ValidationErrors | null => {
  if ((c.value || '').match(/\s/g)) {
    return { pattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.noWhitespaces' };
  }
  return null;
};

/**
 * Validator: Registration plate
 * @description Validates if a field value has a registration code format
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorRegistrationPlate: ValidatorFn | null = (
  c: AbstractControl
): ValidationErrors | null => {
  if (
    c.value &&
    c.value.toString().trim() !== '' &&
    !masks.registration.test(c.value)
  ) {
    return { pattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.pattern' };
  }
  return null;
};
