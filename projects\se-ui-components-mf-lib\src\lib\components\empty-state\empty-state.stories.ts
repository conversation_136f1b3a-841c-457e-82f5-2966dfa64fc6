import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { EmptyStateComponent } from './empty-state.component';
import { SeEmptyStateModule } from './empty-state.module';

// More on how to set up stories at: https://storybook.js.org/docs/angular/writing-stories/introduction
const meta: Meta<EmptyStateComponent> = {
  title: 'Components/Empty State',
  component: EmptyStateComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [SeEmptyStateModule],
    }),
  ],
  args: {
    backgroundTheme: 'default',
    message: 'No se han encontrado valores',
    subMessage: 'Esto es un subtitulo',
    actionButton: { label: 'Aferir', btnTheme: 'secondary' },
    icon: 'search',
  },
  argTypes: {
    backgroundTheme: {
      description: 'Background theme',
      options: ['default', 'primary'],
      control: { type: 'select' },
      table: {
        defaultValue: { summary: 'default' },
      },
    },
    message: {
      description: 'Message desciption',
      control: { type: 'text' },
      table: {
        defaultValue: { summary: '' },
      },
    },
    subMessage: {
      description: 'SubMessage desciption',
      control: { type: 'text' },
      table: {
        defaultValue: { summary: '' },
      },
    },
    actionButton: {
      description: 'Button text',
      control: { type: 'object' },
      table: {
        defaultValue: { summary: '' },
      },
    },
    icon: {
      description: 'Icono empty',
      options: ['document', 'search', undefined],
      control: { type: 'select' },
      table: {
        defaultValue: { summary: undefined },
      },
    },
    actionButtonEvent: {
      action: 'close',
      description: 'Emitted when the button is clicked.',
      table: {
        type: {
          summary: 'EventEmitter<Event>',
        },
      },
    },
  },
  render: (args) => ({
    props: {
      ...args,
      actionButtonEvent: () => console.log('actionButtonEvent'),
    },

    template: `
      <se-empty-state
        [backgroundTheme]="backgroundTheme"
        [message]="message"
        [subMessage]="subMessage"
        [icon]="icon"
        [actionButton]="actionButton"
        (actionButtonEvent)="actionButtonEvent()"
      >
      </se-empty-state>
    `,
  }),
};

export default meta;
type Story = StoryObj<EmptyStateComponent>;

export const Default: Story = {
  args: {
    icon: undefined,
  },
};

export const ThemePrimary: Story = {
  args: {
    backgroundTheme: 'primary',
  },
};

export const emptySearch: Story = {
  args: {
    actionButton: { label: 'Aferir', btnTheme: 'primary' },
    icon: 'search',
  },
};

export const emptyDocument: Story = {
  args: {
    actionButton: { label: 'Aferir Documents', btnTheme: 'secondary' },
    icon: 'document',
  },
};

export const emptyInfo: Story = {
  args: {
    actionButton: { label: 'Aferir Documents', btnTheme: 'secondary' },
    icon: 'info',
  },
};
