import { Subscription } from 'rxjs';
import { Injectable } from '@angular/core';
import {
  L3Subscription,
  SeDynamicDependencies,
} from '../validations/validations.model';

@Injectable({ providedIn: 'root' })
export class SeSubscriptionManagerService {
  public subscriptions: L3Subscription = {};

  constructor() {}

  /**
   * Check existance
   * @description Check if a susbcription already exists
   */
  checkIfAlreadyExists(subscriberID: string, dependencyID: string): boolean {
    return this.subscriptions.hasOwnProperty(subscriberID) &&
      this.subscriptions[subscriberID].dependencies?.hasOwnProperty(
        dependencyID
      )
      ? true
      : false;
  }

  /**
   * Add susbcription
   * @description Add a new subscription
   */
  add(
    subscriberID: string,
    recievedSubscription: Subscription,
    dependencyFieldID: string,
    dependencies: SeDynamicDependencies
  ) {
    if (!this.subscriptions.hasOwnProperty(subscriberID)) {
      // Subscriber doesn't exist -> add subscriber & dependency
      this.subscriptions[subscriberID] = {
        subscription: recievedSubscription,
        dependencies: {
          [dependencyFieldID]: dependencies,
        },
      };
    } else if (
      subscriberID &&
      this.subscriptions?.hasOwnProperty(subscriberID)
    ) {
      this.subscriptions[subscriberID].dependencies =
        this.subscriptions[subscriberID]?.dependencies ?? {};
      if (
        !this.subscriptions[subscriberID].dependencies?.hasOwnProperty(
          dependencyFieldID
        )
      ) {
        this.subscriptions[subscriberID].dependencies[dependencyFieldID] =
          dependencies;
      }
    }
  }

  /**
   * Remove subscription:
   * If "id" -> Remove a specific subscription
   * If no "id" -> Remove all subscriptions
   * @descirption Remove all the subscriptions if an ID is not provided. Remove a specific
   */
  remove(subscriberID?: string) {
    if (this.subscriptions.hasOwnProperty(subscriberID as PropertyKey)) {
      this.subscriptions['subscriberID'].subscription.unsubscribe();
    }
    delete this.subscriptions['subscriberID'];
  }

  /**
   * Remove all subcriptions
   * @description Remove all subscriptions
   */
  removeAll() {
    for (const subscriber in this.subscriptions) {
      if (this.subscriptions.hasOwnProperty(subscriber)) {
        this.subscriptions[subscriber].subscription.unsubscribe();
        delete this.subscriptions[subscriber];
      }
    }
  }
}
