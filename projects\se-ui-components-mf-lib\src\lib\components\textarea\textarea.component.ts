import {Component, ElementRef, forwardRef, Input, Renderer2, ViewChild,} from '@angular/core';
import {ControlContainer, ControlValueAccessor, FormControl, FormGroup, NG_VALUE_ACCESSOR} from '@angular/forms';

@Component({
  selector: 'se-textarea',
  styleUrls: ['./textarea.component.scss'],
  template: `
    <div class="textarea-container">
      <label [for]="id">{{ label }}</label>
      <ng-container *ngIf="readonly; else textareaBlock">
        <p class="readonly-text">{{ value }}</p>
      </ng-container>
      <ng-template #textareaBlock>
        <div class="textarea-wrapper">
          <textarea
            [ngClass]="[ 
              disabled ? 'disabled' : '', 
              control && control.touched && control.status === 'INVALID' ? 'invalid' : '', 
              control && control.touched && control.status === 'VALID' ? 'valid' : ''
            ]"
            #textareaElement
            [attr.id]="id"
            [value]="value"
            [attr.maxlength]="maxlength"
            (input)="onInput($event)"
            (focus)="focus = true"
            (blur)="focus = false"
            [readonly]="readonly"
            [disabled]="disabled"
          ></textarea>
          <button
            #clearButton
            [hidden]="!value"
            class="clear-button"
            (click)="clear()"
            title="Clear"
          >
            &#10005;
          </button>
          <div #successCheck class="success-icon">
            <ng-icon *ngIf="control && control.touched && control.status === 'VALID' && !readonly" name="matCheckOutline"></ng-icon>
          </div>
        </div>
        <se-error-message *ngIf="control" [control]="control"></se-error-message>
      </ng-template>
    </div>
  `,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TextareaComponent),
      multi: true,
    },
  ],
})
export class TextareaComponent implements ControlValueAccessor {
  @ViewChild('clearButton') clearButton!: ElementRef;
  @ViewChild('successCheck') successCheck!: ElementRef;
  @ViewChild('textareaElement') textareaElement!: ElementRef;

  @Input() label: string = '';
  @Input() id: string = '';
  @Input() maxlength!: number;
  @Input() readonly: boolean = false;
  @Input() formControlName!: string;

  @Input() set disabled(value: boolean) {
    this.setDisabledState(value);
    if(!value) {
      this.getFormControl()?.enable({ emitEvent: true });
    } else {
      this.getFormControl()?.disable({ emitEvent: true });
    }
  }

  get disabled() { return this._disabled };
  
  value: string = '';
  control!: FormControl;
  focus: boolean = false;

  private _disabled: boolean = false;

  constructor(private renderer: Renderer2, private controlContainer: ControlContainer) {
  }

  ngOnInit() {
    this.control = this.getFormControl();

    this._disabled = this.control.disabled || this.disabled;

    this.control.registerOnDisabledChange((value) => { this.setDisabledState(value) })
  }

  ngAfterViewInit() {
    if(this.textareaElement?.nativeElement) {
      const ro = new ResizeObserver(() => {
        this.positionClearButton();
        this.positionSuccessButton();
      });
  
      ro.observe(this.textareaElement?.nativeElement);
      this.control = this.getFormControl();
    }
  }

  positionClearButton() {
    const top = this.textareaElement.nativeElement.offsetTop + 5;
    const isScrollShown = this.textareaElement.nativeElement.clientHeight < this.textareaElement.nativeElement.scrollHeight;
    const leftPx = isScrollShown ? 40 : 28;
    const left = this.textareaElement.nativeElement.offsetWidth - leftPx;
    
    this.renderer.setStyle(this.clearButton.nativeElement, 'top', `${top}px`);
    this.renderer.setStyle(this.clearButton.nativeElement, 'left', `${left}px`);
  }

  positionSuccessButton() {
    const bottom = this.textareaElement.nativeElement.offsetPosition = 'bottom 5px';
    const isScrollShown = this.textareaElement.nativeElement.clientHeight < this.textareaElement.nativeElement.scrollHeight;
    const leftPx = isScrollShown ? 38 : 26;
    const left = this.textareaElement.nativeElement.offsetWidth - leftPx;
    
    this.renderer.setStyle(this.successCheck.nativeElement, 'bottom', `${bottom}px`);
    this.renderer.setStyle(this.successCheck.nativeElement, 'left', `${left}px`);
  }

  private onChange: (value: any) => void = () => {
  };
  private onTouched: () => void = () => {
  };

  onInput(event: Event) {
    const value = (event.target as HTMLTextAreaElement).value;
    this.value = value;
    this.onChange(value);
    this.onTouched();
  }

  clear() {
    this.value = '';
    this.textareaElement.nativeElement.value = '';
    this.onChange(this.value);
    this.onTouched();
  }

  writeValue(value: any): void {
    this.value = value;
  }

  registerOnChange(fn: (value: any) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this._disabled = isDisabled;
  }

  private getFormControl() : FormControl {
    if(this.control){
      return this.control;
    }

    return (this.controlContainer.control as FormGroup).get(
      this.formControlName
    ) as FormControl;
  }
}
