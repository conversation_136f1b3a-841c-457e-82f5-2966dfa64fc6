import { Component } from "@angular/core";

@Component({
  selector: 'svg-empty-info',
  template: `<svg width="82" height="51" viewBox="0 0 82 51" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M78.4997 8.08789C80.3948 8.08789 81.9311 9.62417 81.9311 11.5193C81.9311 13.4144 80.3948 14.9506 78.4997 14.9506H58.8919C60.787 14.9506 62.3233 16.4869 62.3233 18.382C62.3233 20.2771 60.787 21.8134 58.8919 21.8134H69.6762C71.5713 21.8134 73.1076 23.3497 73.1076 25.2448C73.1076 27.1398 71.5713 28.6761 69.6762 28.6761H64.689C62.2995 28.6761 60.3625 30.2124 60.3625 32.1075C60.3625 33.3709 61.3429 34.5147 63.3037 35.5389C65.1987 35.5389 66.735 37.0751 66.735 38.9702C66.735 40.8653 65.1987 42.4016 63.3037 42.4016H22.6174C20.7223 42.4016 19.186 40.8653 19.186 38.9702C19.186 37.0751 20.7223 35.5389 22.6174 35.5389H3.49973C1.60464 35.5389 0.0683594 34.0026 0.0683594 32.1075C0.0683594 30.2124 1.60464 28.6761 3.49973 28.6761H23.1076C25.0027 28.6761 26.5389 27.1398 26.5389 25.2448C26.5389 23.3497 25.0027 21.8134 23.1076 21.8134H10.8527C8.95758 21.8134 7.4213 20.2771 7.4213 18.382C7.4213 16.4869 8.95758 14.9506 10.8527 14.9506H30.4605C28.5654 14.9506 27.0291 13.4144 27.0291 11.5193C27.0291 9.62417 28.5654 8.08789 30.4605 8.08789H78.4997ZM78.4997 21.8134C80.3948 21.8134 81.9311 23.3497 81.9311 25.2448C81.9311 27.1398 80.3948 28.6761 78.4997 28.6761C76.6046 28.6761 75.0684 27.1398 75.0684 25.2448C75.0684 23.3497 76.6046 21.8134 78.4997 21.8134Z" fill="#CBE3F4"/>
  <circle cx="40.9998" cy="26.7615" r="17.4138" fill="#F6FAFE"/>
  <path d="M41.1064 36.8226C41.5843 36.8226 41.983 36.6615 42.3024 36.3393C42.6219 36.0171 42.7816 35.6178 42.7816 35.1415V26.3997C42.7816 25.9234 42.6199 25.5241 42.2965 25.2019C41.9732 24.8797 41.5725 24.7186 41.0946 24.7186C40.6166 24.7186 40.2179 24.8797 39.8985 25.2019C39.5791 25.5241 39.4194 25.9234 39.4194 26.3997V35.1415C39.4194 35.6178 39.581 36.0171 39.9044 36.3393C40.2277 36.6615 40.6284 36.8226 41.1064 36.8226ZM40.999 20.8856C41.5037 20.8856 41.927 20.7181 42.2689 20.383C42.6107 20.0479 42.7816 19.6327 42.7816 19.1373C42.7816 18.6229 42.6108 18.1917 42.2694 17.8437C41.928 17.4957 41.5049 17.3217 41.0002 17.3217C40.4955 17.3217 40.0722 17.4957 39.7303 17.8437C39.3885 18.1917 39.2176 18.6229 39.2176 19.1373C39.2176 19.6327 39.3883 20.0479 39.7298 20.383C40.0712 20.7181 40.4943 20.8856 40.999 20.8856ZM41.0085 46.9093C38.2238 46.9093 35.6069 46.3798 33.1577 45.3207C30.7085 44.2616 28.5705 42.8186 26.7437 40.9918C24.9169 39.165 23.4739 37.0261 22.4148 34.5753C21.3557 32.1244 20.8262 29.5057 20.8262 26.7191C20.8262 23.9325 21.3557 21.3138 22.4148 18.8629C23.4739 16.4121 24.9169 14.2788 26.7437 12.4632C28.5705 10.6476 30.7094 9.21026 33.1602 8.15116C35.6111 7.09205 38.2298 6.5625 41.0164 6.5625C43.803 6.5625 46.4217 7.09205 48.8726 8.15116C51.3234 9.21026 53.4567 10.6476 55.2723 12.4632C57.0879 14.2788 58.5252 16.4139 59.5844 18.8683C60.6435 21.3227 61.173 23.9423 61.173 26.727C61.173 29.5117 60.6435 32.1286 59.5844 34.5778C58.5252 37.027 57.0879 39.1629 55.2723 40.9856C53.4567 42.8083 51.3217 44.2512 48.8672 45.3145C46.4128 46.3777 43.7932 46.9093 41.0085 46.9093ZM41.0164 43.5471C45.6899 43.5471 49.6574 41.9108 52.9188 38.6382C56.1801 35.3657 57.8108 31.3926 57.8108 26.7191C57.8108 22.0456 56.1822 18.0782 52.9251 14.8168C49.6679 11.5554 45.6927 9.9247 40.9996 9.9247C36.3373 9.9247 32.3698 11.5533 29.0973 14.8105C25.8247 18.0677 24.1884 22.0428 24.1884 26.7359C24.1884 31.3982 25.8247 35.3657 29.0973 38.6382C32.3698 41.9108 36.3429 43.5471 41.0164 43.5471Z" fill="#106BC4"/>
  </svg>`

})
export class SvgEmptyInfoComponent { }