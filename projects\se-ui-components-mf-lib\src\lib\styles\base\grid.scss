:root {
  --margin-xs: 0.5rem; /* 8px */
  --margin-sm: 10px; 
  --margin-base: 1rem;  /* 16px */
  --margin-md: 20px;
  --margin-lg: 1.5rem; /* 24px */
  --margin-xl: 2rem; /* 32px */

  --padding-xs: 0.5rem; /* 8px */
  --padding-sm: 10px; 
  --padding-base: 1rem;  /* 16px */
  --padding-md: 20px;
  --padding-lg: 1.5rem; /* 24px */
  --padding-xl: 2rem; /* 32px */
}

/* You can add global styles to this file, and also import other style files */
// RESPONSIVE
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

// @media (max-width: 576px) {
@include media-breakpoint-down(sm) {
  .w-xs-100 {
    width: 100%;
  }
}
