import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { Observable, firstValueFrom } from 'rxjs';
import { merge } from 'rxjs/internal/observable/merge';
import { map } from 'rxjs/operators';
import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import { CellEventService } from '../cell-event.service';
import { CellComponent, CellConfig, CellEventTypes, FlattenedCell } from '../cells.model';
import { SeModalService } from '../../../modal/modal.service';

@Component({
  selector: 'actions-cell',
  template: `
    <div class="d-flex justify-content-start justify-content-md-end gap-2">
      <button
        *ngIf="hasDownload"
        class="action-button"
        [title]="'UI_COMPONENTS.BUTTONS.DOWNLOAD' | translate"
        (click)="onDownload()">
          <ng-icon name="matFileDownloadOutline"></ng-icon>
      </button>
      <button
        *ngIf="hasEdit"
        class="action-button"
        [title]="'UI_COMPONENTS.BUTTONS.EDIT' | translate"
        (click)="onEdit()">
        <ng-icon name="matEditOutline"></ng-icon>
      </button>
      <button
      *ngIf="hasEditWorkingSession"
        class="action-button"
        [title]="'UI_COMPONENTS.BUTTONS.EDIT_WORKING_SESSION' | translate"
        (click)="onEdit()">
        <ng-icon name="matEditNoteOutline"></ng-icon>
      </button>
      <button
      *ngIf="hasDelete"
        class="action-button"
        [title]="'UI_COMPONENTS.BUTTONS.DELETE' | translate"
        (click)="onDelete()">
        <ng-icon name="matDeleteOutline"></ng-icon>
      </button>
      <button
      *ngIf="hasShow"
        class="action-button"
        [title]="'UI_COMPONENTS.BUTTONS.SHOW' | translate"
        (click)="onShow()">
        <ng-icon name="matVisibilityOutline"></ng-icon>
      </button>
    </div>
  `,
  styleUrls: ['../styles/actions-cell-template.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActionsCellComponent implements CellComponent, OnInit {
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

  hasDownload: boolean = false;
  hasEdit: boolean = false;
  hasEditWorkingSession: boolean = false;
  hasConfirmation: boolean = true;
  hasDelete: boolean = true;
  hasShow: boolean = false;

  constructor(
    private cellEventService: CellEventService,
    private modalService: SeModalService,
  ) { }

  ngOnInit() { 
    this.hasDownload = this.row?.rowConfig['hasDownload'] ?? this.cellConfig['hasDownload']  ?? false;    
    this.hasEdit = this.row?.rowConfig['hasEdit'] ?? this.cellConfig['hasEdit'] ?? false;
    this.hasEditWorkingSession =  this.row?.rowConfig['hasEditWorkingSession'] ?? this.cellConfig['hasEditWorkingSession'] ?? false;
    this.hasConfirmation =  this.row?.rowConfig['hasConfirmation'] ?? this.cellConfig['hasConfirmation'] ?? true;
    this.hasDelete = this.row?.rowConfig['hasDelete'] ?? this.cellConfig['hasDelete'] ?? true;
    this.hasShow =  this.row?.rowConfig['hasShow'] ?? this.cellConfig['hasShow'] ?? false;
  }

  async onDownload() {
    if (!this.cellConfig.downloadCallback)
      return this.throwEvent(CellEventTypes.DOWNLOAD_ROW, 'actionsCellComponent');
    await this.cellConfig.downloadCallback(this.row, this.cell, this.column);
  }

  async onEdit() {
    if (!this.cellConfig.editCallback) return this.throwEvent(CellEventTypes.EDIT_ROW, 'editCellComponent');
    const { data, apply } = await this.cellConfig.editCallback(this.row, this.cell, this.column);

    if (data) {
      this.throwEvent(CellEventTypes.EDIT_ROW, 'editCellComponent', { newData: data, rowId: this.row.id }, apply);
    }
  }


  async onDelete() {
    let confirmation = this.hasConfirmation ? await this.getConfirmation() : true;

    if (confirmation) await this.processDelete();
  }

  async processDelete(): Promise<void> {
    if (!this.cellConfig.deleteCallback) return this.throwEvent(CellEventTypes.DELETE_ROW, 'actionsCellComponent', { row: this.row }, true);
    const { delete: remove, apply } = await this.cellConfig.deleteCallback(this.row, this.cell, this.column);

    if (remove) {
      this.throwEvent(CellEventTypes.DELETE_ROW, 'actionsCellComponent', { row: this.row }, apply);
    }
  }

  async onShow() {
    if (!this.cellConfig.showCallback)
      return this.throwEvent(CellEventTypes.SHOW_ROW, 'actionsCellComponent');
    await this.cellConfig.showCallback(this.row, this.cell, this.column);
  }

  private async getConfirmation(): Promise<boolean> {

    const modalRef = this.modalService.openModal({
      severity: 'warning',
      hideIcon: false,
      closable: true,
      closableLabel: 'UI_COMPONENTS.BUTTONS.CONTINUE',
      secondaryButton: true,
      secondaryButtonLabel: 'UI_COMPONENTS.BUTTONS.CANCEL',
      title: 'UI_COMPONENTS.DELETE_MODAL.DELETE_DOCUMENT_TITLE',
      subtitle: 'UI_COMPONENTS.DELETE_MODAL.DELETE_DOCUMENT_SUBTITLE',
    });
    const confirmation$: Observable<boolean> = merge(
      modalRef?.componentInstance?.modalSecondaryButtonEvent.pipe(
        map(() => false)
      ),
      modalRef?.componentInstance?.modalOutputEvent.pipe(map(() => true))
    ) as Observable<boolean>;
    const response = await firstValueFrom(confirmation$);
    modalRef?.close();
    return response;
  }

  private throwEvent(
    type: CellEventTypes,
    cellName: string,
    data?: any,
    apply = false
  ) {
    this.cellEventService.emitEvent({
      type,
      cellName,
      cell: this.cell,
      data: data ? { ...data, apply } : { apply },
    });
  }
}
