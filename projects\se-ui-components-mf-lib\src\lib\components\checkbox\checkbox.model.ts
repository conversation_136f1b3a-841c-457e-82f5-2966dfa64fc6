import { TemplateRef } from "@angular/core";

export interface SeCheckbox {
    label?: string;
    id: string;
    formControlName?: string | number | null;
    tooltip?: boolean;
    tooltipText?: string | TemplateRef<HTMLElement>;
    binary?: boolean;
    readonly?: boolean;
    value: any;
    tooltipClass?: string;
    tooltipAriaLabel?: string;
    tooltipEvent?: 'hover' | 'focus' | 'hover-focus';
    tooltipPositionLeft?: number;
    tooltipPositionTop?: number;
    ariaLabel?: string;
    styleClass?: string;
    disabled?: boolean;
}