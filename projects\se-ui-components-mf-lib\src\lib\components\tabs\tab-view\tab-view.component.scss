.se-tab-view{

  &--header {
    display: flex;
    position: relative;

    &--button {
      color: var(--color-primary-action);
      border: none;
      background: transparent;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      text-decoration: none;
      height: 40px;
      min-width: 40px;
      position: absolute;

      &.right-paddle {
        right: 0;
      }
    }
  }

  .tab-header {
    display: flex;
    overflow: hidden;
    scroll-behavior: smooth;

    &.left-paddle {
      margin-left: 40px;
    }

    &.right-paddle {
      margin-right: 40px;
    }
  }

  .tab-content {
    padding: 8px;
  }
}
