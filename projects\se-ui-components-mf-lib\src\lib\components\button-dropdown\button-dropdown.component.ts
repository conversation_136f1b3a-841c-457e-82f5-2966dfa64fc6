import { Component, Input } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { SeButton } from '../button';

@Component({
  selector: 'se-button-dropdown',
  template: `
    <div class="se-button-dropdown" #se_button_dropdown>
      <p-menu #se_button_dropdown_menu [model]="items" [popup]="true" [appendTo]="se_button_dropdown">
        <ng-template pTemplate="item" let-item>
          <a class="p-menuitem-link flex justify-content-between align-items-center p-3">
            <div>
              <ng-icon *ngIf="item.icon" [name]="item.icon"></ng-icon>
              <span> {{ item.label }}</span>
            </div>
          </a>
        </ng-template>
      </p-menu>
      <se-button
        #button
        [size]="buttonOptions?.size ?? 'default'"
        [btnTheme]="buttonOptions?.btnTheme ?? 'secondary'"
        [disabled]="!!buttonOptions?.disabled"
        [icon]="buttonOptions?.icon ?? ''"
        [title]="buttonOptions?.title ?? ''"
        [iconSize]="buttonOptions?.iconSize ?? ''"
        [iconPosition]="buttonOptions?.iconPosition ?? 'left'"
        (onClick)="se_button_dropdown_menu.toggle($event)"
        [ariaLabel]="buttonOptions?.ariaLabel ?? '' | translate"
        >
        <ng-content></ng-content>
      </se-button>
    </div>
  `,
  styleUrls: ['./button-dropdown.component.scss']
})

export class ButtonDropdownComponent{

  @Input() items: MenuItem[] | undefined;
  @Input() buttonOptions: SeButton | undefined;
}
