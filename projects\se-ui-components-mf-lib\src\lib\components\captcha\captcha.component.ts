import { Observable, Subject, takeUntil } from 'rxjs';
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
  ViewChild,
  AfterViewInit,
  ChangeDetectionStrategy,
  ViewEncapsulation,
  ElementRef,
  NgZone,
  ChangeDetectorRef,
  OnInit,
} from '@angular/core';
import { ComponentEventOutput } from './captcha.model';
import { Nullable } from '../../models';

const CAPCHA_KEY = '6LckXxgmAAAAAPsZGY8M-83K1VGyUzfMS9oSYasN';

@Component({
  selector: 'se-captcha',
  template: `
      <div
        #captchaComponent
        class="captcha-container"
        [class]="containerClass"
      ></div>
    `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class CaptchaComponent implements OnInit, AfterViewInit, On<PERSON><PERSON>roy {
  private firstLoadComponent = true;

  private instance: any = null;

  private captchaMethod: any;

  @ViewChild('captchaComponent') captchaComponent: ElementRef | undefined;

  /* Component inputs */

  // Public sitekey.
  @Input() captchaPublicApiKey: string = CAPCHA_KEY;

  @Input() size: 'normal' | 'compact' = 'normal';

  @Input() set reset(value: boolean) {
    if (value) {
      this.resetCaptcha();
    }
    // call setCaptchaAriaLabel() each time reset changes
    // setTimeout(0) sends the callback at the end of the callstack
    if (!this.firstLoadComponent)
      setTimeout(() => this.setCaptchaAriaLabel(), 0);
  }

  @Input() set reset$(value: Observable<boolean> | undefined){
    if (value) {
      value
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((reset: boolean) => {
          if (reset) {
            this.resetCaptcha();
          }
          // call setCaptchaAriaLabel() each time reset changes
          // setTimeout(0) sends the callback at the end of the callstack
          if (!this.firstLoadComponent)
            setTimeout(() => this.setCaptchaAriaLabel(), 0);
        });
    }
  };

  @Input() theme = 'light';

  @Input() containerClass: string = '';

  @Input() type = 'image';

  @Input() tabindex = 0;

  @Input() enterprise: boolean = false;

  @Input() initCallback = 'initRecaptcha';

  @Input() language: string = 'en';

  /* Component outputs */
  // The callback function to be executed when the user submits a successful CAPTCHA response.
  @Output() responseEvent: EventEmitter<ComponentEventOutput> =
    new EventEmitter<ComponentEventOutput>();

  // The callback function to be executed when the recaptcha response expires and the user needs to solve a new CAPTCHA.
  @Output() expireEvent: EventEmitter<void> = new EventEmitter<void>();

  private unsubscribe: Subject<void> = new Subject<void>();

  constructor(
    public el: ElementRef,
    public _zone: NgZone,
    public cd: ChangeDetectorRef,
  ) { }

  ngOnInit(): void {
    this.captchaMethod = this.enterprise
      ? (<any>window).grecaptcha?.enterprise
      : (<any>window).grecaptcha;
  }

  ngAfterViewInit(): void {
    if ((<any>window).grecaptcha) {
      if (!this.captchaMethod?.render) {
        setTimeout(() => {
          this.init();
        }, 100);
      } else {
        this.init();
      }
    } else {
      (<any>window)[this.initCallback] = (): void => {
        this.init();
      };
    }
    setTimeout(() => this.setCaptchaAriaLabel(), 100);
  }

  ngOnDestroy(): void {
    if (this.instance !== null) {
      this.captchaMethod?.reset(this.instance);
    }

    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  init(): void {
    try {
      this.instance = this.captchaMethod?.render(
        this.el.nativeElement.children[0],
        {
          sitekey: this.captchaPublicApiKey,
          theme: this.theme,
          type: this.type,
          size: this.size,
          tabindex: this.tabindex,
          hl: this.language,
          callback: (response: string) => {
            this._zone.run(() => this.recaptchaCallback(response));
          },
          'expired-callback': () => {
            this._zone.run(() => this.recaptchaExpiredCallback());
          },
        },
      );
    } catch (error) {
      console.error(error);
    }
  }

  resetCaptcha(): void {
    if (this.instance === null) return;

    this.captchaMethod?.reset(this.instance);
    this.recaptchaCallback(null);
    this.cd.markForCheck();
  }

  getResponse(): string | null {
    if (this.instance === null) return null;

    return this.captchaMethod?.getResponse(this.instance);
  }

  recaptchaCallback(response: Nullable<string>): void {
    this.responseEvent.emit({
      componentId: 'captcha',
      browserEvent: null,
      componentValue: response,
    });
  }

  recaptchaExpiredCallback(): void {
    this.expireEvent.emit();
  }

  private setCaptchaAriaLabel(): void {
    if (this.firstLoadComponent) this.firstLoadComponent = false;

    const captchaInput = this.captchaComponent?.nativeElement.querySelector(
      '.captcha-container #g-recaptcha-response',
    );
    if (captchaInput) captchaInput.setAttribute('aria-label', 'Captcha');
  }
}
