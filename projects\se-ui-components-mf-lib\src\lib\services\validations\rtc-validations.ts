import {
  AbstractControl,
  FormGroup,
  UntypedFormControl,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';

/**
 * Masks: regexp
 * @description Export a series of predefined regexp
 */
export const rtcMasks = {
  rtc1: /^[A-Z]{2,5}$/,
  rtc1Provisional: /^(CP)$/,
  rtc2: /^\d{6}$/,
  rtc3: /^\d{1,2}$/,
};

/**
 * Validator: RTC (field 1 /rtc1)
 * @description Validates if the first field of the RTC has a valid value
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorRtc1: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  if (c.value && c.value.toString().trim() !== '' && !rtcMasks.rtc1.test(c.value))
    return { rtc: 'UI_COMPONENTS.VALIDATIONS_ERRORS.rtc' };

  return null;
};

/**
 * Validator: RTC (field 2 /rtc2)
 * @description Validates if the first field of the RTC has a valid value
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorRtc2: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  if (c.value && c.value.toString().trim() !== '' && !rtcMasks.rtc2.test(c.value))
    return { rtc: 'UI_COMPONENTS.VALIDATIONS_ERRORS.rtc' };

  return null;
};

/**
 * Validator: RTC (field 3 /rtc3)
 * @description Validates if the first field of the RTC has a valid value
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorRtc3: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  if (c.value && c.value.toString().trim() !== '' && !rtcMasks.rtc3.test(c.value))
    return { rtc: 'UI_COMPONENTS.VALIDATIONS_ERRORS.rtc' };

  return null;
};

/**
 * Validator: RTC
 * @description Validates if the first field of the RTC has a valid value
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorRtc: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  if (
    (c.value?.rtc1 || c.value?.rtc2 || c.value?.rtc3) &&
    (!rtcMasks.rtc1.test(c.value.rtc1) ||
      !rtcMasks.rtc2.test(c.value.rtc2) ||
      !rtcMasks.rtc3.test(c.value.rtc3))
  ) {
    return { rtc: 'UI_COMPONENTS.VALIDATIONS_ERRORS.rtc' };
  }

  return null;
};

/**
 * Validator: RTC (field 1 /rtc1)
 * @description Validates if the first field of the RTC has a valid value
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorRtc1Provisional: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  if (
    c.value &&
    c.value.toString().trim() !== '' &&
    !rtcMasks.rtc1Provisional.test(c.value)
  )
    return {
      rtcProvisional: 'UI_COMPONENTS.VALIDATIONS_ERRORS.rtcProvisional',
    };

  return null;
};

/**
 * Validator: RTC (field 2 /rtc2)
 * @description Validates if the first field of the RTC has a valid value
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorRtc2Provisional: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  if (c.value && c.value.toString().trim() !== '' && !rtcMasks.rtc2.test(c.value))
    return {
      rtcProvisional: 'UI_COMPONENTS.VALIDATIONS_ERRORS.rtcProvisional',
    };

  return null;
};

/**
 * Validator: RTC (field 3 /rtc3)
 * @description Validates if the first field of the RTC has a valid value
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorRtc3Provisional: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  if (c.value && c.value.toString().trim() !== '' && !rtcMasks.rtc3.test(c.value))
    return {
      rtcProvisional: 'UI_COMPONENTS.VALIDATIONS_ERRORS.rtcProvisional',
    };

  return null;
};

/**
 * Validator: RTC
 * @description Validates if the first field of the RTC has a valid value
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorRtcProvisional: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  if (
    (c.value?.rtc1 || c.value?.rtc2 || c.value?.rtc3) &&
    (!rtcMasks.rtc1Provisional.test(c.value.rtc1) ||
      !rtcMasks.rtc2.test(c.value.rtc2) ||
      !rtcMasks.rtc3.test(c.value.rtc3))
  ) {
    return {
      rtcProvisional: 'UI_COMPONENTS.VALIDATIONS_ERRORS.rtcProvisional',
    };
  }

  return null;
};

// Merge external form & internal form errors
const setErrors = (
  externalFormErrors: ValidationErrors,
  internalFormErrors: ValidationErrors
): ValidationErrors | null => {
  let errors: ValidationErrors | null;

  if (externalFormErrors && !internalFormErrors) {
    errors = externalFormErrors;
  } else if (!externalFormErrors && internalFormErrors) {
    errors = internalFormErrors;
  } else if (externalFormErrors && internalFormErrors) {
    errors = { ...externalFormErrors, ...internalFormErrors };
  } else {
    errors = null;
  }

  return errors;
};

/**
 * Validator: IBAN internal form
 * @description Validates the IBAN component internal form.
 * @returns {ValidatorFn} Returns a validaton error
 */
export const validatorRtcInternalForm = (
  externalFormValidations: ValidatorFn[]
): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    const internalForm = control as FormGroup;

    // Set a temporary form control with the internalForm values and the externalForm validations
    const tmpFormControl = new UntypedFormControl(
      internalForm.value,
      externalFormValidations
    );

    ['rtc1', 'rtc2', 'rtc3'].forEach((rtc) => {
      // WARNING! Access private properties can lead to future errors in case the developers deprectate the private methods
      // TODO: Replace the _rawValidatorsErrors
      const _rawValidatorsErrors = new UntypedFormControl(
        internalForm.get(rtc)!.value,
        (internalForm.get(rtc)! as any)._rawValidators
      ).errors;
      const errors1 = tmpFormControl.errors;
      const errors2 = new UntypedFormControl(
        internalForm.get(rtc)!.value,
        _rawValidatorsErrors
      );

      if (errors1 || errors2) {
        internalForm
          .get(rtc)!
          .setErrors(setErrors(errors1 || {}, errors2 || {}));
      }
    });

    return null;
  };
};
