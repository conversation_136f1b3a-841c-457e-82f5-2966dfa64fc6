import '@angular/common/locales/global/ca';
import { LOCALE_ID } from '@angular/core';
import {
  applicationConfig,
  Meta,
  moduleMetadata,
  StoryObj,
} from '@storybook/angular';

import { provideAnimations } from '@angular/platform-browser/animations';
import { CellEvent, FlattenedCell } from './cells/cells.model';
import { Column } from './columns/column.model';
import { FlattenedRow } from './rows/rows.model';
import { TableComponent } from './table.component';
import { GroupRow, Row, TableMobileLayoutMode } from './table.model';
import { SeTableModule } from './table.module';
import { SeButton, SeButtonSizeEnum, SeButtonThemeEnum } from '..';

/**
 * Genera un número entero aleatorio entre un valor mínimo y uno máximo.
 *
 * @param min Valor mínimo.
 * @param max Valor máximo
 * @returns Un número entero aleatorio situado entre dos valores específicos.
 * El valor devuelto será mayor o igual que `min` y menor que `max`.
 */
function getRandomInt(min: number, max: number): number {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min) + min);
}

function getRandomColor(): string {
  const colors = ['red', 'fuchsia', 'lime', 'blue', 'teal', 'chocolate'];
  return colors[getRandomInt(0, colors.length)];
}

const generateRow = (
  name: string,
  age: number,
  isDisabled = false,
  keyGroup: string = ''
): Row => {
  return {
    data: {
      name: { value: name },
      age: { value: age, cellConfig: { align: 'right' } },
      input: { value: 50 },
      tag: {
        value: 'TCJ',
        cellConfig: {
          tooltip: true,
          tooltipText: 'Texto de tooltip de una etiqueta',
          tagCell: { tagTheme: 'pink' },
        },
      },
      date: { value: new Date() },
      currency: { value: Math.random() * 10000 },
      longText: {
        value:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec condimentum tempus blandit.',
      },
      styledText: {
        value: 'abcDEF',
        cellComponentName: 'styledTextCellComponent',
        cellConfig: { ngStyle: { color: getRandomColor() } },
      },
      docType: { value: 1 },
      number: { value: Math.random() * 1000 },
      icon: {
        value: 'Icon cell',
        cellConfig: {
          iconName: 'matInfoSharp',
          titleIcon: 'title icon',
          iconStyle: {
            color: getRandomColor(),
        },
      },
        cellComponentName: 'iconCellComponent',
      },
      link: {
        value: 'https://www.google.com',
        cellConfig: {
          tooltip: true,
          ellipsis: true,
          linkCell: {
            linkConfig: {
              label: 'linkaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
              ariaLabel: 'Link to Google',
              target: '_blank',
              size: 'regular',
              linkTheme: 'secondary',
              disabled: false,
            },
            linkCallback: () => {
              return new Promise(() => {
                alert('Link clicked');
              });
            },
          },
        },
      },
    },
    rowConfig: {
      isDisabled: isDisabled,
      hasDelete: Math.random() > 0.5,
      hasDownload: Math.random() > 0.5,
      hasConfirmation: Math.random() > 0.5,
      hasShow: Math.random() > 0.5,
      hasEdit: Math.random() > 0.5,
    },
    keyGroup,
  };
};

const generatGroupedRow = (
  key: string,
  name: string,
  isCollapsed: boolean
): GroupRow => {
  return {
    key,
    name,
    isCollapsed,
  };
};

const downloadButton: SeButton = {
  btnTheme: SeButtonThemeEnum.SECONDARY,
  disabled: false,
  icon: 'matFileDownloadOutline',
  iconSize: '20px',
  size: SeButtonSizeEnum.SMALL,
};

const meta: Meta = {
  title: 'Components/Table',
  component: TableComponent,
  decorators: [
    moduleMetadata({
      imports: [SeTableModule],
      providers: [{ provide: LOCALE_ID, useValue: 'ca-ES' }],
    }),
    applicationConfig({
      providers: [provideAnimations()],
    }),
  ],
  args: {
    cellTemplatePriorityOrder: 'row-column-cell',
    resizable: true,
    columns: [
      {
        header: 'Name',
        key: 'name',
        resizable: true,
        size: 10,
        sortable: true,
      },
      {
        header: 'Age',
        key: 'age',
        tooltip: true,
        tooltipText: 'Lorem Itsum',
        resizable: true,
        size: 10,
        cellConfig: { nowrap: true },
      },
      {
        header: 'Tag',
        key: 'tag',
        cellComponentName: 'tagCellComponent',
        resizable: true,
        size: 10,
      },
      {
        header: 'Input',
        key: 'input',
        cellComponentName: 'inputCellComponent',
        resizable: true,
        size: 10,
        cellConfig: {
          ariaLabel: 'input label',
          align: 'right',
        },
      },
      {
        header: 'Date Format',
        key: 'date',
        size: 10,
        resizable: false,
        cellComponentName: 'dateCellComponent',
        cellConfig: {
          dateFormat: 'hh:mm dd/MM/yyyy',
        },
      },
      {
        header: 'Currency',
        key: 'currency',
        cellComponentName: 'currencyCellComponent',
        resizable: true,
        size: 10,
        cellConfig: {
          // See https://v16.angular.io/api/common/CurrencyPipe
          currencyCode: 'GBP', // 'USD', 'EUR', etc. Por defecto: 'EUR'.
          display: 'symbol-narrow', // 'code' | 'symbol' | 'symbol-narrow' | string. Por defecto: 'symbol'.
          digitsInfo: '1.3-3', // '{minDigitosEnteros}.{minDigitosFraccionarios}-{maxDigitosFraccionarios}'. Por defecto: '1.2-2'.
          // locale: 'ca-ES', // 'en-US', 'ca-ES', etc. Por defecto: 'ca-ES'.
        },
        sortable: true,
      },
      {
        header: 'Texto CSS',
        key: 'styledText',
        resizable: true,
        size: 10,
      },
      {
        header: 'Texto largo',
        key: 'longText',
        resizable: false,
        size: 10,
        cellConfig: { tooltip: true, ellipsis: true },
      },
      {
        header: 'Number',
        key: 'number',
        cellComponentName: 'numberCellComponent',
        resizable: false,
        size: 10,
      },
      {
        header: 'Link',
        key: 'link',
        cellComponentName: 'linkCellComponent',
        resizable: false,
        size: 10,
      },
      {
        header: 'Icon',
        key: 'icon',
        cellComponentName: 'iconCellComponent',
        resizable: false,
        size: 10,
      },
      {
        header: 'Action',
        key: 'action',
        size: 10,
        resizable: false,
        cellComponentName: 'buttonCellComponent',
        cellConfig: {
          buttonCell: {
            buttonConfig: {
              label: 'Action',
              icon: 'matAddCircleOutlineOutline',
            },
            buttonCallback: (
              row: FlattenedRow,
              cell: FlattenedCell,
              column: Column
            ) => {
              return new Promise((res, rej) => {
                alert('Action');
              });
            },
          },
        },
      },
      {
        header: 'Edit',
        key: 'edit',
        size: 5,
        resizable: false,
        cellComponentName: 'actionsCellComponent',
        cellConfig: {
          editLabel: 'Nombre Edit custom',
          hasDelete: true,
          hasDownload: true,
          hasConfirmation: true,
          hasShow: true,
          hasEdit: true,
          hasEditWorkingSession: true,
          deleteCallback: (
            row: FlattenedRow,
            cell: FlattenedCell,
            column: Column
          ) => {
            return new Promise((res, rej) => {
              const userConfirmed = window.confirm(
                'Are you sure you want to delete this row?'
              );

              userConfirmed
                ? res({ delete: true, apply: true })
                : res({ delete: true });
            });
          },
          editCallback: (
            row: FlattenedRow,
            cell: FlattenedCell,
            column: Column
          ) => {
            console.log('ROW:', row);
            console.log('CELL:', cell);
            console.log('COLUMN:', column);
            return new Promise((res, rej) => {
              const newName = window.prompt('What name would you like to set?');

              if (newName) {
                res({ data: { name: newName, age: 25 }, apply: true });
              }
            });
          },
        },
      },
      {
        header: 'Dropdown',
        key: 'docType',
        cellComponentName: 'dropdownCellComponent',
        resizable: false,
        size: 15,
        cellConfig: {
          id: 'docType',
          label: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
          options: [
            { id: 1, label: 'DocType 1' },
            { id: 2, label: 'DocType 2' },
            { id: 3, label: 'DocType 3' },
            { id: 4, label: 'DocType 4' },
            { id: 5, label: 'DocType 5' },
          ],
          disabled: false,
          showClear: false,
          editable: false,
          readOnly: false,
          filter: false,
          optionLabel: 'label',
          optionValue: 'id',
          placeholder: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
          required: true,
        },
      },
      {
        header: 'Dropdown Button',
        key: 'dropdownButton',
        cellComponentName: 'dropdownButtonCellComponent',
        resizable: false,
        size: 15,
        cellConfig: {
          dropdownButtonCell: {
            buttonOptions: {
              label: 'Options',
              btnTheme: 'secondary',
              icon: 'matAddCircleOutlineOutline',
              size: 'small',
            },
            items: [
              {
                label: 'Open Google',
                command: () => window.open('https://www.google.com', '_blank'),
              },
              {
                label: 'Alert Option 2',
                command: () => alert('Option 2'),
              },
              {
                label: 'Other option',
                command: (event: any): void => {
                  console.log('Event:', event);
                },
              },
            ],
          },
        },
      },
    ],
    data: [
      generateRow('John Doe', 25, false, 'key-group-1'),
      generateRow('Second Name', 45, true, 'key-group-2'),
      generateRow('Third Name', 52),
      generateRow('Fourth Name', 32, true, 'key-group-1'),
      generateRow('Fifth Name', 13, true),
      generateRow('Sixth Name', 34),
      generateRow('Seventh Name', 78),
      generateRow('Eighth Name', 43, false, 'key-group-2'),
      generateRow('Ninth Name', 21),
      generateRow('Tenth Name', 12),
      generateRow('Eleventh Name', 56),
      generateRow('Twelfth Name', 25, false, 'key-group-1'),
    ],
    currentPage: 0,
    itemsPerPage: 5,
    showPagination: true,
    showEmptyState: true,
    clickableRows: true,
    emptyButton: { label: 'Afegir' },
    paginationDownloadButton: downloadButton,
  },
  argTypes: {
    resizable: {
      description: 'Allow resize the table columns',
      control: { type: 'boolean' },
      type: 'boolean',
      table: { defaultValue: { summary: true } },
    },
    clickableRows: {
      description: 'Allow Row to be clicked',
      control: { type: 'boolean' },
      type: 'boolean',
      table: { defaultValue: { summary: true } },
    },
    cellTemplatePriorityOrder: {
      description: 'Defines the priority order for cell templates.',
      options: ['row-column-cell', 'cell-row-column', 'column-row-cell'],
      control: { type: 'select' },
      table: { defaultValue: { summary: 'cell-row-column' } },
    },
    columns: {
      description: 'An array of column definitions.',
      control: { type: 'object' },
      table: { defaultValue: { summary: '[]' } },
    },
    data: {
      description: 'An array of row data.',
      control: { type: 'object' },
      table: { defaultValue: { summary: '[]' } },
    },
    currentPage: {
      description: 'The current page number for pagination.',
      control: { type: 'number' },
      table: { defaultValue: { summary: '0' } },
    },
    itemsPerPage: {
      description: 'The number of items to display per page.',
      control: { type: 'number' },
      table: { defaultValue: { summary: '10' } },
    },
    showPagination: {
      description: 'Boolean flag to enable or disable pagination.',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: 'true' } },
    },
    selectable: {
      description: 'Enables checkboxes for rows',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: 'false' } },
    },
    showEmptyState: {
      description: 'Boolean flag to enable or disable empty state.',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: 'true' } },
    },
    emptyMessage: {
      description: 'Message to show in empty state.',
      control: { type: 'string' },
      table: {},
    },
    emptyButton: {
      description: 'Message to show in empty state button.',
      control: { type: 'SeButton' },
      table: {},
    },
    styleClass: {
      description: 'Class to apply to component.',
      control: { type: 'string' },
      table: {},
    },
    emptyButtonEvent: {
      description: 'Emitted when clicked the empty state button.',
      action: 'pageChanged',
      table: {
        type: { summary: 'EventEmitter<void>' },
      },
    },
    pageChange: {
      description: 'Emitted when the current page is changed.',
      action: 'pageChanged',
      table: {
        type: { summary: 'EventEmitter<number>' },
      },
    },
    cellEvent: {
      description: 'Emitted when a cell event occurs.',
      action: 'cellEventTriggered',
      table: {
        type: { summary: 'EventEmitter<CellEvent>' },
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<TableComponent>;

export const DefaultTable: Story = {};

let trackByIdRows = [generateRow('Third Name', 52)];
export const CustomTrackById: Story = {
  args: {
    data: trackByIdRows,
  },

  render: (args) => ({
    props: {
      ...args,
      cellEvent: (event: CellEvent) => {
        trackByIdRows[0].data['age'].value = event.data?.newData.input;
        trackByIdRows[0].data['name'].value = event.data?.newData.input;
      },
      customTrackById: (index: number, row: FlattenedRow): string => {
        return (
          row['id'] +
          row.cells[index].rowData['name'].value +
          row.cells[index].rowData['age'].value
        );
      },
    },
    template: `
      <se-table
        [columns]="[columns[0], columns[1], columns[3]]"
        [data]="data"
        (onCellEvent)="cellEvent($event)"
        [customTrackById]="customTrackById"
        [paginationDownloadButton]="paginationDownloadButton"
      ></se-table>
    `,
  }),
};

export const GroupedTable: Story = {
  args: {
    groupedRows: [
      generatGroupedRow('key-group-1', 'Agrupación 1', true),
      generatGroupedRow('key-group-2', 'Agrupación 2', false),
    ],
  },
};
export const WithRowsPerPageOption: Story = {
  args: {
    showRowsPerPage: true,
    itemsPerPage: 3,
    rowsPerPageOptions: [3, 5, 10, 15, 20, 50],
  },
};

export const NoData: Story = { args: { data: [] } };

export const NoDataDocument: Story = {
  args: {
    data: [],
    emptyButton: { label: 'Afegir Documents' },
    emptyIcon: 'document',
  },
};

export const NoDataSearch: Story = {
  args: {
    data: [],
    emptyButton: { label: 'Afegir', btnTheme: 'primary' },
    emptyIcon: 'search',
  },
};

export const WithSelect: Story = { args: { selectable: true } };

export const CollapsibleRows: Story = {
  args: {
    selectable: false,
    columns: [
      {
        header: 'Grup Num',
        key: 'grup',
        cellComponentName: 'collapsibleCellComponent',
      },
      {
        header: 'Valor declarat',
        key: 'valorDeclarat',
        cellComponentName: 'currencyCellComponent',
      },
      {
        header: 'Aditional Data',
        key: 'aditionalData',
      },
    ],
    data: [
      {
        data: {
          grup: { value: 'A.1' },
          valorDeclarat: { value: 2323 },
          aditionalData: { value: 'additional data' },
        },
        isParent: true,
        rowConfig: {
          isDisabled: false,
        },
        keyGroup: 'key-group-1',
      },
      {
        data: {
          grup: {
            value: 'E.1',
            cellConfig: {
              ngStyle: {
                padding: ' 0 0 0 40px ',
              },
            },
          },
          valorDeclarat: { value: 2323 },
        },
        isChild: true,
        rowConfig: {
          isDisabled: false,
        },
        keyGroup: 'key-group-1',
      },
      {
        data: {
          grup: {
            value: 'F.1',
            cellConfig: {
              ngStyle: {
                padding: ' 0 0 0 40px ',
              },
            },
          },
          valorDeclarat: { value: 2323 },
        },
        isChild: true,
        rowConfig: {
          isDisabled: false,
        },
        keyGroup: 'key-group-1',
      },
      {
        data: {
          grup: { value: 'B.1' },
          valorDeclarat: { value: 2323 },
          aditionalData: { value: 'additional data' },
        },
        isParent: true,
        rowConfig: {
          isDisabled: false,
        },
        keyGroup: 'key-group-2',
      },
      {
        data: {
          grup: {
            value: 'E.1',
            cellConfig: {
              ngStyle: {
                padding: ' 0 0 0 40px ',
              },
            },
          },
          valorDeclarat: { value: 2323 },
        },
        isChild: true,
        isCollapsed: false,
        rowConfig: {
          isDisabled: false,
        },
        keyGroup: 'key-group-2',
      },
    ],
  },
};

export const WithCustomStyles: Story = {
  args: {
    columns: [
      {
        header: 'Default cell',
        key: 'defaultCell',
      },
      {
        header: 'Currency cell',
        key: 'exampleCurrency',
        cellComponentName: 'currencyCellComponent',
      },
      {
        header: 'Date cell',
        key: 'exampleDate',
        cellComponentName: 'dateCellComponent',
      },
    ],
    data: [
      // La primera fila mostrará los estilos por defecto
      {
        data: {
          defaultCell: { value: 'Default 1' },
          exampleCurrency: { value: 123.45 },
          exampleDate: { value: '2024-07-26' },
        },
      },
      /* La segunda fila muestra una apariencia distinta para cada celda. */
      {
        data: {
          defaultCell: {
            value: 'Default 2',
            cellConfig: {
              ngStyle: {
                'text-transform': 'uppercase',
                color: 'orange',
              },
            },
          },
          exampleCurrency: {
            value: 678.91,
            /* El nombre del componente es necesario si no es la celda por
              defecto. */
            cellComponentName: 'currencyCellComponent',
            cellConfig: {
              ngStyle: {
                color: 'green',
                'font-weight': 'bold',
              },
            },
          },
          exampleDate: {
            value: '2024-07-27',
            /* El nombre del componente es necesario si no es la celda por
              defecto. */
            cellComponentName: 'dateCellComponent',
            cellConfig: {
              ngStyle: {
                color: 'red',
                // subrayado rojo ondulado:
                'text-decoration': 'underline wavy red',
              },
            },
          },
        },
      },
      /* La tercer fila muestra otros estilos para las celdas. */
      {
        data: {
          defaultCell: {
            value: 'Default 3',
            cellConfig: {
              ngStyle: {
                'text-style': 'italic',
                color: 'blue',
                'text-transform': 'lowercase',
              },
            },
          },
          exampleCurrency: {
            value: 2345.67,
            /* El nombre del componente es necesario si no es la celda por
              defecto y queremos pasarle una configuración personalizada. */
            cellComponentName: 'currencyCellComponent',
            cellConfig: {
              ngStyle: {
                color: 'red',
                'text-decoration': 'line-through black', // Tachado negro
              },
            },
          },
          exampleDate: {
            value: '2024-07-27',
            /* El nombre del componente es necesario si no es la celda por
              defecto y queremos pasarle una configuración personalizada. */
            cellComponentName: 'dateCellComponent',
            cellConfig: {
              ngStyle: {
                display: 'inline-block',
                'font-weight': 'bold',
                transform: 'rotate(180deg)',
              },
            },
          },
        },
      },
    ],
  },
};

export const WithStyleClass: Story = { args: { styleClass: 'gray-header' } };

const cellConfigIconsActionCell = {
  ellipsis: true,
  iconActions: {
    icons: [
      {
        title: 'Euro',
        label: 'Pagar',
        name: 'matEuroOutline',
      },
    ],
    button: {
      btnTheme: 'onlyText',
      icon: 'matMoreVertSharp',
      size: 'small',
    },
    buttonActions: [
      {
        label: 'Ver detalle',
      },
    ],
  },
};

const getIconsActionCellTemplateData = (defaultText: string): Row => {
  return {
    data: {
      defaultCell: { value: defaultText },
      actions: {
        value: '',
        cellComponentName: 'iconActionsCellComponent',
        cellConfig: cellConfigIconsActionCell,
      },
    },
  };
};

export const WithIconsActionCellTemplate: Story = {
  args: {
    columns: [
      {
        header: 'Default cell',
        key: 'defaultCell',
      },
      {
        header: 'Icon Actions',
        key: 'actions',
        cellComponentName: 'iconActionsCellComponent',
      },
    ],
    data: [
      getIconsActionCellTemplateData('Default 1'),
      getIconsActionCellTemplateData('Default 2'),
    ],
  },
};

export const MobileSummarySelectable: Story = {
  args: {
    selectable: true,
    mobileLayoutMode: TableMobileLayoutMode.SUMMARY_MODE,
    columns: [
      { header: 'Name', key: 'name', resizable: true, size: 50 },
      {
        header: 'Age',
        key: 'age',
        resizable: true,
        size: 50,
        cellConfig: { align: 'right' },
      },
    ],
    data: [
      generateRow('Alice', 30),
      generateRow('Bob', 40),
      generateRow('Charlie', 50),
    ],
    itemsPerPage: 5,
    showPagination: false,
  },
  parameters: {
    docs: {
      description: {
        story:
          'Table in mobile SUMMARY_MODE with selectable enabled. The checkbox appears to the left of each row, outside the table cells.',
      },
    },
  },
};

export const MobileSummaryWithExpandableColumns: Story = {
  args: {
    selectable: true,
    mobileLayoutMode: TableMobileLayoutMode.SUMMARY_MODE,
    columns: [
      { header: 'Name', key: 'name', resizable: true, size: 10 },
      {
        header: 'Age',
        key: 'age',
        resizable: true,
        size: 30,
        cellConfig: { align: 'right' },
        mobileCellConfig: {
          summary: false,
        },
      },
      {
        header: 'Email',
        key: 'email',
        resizable: false,
        size: 20,
        mobileCellConfig: {
          summary: false,
        },
      },
      {
        header: 'Address',
        key: 'address',
        resizable: true,
        size: 20,
        mobileCellConfig: {
          summary: false,
        },
      },
      {
        header: 'Phone',
        key: 'phone',
        resizable: true,
        size: 20,
        mobileCellConfig: {
          summary: false
        }
      },
      {
        header: 'tag',
        key: 'tag',
        cellComponentName: 'tagCellComponent',
        resizable: true,
        size: 10,
        mobileCellConfig: {
          header: false,
          summary: true,
        }
      },
    ],
    data: [
      {
        data: {
          name: { value: 'veeeeery long text in the default cell with spaces',
            cellConfig: {
              ellipsis: true,
            } },
          age: { value: 30 },
          email: { value: '<EMAIL>' },
          address: { value: '123 Main St' },
          phone: { value: '555-1234' },
          tag: {
            value: 'TCJ',
            cellConfig: {
              tooltip: true,
              tooltipText: 'Texto de tooltip de una etiqueta',
              tagCell: { tagTheme: 'pink' },
            },
          },
        },
      },
      {
        data: {
          name: {
            value: 'veeeeryLongTextInDefaultCellWithoutAnySpaces',
            cellConfig: {
              ellipsis: true,
            },
          },
          age: { value: 40 },
          email: { value: '<EMAIL>' },
          address: { value: '456 Oak Ave' },
          phone: { value: '555-5678' },
          tag: {
            value: 'TCJ',
            cellConfig: {
              tooltip: true,
              tooltipText: 'Texto de tooltip de una etiqueta',
              tagCell: { tagTheme: 'pink' },
            },
          },
        },
      },
      {
        data: {
          name: { value: 'Charlie' },
          age: { value: 50 },
          email: { value: '<EMAIL>' },
          address: { value: '789 Pine Rd' },
          phone: { value: '555-9012' },
          tag: {
            value: 'TCJ',
            cellConfig: {
              tooltip: true,
              tooltipText: 'Texto de tooltip de una etiqueta',
              tagCell: { tagTheme: 'pink' },
            },
          },
        },
      },
    ],
    itemsPerPage: 5,
    showPagination: false,
  },
  parameters: {
    docs: {
      description: {
        story:
          'Table in mobile SUMMARY_MODE with selectable enabled and expandable columns. The chevron appears only if there are hidden columns (showInSummary: false) for that row.',
      },
    },
  },
};
