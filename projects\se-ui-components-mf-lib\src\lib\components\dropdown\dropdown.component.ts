import { AfterViewInit, Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild, forwardRef } from '@angular/core';
import {
  NG_VALUE_ACCESSOR,
  FormControl,
  ControlValueAccessor,
  ControlContainer,
  FormGroup,
} from '@angular/forms';
import { Dropdown, DropdownChangeEvent } from 'primeng/dropdown';
import { SeDropdownOption } from './dropdown.model';
import { Nullable } from '../../models/nullable.model';

@Component({
  selector: 'se-dropdown',
  template: `
    <div class="se-dropdown" fixAriaHiddenFocusable>
      <div class="input-label" *ngIf="label || tooltip">
        <label *ngIf="label" [for]="id">{{ label | translate }}</label>
        <ng-icon class="tooltip-icon" *ngIf="tooltip" name="matInfo" [pTooltipAccessible]="tooltipText"></ng-icon>
      </div>
      <p *ngIf="readOnly; else dropdownComponent" class="readonly">
        {{ getValue() | translate }}
      </p>

      <ng-template #dropdownComponent>
        <p-dropdown
          #dropdown
          id="dropdown"
          [attr.aria-label]="(ariaLabel ?? label ?? '') | translate"
          [styleClass]="getDropwDownClass()"
          [inputId]="id"
          [virtualScroll]="virtualScroll"
          [virtualScrollItemSize]="virtualScrollItemSize"
          [editable]="editable"
          [appendTo]="appendTo"
          [options]="optionsValue"
          [optionLabel]="optionLabel"
          [optionValue]="optionValue"
          [placeholder]="placeholder | translate"
          [emptyMessage]="empty | translate"
          [emptyFilterMessage]="'SE_COMPONENTS.DROPDOWN.FILTER_NO_RESULTS' | translate"
          [disabled]="disabled"
          [showClear]="isShowClearActive"
          [ngModel]="value"
          [filter]="filter"
          [scrollHeight]="scrollHeight"
          [autoDisplayFirst]="false"
          (onChange)="onOptionSelected($event)"
          (onShow)="onDropdownShow()"
          (onHide)="isPanelShown = false">
          <!-- SELECTED -->
          <ng-template pTemplate="selectedItem">
            {{ getValue() | translate }}
          </ng-template>
          <!-- ITEMS -->
          <ng-template let-item pTemplate="item">
            <span [attr.translate]="allowTranslations === false ? 'no' : null">{{ item[optionLabel] | translate }}</span>
          </ng-template>

          <!-- CLEAR ICON -->
          <ng-template pTemplate="clearicon">
            <TimesIcon
              [title]="titleClear | translate"
              (click)="clear($event, dropdown)"
              (keydown)="clear($event, dropdown)"
              [tabindex]="0"
            />
          </ng-template>
          <!-- DROPDOWN ICON -->
          <ng-template pTemplate="dropdownicon">
            <ChevronUpIcon *ngIf="isPanelShown" />
            <ChevronDownIcon *ngIf="!isPanelShown" />
          </ng-template>
        </p-dropdown>
        <se-error-message [control]="control"></se-error-message>
      </ng-template>
    </div>
  `,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DropdownComponent),
      multi: true,
    },
  ],
  styleUrls: ['./dropdown.component.scss'],
})
export class DropdownComponent implements ControlValueAccessor, OnInit, AfterViewInit {
  @ViewChild('dropdown') dropdown!: Dropdown;

  private _disabled: boolean = false;
  private _firstLoadComponent: boolean = true;

  @Input() id: string | undefined;
  @Input() label: string | undefined;
  @Input() ariaLabel: string | undefined;
  @Input() autoSelect: boolean = false;
  @Input() virtualScroll: boolean = false;
  @Input() virtualScrollItemSize?: number;
  @Input() appendTo: string | undefined;
  @Input() set options (value: SeDropdownOption[] | undefined) {
    this.optionsValue = value;
    // Autoselecciono la primera opcion si es la unica
    if (this.autoSelect && this.optionsValue?.length === 1) {
      this.setValue(this.optionsValue[0][this.optionValue]);
      const control = this.getFormControl();
      control.setValue(this.value);
      setTimeout(() => {
        this.dropdownOutput.emit(this.value);
      }, 0);
    }
    /* markAsTouched=false evita error
      ExpressionChangedAfterItHasBeenCheckedError. Setear las opciones no
      implica que el usuario haya "tocado" el input, por lo tanto no debería
      marcarse como touched en este caso. Si se marca como touched, en este
      punto del ciclo de vida, saltará un error debido a que el valor de
      touched pasará de false a true al inicializar el componente sin que el
      usuario haya tocado el input. */
    this.updateValue(this.value, { markAsTouched: false });
    if (!this._firstLoadComponent) this.setAriaExpanded();
  }

  get options (): SeDropdownOption[] | undefined {
    return this.optionsValue;
  }

  @Input() optionLabel: string = 'label';
  @Input() optionValue: string = 'id';
  @Input() placeholder: string = 'SE_COMPONENTS.DROPDOWN.PLACEHOLDER';
  @Input() empty: string = 'SE_COMPONENTS.DROPDOWN.NO_RESULTS';
  @Input() showClear: boolean = false;
  @Input() titleClear: string = ''; //Use default: 'SE_COMPONENTS.DROPDOWN.CLEAR_SELECTION'

  @Input() set editable(value: boolean) {
    this._editable = value;
    this.editableOriginalState = value;
  }

  @Input() readOnly: boolean = false;
  @Input() formControlName!: string;
  @Input() filter: boolean = false;
  @Input() tooltip = false;
  @Input() tooltipText: string | TemplateRef<HTMLElement> | undefined;
  @Input() scrollHeight: string = '460px';
  @Input() allowTranslations: boolean = true;

  @Input() set disabled(value: boolean) {
    this.setDisabledState(value);

    if(!value) {
      this.getFormControl()?.enable({ emitEvent: true });
    } else {
      this.getFormControl()?.disable({ emitEvent: true });
    }
  }

  get disabled() { return this._disabled };

  get editable() { return this._editable };

  get isShowClearActive() { return (this.showClear || this.editableOriginalState) && this.value !== undefined };

  @Output() dropdownOutput: EventEmitter<any> = new EventEmitter<any>();

  value: Nullable<string | number | boolean>;
  control!: FormControl;
  isPanelShown: boolean = false;
  optionsValue: SeDropdownOption[] | undefined;
  //This variable save original value of this.editable for bugfix primeNg for editable dropdown
  _editable: boolean = false;
  editableOriginalState: boolean = false;

  constructor(
    private controlContainer: ControlContainer,
  ) {
    // Intencionadamente vacío
  }

  ngOnInit() {
    this.control = this.getFormControl();

    this._disabled = this.control.disabled || this.disabled;

    this.control.registerOnDisabledChange((value) => { this.setDisabledState(value) })
  }

  ngAfterViewInit() {
    if (this._firstLoadComponent) this._firstLoadComponent = false;
    this.setAriaExpanded();
  }

  private onChange: (value: Nullable<string | number | boolean>) => void = () => {};
  private onTouched: () => void = () => {};

  onOptionSelected(event: DropdownChangeEvent) {
    this.value = undefined;

    if (event.originalEvent instanceof InputEvent && this.editable) {
      this.setValue(event.value);
    } else if (this.getValidOption(event.value)) {
      this._editable = false;
      this.setValidOption(event.value);
    }

    this.onTouched();
    this.onChange(this.value);
    this.dropdownOutput.emit(this.value);
  }

  private getOptionValue(value: Nullable<string | number | boolean>): SeDropdownOption | undefined {
    return this.optionsValue?.find(option => option[this.optionValue as keyof SeDropdownOption]?.toString().toLowerCase() === value?.toString().toLowerCase());
  }

  private getValidOption(value: Nullable<string | number | boolean>): SeDropdownOption | undefined {
    const found = this.getOptionValue(value);
    return found && !found.disabled ? found : undefined;
  }

  private setValidOption(value: Nullable<string | number | boolean>): void {
    this._editable = false;
    this.setValue(value);
  }

  writeValue(value: Nullable<string | number | boolean>): void {
    this.updateValue(value);
  }

  private updateValue(
    value: Nullable<string | number | boolean>,
    { markAsTouched } = { markAsTouched: true }
  ): void {
    if (value === undefined) {
      return;
    }

    if (!this.options?.length) {
      this.setValue(value);
      return;
    }

    const found = this.getOptionValue(value);
    this.setValidOption((found || this.editableOriginalState) ? value : undefined);

    if (markAsTouched) this.onTouched();
  }

  setValue(value: Nullable<string | number | boolean>): void {
    this.value = value;
  }

  getValue(): string {
    const val = this.optionsValue?.find((option) => option?.[this.optionValue as keyof SeDropdownOption] === this.value)?.[this.optionLabel as keyof SeDropdownOption];

    return val?.toString() ?? '';
  }

  registerOnChange(fn: (value: any) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this._disabled = isDisabled;
  }

  clear(event: MouseEvent | KeyboardEvent, dropdown: Dropdown): void {
    if (event instanceof KeyboardEvent && event.code !== 'Enter') return;

    dropdown.clear(event);
    this._editable = this.editableOriginalState;
  }

  getDropwDownClass() {
    return this.control.touched && this.control.status === 'INVALID'
      ? 'dropdown-invalid'
      : '';
  }

  setAriaExpanded() {
    setTimeout(() => {
      const combobox = this.dropdown?.containerViewChild?.nativeElement.querySelector('div.p-dropdown span.p-dropdown-label.p-inputtext');
    if (combobox){
      combobox.role = 'combobox';
      combobox.setAttribute('aria-expanded', false);
   }
    }, 0);
  }

  private getFormControl() : FormControl {
    if(this.control){
      return this.control;
    }

    return (this.controlContainer.control as FormGroup).get(
      this.formControlName
    ) as FormControl;
  }

  onDropdownShow(): void {
    this.isPanelShown = true;

    /* El siguiente código evita el error de accesibilidad
      "scrollable-region-focusable" de las Web Content Accessibility
      Guidelines:
      [WCAG 2.1.1](https://www.w3.org/WAI/WCAG21/Understanding/keyboard.html) */
    document
      .querySelector('.p-dropdown-items-wrapper')
      ?.setAttribute('tabindex', '0');
  }
}
