import { CommonModule, CurrencyPipe, registerLocaleData } from '@angular/common';
import { DEFAULT_CURRENCY_CODE, LOCALE_ID, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgIcon } from '@ng-icons/core';
import { TranslateModule } from '@ngx-translate/core';
import { MultiSelectModule } from 'primeng/multiselect';
import { SeSharedModule } from '../../shared/shared.module';
import { SeBadgeModule } from '../badge/badge.module';
import { SeButtonModule } from '../button';
import { SeInputModule } from '../input/input.module';
import { RangeFilterComponent } from './range-filter.component';
import localeCa from '@angular/common/locales/ca';
import { TimesIcon } from 'primeng/icons/times';


registerLocaleData(localeCa, 'ca');

@NgModule({
  declarations: [RangeFilterComponent],
  imports: [
    CommonModule,
    SeSharedModule,
    MultiSelectModule,
    ReactiveFormsModule,
    FormsModule,
    SeInputModule,
    NgIcon,
    SeBadgeModule,
    SeButtonModule,
    TimesIcon,
    TranslateModule.forChild(),
  ],
  exports: [RangeFilterComponent],
  providers: [
    { provide: LOCALE_ID, useValue: 'ca-ES' },
    {
      provide: DEFAULT_CURRENCY_CODE,
      useValue: 'EUR',
    },
  ],
})
export class SeRangeFilterModule {}
