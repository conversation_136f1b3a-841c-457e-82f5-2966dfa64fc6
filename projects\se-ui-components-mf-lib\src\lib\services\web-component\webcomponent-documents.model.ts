// Documents: functional modules
export enum WcDocumentsFunctionalModules {
  'TAX600' = 'TAX600',
  'PAGOS_INDEGUTS' = 'PAGOS_INDEGUTS',
  'INSPECTION_SIGNATURE' = 'INSPECTION_SIGNATURE',
}
export declare type WcDocumentsFunctionalModulesT =
  keyof typeof WcDocumentsFunctionalModules;

// Documents: endpoint services
export enum WcDocumentsServices {
  DOWNLOAD = 'DOWNLOAD',
  DOWNLOAD_RECEIPTS = 'DOWNLOAD_RECEIPTS',
}
export declare type WcDocumentsServicesT = keyof typeof WcDocumentsServices;
