import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { SeCustomIdDirective } from './custom-id.directive';
import { SeCustomIdModule } from './custom-id.module';
import { SeSharedModule } from '../../shared/shared.module';

const meta: Meta<SeCustomIdDirective> = {
  title: 'Directives/Custom Id',
  component: SeCustomIdDirective,
  decorators: [
    moduleMetadata({
      imports: [SeCustomIdModule,SeSharedModule],
    }),
  ],
  args: {
    customIdInput: {
      querySelector: 'div.test',
      id: 'custom id'
    } 
  },
  tags: ['autodocs'],
  render: (args) => ({
    props: {
      ...args,
    },
    template: `
      <div [customId]="customIdInput">
        <div class="test">
          <ng-icon class="info-icon text-md" name="matInfo"></ng-icon>
        </div>
      </div>
    `,
    styles: [
      `
        .icon-button {
          all: initial;
          width: 16px;
          height: 16px;
          font-size: 16px;
          cursor: pointer;
          border-radius: 8px;
      
          &:focus-visible {
            outline: var(--color-black) auto 1px;
          }
        }
      `
    ]
  }),
};

export default meta;
type Story = StoryObj<SeCustomIdDirective>;

export const Default: Story = {};
