{"name": "se-ui-components-mf-lib-app", "version": "0.177.0-snapshot", "scripts": {"ng": "ng", "start": "ng serve", "watch": "ng build --watch --configuration development", "test": "ng test", "storybook": "ng run se-ui-components-mf-lib:storybook", "build:storybook": "ng run se-ui-components-mf-lib:build-storybook", "build:lib": "ng build se-ui-components-mf-lib --configuration production", "build-watch:lib": "ng build se-ui-components-mf-lib --configuration production --watch", "publish:lib": "npm publish ./dist/se-ui-components-mf-lib", "build-publish:lib": "npm run build:lib && npm run publish:lib", "build:prod": "ng build se-ui-components-mf-lib --configuration production"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@ng-bootstrap/ng-bootstrap": "^15.1.1", "@ng-icons/core": "^25.2.0", "@ng-icons/material-icons": "^25.2.0", "@ngx-translate/core": "^15.0.0", "@popperjs/core": "^2.11.6", "bootstrap": "^5.3.2", "crypto-js": "^4.1.1", "ngx-cookie-service": "^16.0.1", "ngx-device-detector": "^6.0.0", "ngx-translate-multi-http-loader": "^16.0.2", "primeng": "^16.9.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "uuid": "^8.3.2", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.0", "@angular/cli": "~16.2.0", "@angular/compiler-cli": "^16.2.0", "@angular/localize": "^16.2.0", "@storybook/addon-a11y": "7.0.27", "@storybook/addon-essentials": "7.0.27", "@storybook/addon-interactions": "7.0.27", "@storybook/addon-links": "7.0.27", "@storybook/addon-storysource": "7.0.3", "@storybook/addon-styling": "^1.3.6", "@storybook/angular": "7.0.27", "@storybook/blocks": "7.0.27", "@storybook/testing-library": "^0.0.14-next.2", "@types/crypto-js": "^4.1.2", "@types/jasmine": "~4.3.0", "@types/uuid": "^9.0.4", "http-proxy-middleware": "^2.0.6", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ng-packagr": "^16.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "storybook": "^7.0.27", "typescript": "~5.1.3"}}