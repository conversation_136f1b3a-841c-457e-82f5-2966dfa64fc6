export interface SeMessageI {
  severity: 'info' | 'warning' | 'error' | 'success' | null;
  title?: string | null;
  subtitle?: string | null;
  closable?: boolean | null;
  closableFn?: (message: SeMessage) => void;
  titleParams?: {[x: string]: string};
  subtitleParams?: {[x: string]: string};
  collapsible?: boolean;
  collapsibleFn?: boolean;
  trackingId?: string;
  titleDescription?: string;
  titleClass?: string;
}

export class SeMessage implements SeMessageI {
  severity: 'info' | 'warning' | 'error' | 'success' | null = null;
  title?: string | null = null;
  subtitle?: string | null = null;
  closable?: boolean | null = null;
  closableFn?: (message: SeMessage) => void;
  titleParams?: {[x: string]: string};
  subtitleParams?: {[x: string]: string};
  collapsible?: boolean;
  collapsibleFn?: boolean;
  trackingId?: string;
  titleClass?: string;

  constructor(
    severity: 'info' | 'warning' | 'error' | 'success' | null,
    title?: string,
    subtitle?: string,
    closable?: boolean,
    closableFn?: (message: SeMessage) => void,
    titleParams?: {},
    subtitleParams?: {},
    collapsible?: boolean,
    collapsibleFn?: boolean,
    trackingId?: string,
    titleClass?: string,
  ) {
    this.severity = severity || null;
    this.title = title;
    this.subtitle = subtitle;
    this.closable = closable;
    this.closableFn = closableFn;
    this.titleParams = titleParams;
    this.subtitleParams = subtitleParams;
    this.collapsible = collapsible;
    this.collapsibleFn = collapsibleFn;
    this.trackingId = trackingId;
    this.titleClass = titleClass;
  }
}
