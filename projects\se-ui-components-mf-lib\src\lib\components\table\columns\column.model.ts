import { TemplateRef, Type } from '@angular/core';
import { CellComponent, CellComponentConfig, CellConfig } from '../cells/cells.model';

export interface ColumnConfig {
  header: string;
  key: string;
}

export interface FlattenedColumn {
  id: string;
  header: string;
  key: string;
  size: number | string | null;
  tooltip: boolean;
  tooltipText: string | TemplateRef<HTMLElement> | undefined;
  resizable: boolean;
  cellConfig?: CellConfig;
  cellComponent?: Type<CellComponent>;
  sortable?: boolean;
 mobileCellConfig?: MobileCellConfig;
}

export interface Column extends CellComponentConfig {
  id?: string;
  header: string;
  key: string;
  size?: number | string;
  tooltip?: boolean;
  tooltipText?: string | TemplateRef<HTMLElement> | undefined;
  resizable?: boolean;
  sortable?: boolean;
  mobileCellConfig?: MobileCellConfig;
}

export interface MobileCellConfig {
    header?: boolean;
    summary?: boolean
}
