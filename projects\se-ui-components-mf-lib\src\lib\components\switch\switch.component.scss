span {
  display: flex;
  align-items: center;
  font-family: var(--font-primary);
  font-size: var(--text-xs);
  line-height: var(--line-sm);
  padding-bottom: 4px;

  &.title-switch {
    color: var(--color-gray-700);
  }

  &.subtitle-switch {
    color: var(--color-gray-600);
    margin-top: 6px;
  }
}

.switch-container {
  display: flex;
  align-items: flex-start;
  font-size: var(--text-xs);
  line-height: var(--line-sm);
  font-family: var(--font-primary);

  &__label {
    color: var(--color-gray-700);

    &.left {
      order: 0;
      margin-right: 8px;
      text-align: right;
    }

    &.right {
      order: 2;
      margin-left: 8px;
    }

    &.disabled {
      color: var(--color-gray-500);
    }
  }

  .switch {
    position: relative;
    display: inline-block;
    width: 32px;
    min-width: 32px;
    height: 18px;
    border-radius: 9px;
    margin: 2px 0;

    input[type='checkbox'] {
      display: none;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--color-gray-400);
      transition: background 0.3s;
      border-radius: 9px;
      outline: none;

      &.disabled {
        cursor: not-allowed;
        background: var(--color-gray-300);
        color: var(--color-gray-550)
      }

      &.checked {
        background: var(--color-blue-500);

        &.disabled {
          background: var(--color-gray-300);
        }
      }

      &.hover {
        background: var(--color-blue-600);
      }

      &.focus {
        outline: 2px solid var(--color-primary-link);
      }

      .toggle-ball {
        position: absolute;
        width: 14px;
        height: 14px;
        left: 2px;
        top: 2px;
        background: white;
        border-radius: 7px;
        transition: transform 0.3s;
      }

      .toggle-ball.checked {
        transform: translateX(14px);

        .switch-icon {
          font-size: 12px;
          display: flex;
          height: 14px;
          width: 14px;
          color: var(--color-blue-500);
          justify-content: center;
          align-items: center;

          &.disabled {
            color: var(--color-gray-400);
          }
        }
      }
    }
  }
}
