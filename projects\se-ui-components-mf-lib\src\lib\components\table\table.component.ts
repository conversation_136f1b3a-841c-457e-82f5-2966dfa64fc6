import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  Type,
} from '@angular/core';
import { FormBuilder, FormGroup, FormControl } from '@angular/forms';
import { PaginatorState } from 'primeng/paginator';
import { Observable, Subject, Subscription, takeUntil } from 'rxjs';
import { Nullable } from '../../models';
import { generateUniqueId } from '../../shared/utils/generate-unique-id/generate-unique-id';
import { SeButton } from '../button';
import { CellComponentMap } from './cells/cell-component-registry';
import { CellEventService } from './cells/cell-event.service';
import {
  CellComponent,
  CellComponentConfig,
  CellConfig,
  CellEvent,
  CellEventTypes,
  FlattenedCell,
} from './cells/cells.model';
import { Column, FlattenedColumn } from './columns/column.model';
import { RowComponentMap } from './rows/row-component-registry';
import {
  FlattenedGroupRow,
  FlattenedRow,
  RowComponent,
  RowConfig,
} from './rows/rows.model';
import { PanelRowComponent } from './rows/templates/panel-row.component';
import {
  Cell,
  GroupRow,
  Row,
  SortParams,
  TableSortOrder,
  TableMobileLayoutMode,
  TemplatePriorityOrder,
} from './table.model';
import { TableService } from './table.service';
import { DeviceService } from '../../services';

@Component({
  selector: 'se-table',
  templateUrl: './table.component.html',
  styleUrls: ['./table.component.scss'],
  providers: [CellEventService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TableComponent implements OnChanges, OnInit, OnDestroy {
  private resizeSubscription?: Subscription;
  @Input() columns: Column[] | undefined;
  @Input() data: Row[] = [];
  @Input() resizable = true;
  @Input() selectable = false;
  @Input() cellTemplatePriorityOrder: TemplatePriorityOrder = 'cell-row-column';
  @Input() currentPage: number = 0;
  @Input() itemsPerPage: number = 10;
  @Input() showPagination = true;
  @Input() paginationSelectedText: string = '';
  @Input() paginationInnerText: string = 'SE_COMPONENTS.TABLE.PAGINA';
  @Input() showEmptyState = true;
  @Input() emptyMessage = 'SE_COMPONENTS.TABLE.NO_DATA';
  @Input() emptyIcon: 'document' | 'search' | 'info' | undefined;
  @Input() emptyButton: SeButton | undefined;
  @Input() clickableRows = false;
  @Input() showRowsPerPage = false;
  @Input() rowsPerPageOptions: number[] | undefined;
  @Input() groupedRows: GroupRow[] = [];
  @Input() lazyTotalRecords: number = 0;
  @Input() lazy: boolean = false;
  @Input() styleClass: string = '';
  @Input() currentSortColumn: string | undefined;
  @Input() showSelectAll: boolean = true;
  @Input() selectAllLabel = 'SE_COMPONENTS.CHECKBOX.SELECT_ALL';
  @Input() paginationDownloadButton: Nullable<SeButton>;
  @Input() mobileLayoutMode: TableMobileLayoutMode =
    TableMobileLayoutMode.NORMAL_MODE;

  @Input() set reloadTable$(value: Observable<void>) {
    if (!this._reloadTable$ && value) {
      this._reloadTable$ = value;

      this._reloadTable$.pipe(takeUntil(this.unsubscribe)).subscribe({
        next: () => {
          this.precomputeTemplates();
        },
      });
    }
  }

  private _reloadTable$?: Observable<void>;
  private unsubscribe: Subject<void> = new Subject<void>();

  @Input() customTrackById: Nullable<
    (index: number, row: FlattenedRow) => string
  >;

  @Output() onEmptyButtonEvent: EventEmitter<void> = new EventEmitter<void>();
  @Output() onPageChange: EventEmitter<PaginatorState> =
    new EventEmitter<PaginatorState>();
  @Output() onCellEvent: EventEmitter<CellEvent> =
    new EventEmitter<CellEvent>();
  @Output() onSelectionChange: EventEmitter<Row[]> = new EventEmitter<Row[]>();
  @Output() onClickDisabledSelection: EventEmitter<Row> =
    new EventEmitter<Row>();
  @Output() onRowClick: EventEmitter<any> = new EventEmitter<any>();
  @Output() sortByColumn: EventEmitter<SortParams> =
    new EventEmitter<SortParams>();
  @Output() paginationDownloadClick: EventEmitter<void> =
    new EventEmitter<void>();

  flattenedColumns: FlattenedColumn[] = [];
  flattenedData: FlattenedRow[] = [];
  allFlattenedData: FlattenedRow[] = [];
  totalRecordsAllFlattenedData: number = 0;
  renderFlag = true;
  formGroup!: FormGroup;
  private sortOrder: TableSortOrder = TableSortOrder.DESC;

  private rowCache = new Map<string, FlattenedRow>();
  private rowHashes = new Map<string, string>();
  private cellEventSubscription!: Subscription;
  allRowsSelected = false;

  summaryRowExpanded: { [rowId: string]: boolean } = {};

  TableMobileLayoutMode = TableMobileLayoutMode;

  get totalRecords(): number {
    return this.lazy
      ? this.lazyTotalRecords
      : this.totalRecordsAllFlattenedData;
  }

  get first(): number {
    return this.itemsPerPage * this.currentPage;
  }

  constructor(
    private cellEventService: CellEventService,
    private cdRef: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private tableService: TableService,
    private deviceService: DeviceService
  ) {}

  ngOnInit(): void {
    if (this.selectable) {
      this.formGroup = this.formBuilder.group({
        checkbox: [false],
      });
    }

    this.precomputeTemplates();

    this.cellEventSubscription = this.cellEventService.events$.subscribe(
      (cellEvent) => this.onCellEventChange(cellEvent)
    );

    // Subscribe to window resize and re-run setup if device type changes
    this.resizeSubscription = this.deviceService.isMobileSize$.subscribe(() => {
      this.precomputeTemplates();
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (
      (changes['data'] && !changes['data'].firstChange) ||
      (changes['columns'] && !changes['columns'].firstChange) ||
      (changes['templatePriorityOrder'] &&
        !changes['templatePriorityOrder'].firstChange)
    ) {
      this.precomputeTemplates();
    }
  }


  ngOnDestroy(): void {
    this.cellEventSubscription?.unsubscribe();
    this.unsubscribe.next();
    this.unsubscribe.complete();

    this.resizeSubscription?.unsubscribe();
  }



  changePage(newPage: PaginatorState) {
    this.itemsPerPage = newPage.rows ?? this.itemsPerPage;
    this.currentPage = newPage.page ?? 0;
    this.onPageChange.emit(newPage);

    if (!this.lazy) {
      this.updateCurrentPageData(this.currentPage, this.itemsPerPage);

      if (this.selectable) {
        this.updateHeaderCheckboxState();
      }
    }
  }

  trackById(index: number, row: FlattenedRow): string {
    if (this.customTrackById) {
      return this.customTrackById(index, row);
    }
    return row['id'];
  }

  private precomputeTemplates() {
    if (this.columns) {
      const shouldShowCheckboxColumn =
        this.selectable &&
        (this.mobileLayoutMode !== TableMobileLayoutMode.SUMMARY_MODE ||
          !this.isMobile());

      this.flattenedColumns = this.columns.map((column) => {
        return {
          ...column,
          id: column.id ?? generateUniqueId(),
          resizable: column.resizable ?? true,
          size: column.size ?? null,
          tooltip: column.tooltip ?? false,
          tooltipText: column.tooltipText ?? '',
          sortable: column.sortable,
        };
      });

      if (shouldShowCheckboxColumn) {
        this.flattenedColumns.unshift({
          id: 'checkbox',
          header: '',
          key: 'checkbox',
          cellComponent: CellComponentMap['checkboxCellComponent'],
          resizable: false,
          size: '20px',
          tooltip: false,
          tooltipText: undefined,
        });
      }
    }

    this.allFlattenedData = this.data.map((row) => {
      let newRowId;

      if (!row.id) {
        row.id = generateUniqueId();
      }

      newRowId = row.id;
      const rowKey = this.getRowKey(newRowId);
      const newRowHash = this.hashRowData(row.data);
      const cachedRowHash = this.rowHashes.get(rowKey);

      if (!this.rowCache.has(rowKey) || newRowHash !== cachedRowHash) {
        const flattenedRow = this.createFlattenedRow(row, newRowId);

        this.rowCache.set(rowKey, flattenedRow);
        this.rowHashes.set(rowKey, newRowHash);
      }

      return this.rowCache.get(rowKey)!;
    });

    // Las filas hijo no cuentan para el total de registros
    this.totalRecordsAllFlattenedData = this.allFlattenedData.filter(
      (row) => !row.isChild
    ).length;
    // --- SETUP CHECKBOX FORM GROUP HERE ---
    if (this.selectable) {
      const group: { [key: string]: FormControl } = {
        checkbox: new FormControl(false),
      };
      (this.data || []).forEach((row) => {
        if (!row.id) row.id = generateUniqueId();
        group[row.id] = new FormControl(!!row.data?.['checkbox']?.value);
      });
      this.formGroup = this.formBuilder.group(group);
      setTimeout(() => this.updateHeaderCheckboxState());
    }

    if (!this.lazy) {
      this.updateCurrentPageData(this.currentPage, this.itemsPerPage);
    } else {
      this.flattenedData = this.getFlattenedData(this.allFlattenedData);
    }

    this.cdRef.detectChanges();
  }

  private createFlattenedRow(row: Row, rowId: string): FlattenedRow {
    const { component: rowComponent, rowConfig } = this.getRowTemplate(row);
    const flattenedRow: FlattenedRow = {
      cells: [],
      rowComponent: rowComponent,
      rowConfig,
      id: rowId,
      keyGroup: row?.keyGroup ?? '',
      isChild: row?.isChild ?? false,
      isParent: row?.isParent ?? false,
      isCollapsed: row?.isCollapsed ?? true,
    };

    if (this.selectable) {
      flattenedRow.cells.push({
        rowData: row.data,
        column: {
          id: 'checkbox',
          header: '',
          key: 'checkbox',
          resizable: false,
        },
        component: CellComponentMap['checkboxCellComponent'],
        id: generateUniqueId(),
        value: row.data['checkbox']?.value,
        cellConfig: {
          cellComponentName: 'checkboxCellComponent',
        },
      });
      flattenedRow.rowConfig['ariaLabel'] = 'UI_COMPONENTS.SELECT_ROW.SINGLE';
    }

    this.columns?.forEach((column) => {
      const cellData = row.data[column.key] || { value: null };

      const { component, cellConfig } = this.getCellTemplate(
        cellData,
        column,
        row
      );

      const flattenedCell: FlattenedCell = {
        rowData: row.data,
        column,
        component: component,
        id: generateUniqueId(),
        value: cellData.value,
        cellConfig: cellConfig,
      };

      flattenedRow.cells.push(flattenedCell);
    });

    return flattenedRow;
  }

  private getRowKey(rowId: string): string {
    return `row-${rowId}`;
  }

  private hashRowData(rowData: { [key: string]: Cell }): string {
    return JSON.stringify(rowData);
  }

  private getRowTemplate(row: Row): {
    component: Type<RowComponent>;
    rowConfig: RowConfig;
  } {
    if (row.rowComponent) {
      return {
        component: row.rowComponent as Type<RowComponent>,
        rowConfig: row.rowConfig ?? {},
      };
    }

    return {
      component: RowComponentMap[row.rowComponentName || 'defaultRow'],
      rowConfig: row.rowConfig ?? {},
    };
  }

  private getCellTemplate(
    cellData: Cell,
    column: Column,
    row: Row
  ): { component: Type<CellComponent>; cellConfig: CellConfig } {
    const templateOrder = this.cellTemplatePriorityOrder.split('-');
    const sources: CellComponentConfig[] = [
      {
        cellComponent: row.cellComponent,
        cellConfig: row.cellConfig,
        cellComponentName: row.cellComponentName,
      },
      {
        cellComponent: column.cellComponent,
        cellConfig: column.cellConfig,
        cellComponentName: column.cellComponentName,
      },
      {
        cellComponent: cellData?.cellComponent,
        cellConfig: cellData?.cellConfig,
        cellComponentName: cellData?.cellComponentName,
      },
    ];

    let found = templateOrder
      .map((key) =>
        sources.find((_, i) => key === ['row', 'column', 'cell'][i])
      )
      .find((source) => source?.cellComponent ?? source?.cellComponentName);

    if (!found?.cellComponent && found?.cellComponentName) {
      found.cellComponent = CellComponentMap[found.cellComponentName];
    }

    const componentName = found?.cellComponentName ?? 'defaultCellComponent';

    return {
      component: found?.cellComponent ?? CellComponentMap[componentName],
      cellConfig: {
        ...(found?.cellConfig ??
          cellData?.cellConfig ??
          row.cellConfig ??
          column.cellConfig ??
          {}),
        cellComponentName: componentName,
      },
    };
  }

  private onCellEventChange(event: CellEvent) {
    if (event.type === CellEventTypes.DELETE_ROW && event.data.apply) {
      this.deleteRow(event.data.row!);
    }

    if (event.type === CellEventTypes.EDIT_ROW && event.data.apply) {
      this.editRow(event.data.rowId, event.data.newData);
    }

    if (event.type === CellEventTypes.CHECKBOX) {
      this.onCheckboxChange(event.data.rowId, event.data.newData);
    }

    this.onCellEvent.emit(event);
    this.cdRef.detectChanges();
  }

  private updateCurrentPageData(page: number, pageSize: number) {
    const start = page * pageSize;
    const end = start + pageSize;
    const currentPageDataFiltered: FlattenedRow[] = this.allFlattenedData
      .filter((row) => !row.isChild)
      .slice(start, end);

    const currentPageData = [];
    for (const row of currentPageDataFiltered) {
      if (row.isParent) {
        // Añado al padre
        currentPageData.push(row);
        this.allFlattenedData
          .filter((r) => r.keyGroup === row.keyGroup)
          .forEach((childRow) => {
            if (childRow.isChild) {
              // Añado a cada uno de los hijos
              currentPageData.push(childRow);
            }
          });
      } else {
        currentPageData.push(row);
      }
    }
    this.flattenedData = this.getFlattenedData(currentPageData);
  }

  private getFlattenedData(data: FlattenedRow[]): FlattenedRow[] {
    return this.groupedRows.length ? this.getGroupRows(data) : data;
  }

  private deleteRow(row: FlattenedRow) {
    const index = this.allFlattenedData.findIndex((r) => r.id === row.id);
    if (index !== -1) {
      this.data.splice(index, 1);
      this.precomputeTemplates();
    }
  }

  private editRow(rowId: string, updatedData: { [x: string]: any }) {
    const index = this.allFlattenedData.findIndex((r) => r.id === rowId);
    if (index !== -1) {
      Object.entries(updatedData).forEach(([key, value]) => {
        if (this.data[index] && this.data[index].data[key]) {
          this.data[index].data[key].value = value;
        }
      });

      this.precomputeTemplates();
    }
  }

  protected onClickEmptyButton = (): void => this.onEmptyButtonEvent.emit();

  onHeaderCheckboxChange(): void {
    this.renderFlag = false;
    this.cdRef.detectChanges();
    const isChecked = this.formGroup.get('checkbox')?.value;
    this.allRowsSelected = isChecked;
    (this.flattenedData || []).forEach((row) => {
      if (
        !row.rowConfig?.['isDisabled'] &&
        row.id &&
        this.formGroup.contains(row.id)
      ) {
        this.formGroup.get(row.id)?.setValue(isChecked, { emitEvent: false });
        // Also update cell value for legacy logic
        row.cells.forEach((cell) => {
          if (cell.column.key === 'checkbox') {
            cell.value = isChecked;
            if (cell.rowData) {
              cell.rowData['checkbox'] = { value: isChecked };
            }
          }
        });
      }
    });
    this.updateHeaderCheckboxState();
    this.emitSelectionChange();
    this.renderFlag = true;
    this.cdRef.detectChanges();
  }

  onCheckboxChange(rowId: string, newValue: boolean): void {
    const row = this.allFlattenedData.find((r) => r.id === rowId);
    if (row && !row.rowConfig['isDisabled']) {
      // Update the FormGroup control for this row
      if (this.formGroup.contains(rowId)) {
        this.formGroup.get(rowId)?.setValue(newValue, { emitEvent: false });
      }
      const checkboxCell = row.cells.find(
        (cell) => cell.column.key === 'checkbox'
      );
      if (checkboxCell) {
        checkboxCell.value = newValue.toString();
        if (checkboxCell.rowData) {
          checkboxCell.rowData['checkbox'] = { value: newValue };
        }
      }
    } else if (row) {
      const originalRow = this.data.find((r) => r.id === row.id);
      if (originalRow) {
        this.onClickDisabledSelection.emit(originalRow);
      }
    }

    this.updateHeaderCheckboxState();
    this.emitSelectionChange();
  }

  private updateHeaderCheckboxState(): void {
    const selectableRows = this.flattenedData.filter(
      (row) =>
        !row.rowConfig['isDisabled'] &&
        row.id &&
        this.formGroup.contains(row.id)
    );
    this.allRowsSelected =
      selectableRows.length > 0 &&
      selectableRows.every((row) => this.formGroup.get(row.id)?.value);
    this.formGroup
      .get('checkbox')
      ?.setValue(this.allRowsSelected, { emitEvent: false });
  }

  emitSelectionChange(): void {
    const selectedRows = this.data.filter((row) => {
      if (!row.id || !this.formGroup.contains(row.id)) return false;
      return this.formGroup.get(row.id)?.value;
    });
    this.onSelectionChange.emit(selectedRows);
  }

  emitRowClick(row: FlattenedRow): void {
    if (this.isGroupRow(row)) {
      return this.toggleGroupCollapsed(row);
    } else if (row.keyGroup && row.isParent) {
      this.flattenedData.forEach((r) => {
        if (r.keyGroup === row.keyGroup) {
          r.isCollapsed = !r.isCollapsed;
        }
      });
    }

    if (this.clickableRows) {
      const rowdata = row.cells[0].rowData;
      this.onRowClick.emit(rowdata);
    }
  }

  updateRowsPerPage(event: number): void {
    this.itemsPerPage = event;
    this.changePage({ page: 0 });
  }

  isGroupRow = (row: FlattenedRow): row is FlattenedGroupRow =>
    (row as FlattenedGroupRow).key !== undefined;

  private toggleGroupCollapsed(row: FlattenedGroupRow): void {
    row.isCollapsed = !row.isCollapsed;
  }

  private getGroupRows(rows: FlattenedRow[]): FlattenedRow[] {
    const updatedRows: FlattenedRow[] = [];

    for (let row of rows) {
      // If the row does not have a keyGroup, add it directly to updatedRows
      if (!row.keyGroup) {
        updatedRows.push(row);
        continue;
      }

      // Find the existing group row for the given keyGroup
      const existingGroup = updatedRows.find(
        (existingRow) =>
          this.isGroupRow(existingRow) &&
          (existingRow as FlattenedGroupRow).key === row.keyGroup
      );

      if (existingGroup) {
        // If a group row exists for this keyGroup, add the current row to its children
        const groupRow = existingGroup as FlattenedGroupRow;
        row.isGrouped = true;
        groupRow.children?.push(row);
        continue;
      }

      // Create a new group row if none exists for this keyGroup
      const groupedRow = this.groupedRows.find(
        (gRow) => gRow.key === row.keyGroup
      );

      // If a keyGroup not have a groupedRow realted, add the row directly
      if (!groupedRow) {
        updatedRows.push(row);
        continue;
      }

      //If not has existing group created but exist, create a new group row
      const newGroupRow = this.createFlattenedGroupRow(groupedRow, row);
      updatedRows.push(newGroupRow);
    }

    return updatedRows;
  }

  private createFlattenedGroupRow(
    groupedRow: GroupRow,
    row: FlattenedRow
  ): FlattenedGroupRow {
    return {
      id: generateUniqueId(),
      key: groupedRow.key,
      name: groupedRow.name,
      isCollapsed: groupedRow.isCollapsed,
      rowComponent: PanelRowComponent,
      rowConfig: {
        colSpan: this.flattenedColumns.length,
      },
      cells: [],
      children: [{ ...row, isGrouped: true }],
    };
  }

  handleColumHeaderKeyEvent(
    event: KeyboardEvent,
    column: FlattenedColumn
  ): void {
    if (event.key === 'Enter') {
      this.onHeaderClick(column);
    }
  }

  onHeaderClick(column: FlattenedColumn): void {
    if (!column.sortable) return;
    if (this.lazy) {
      this.tableService.setCurrentSortColumn(column);
      this.sortOrder =
        this.sortOrder === TableSortOrder.DESC
          ? TableSortOrder.ASC
          : TableSortOrder.DESC;
      this.sortByColumn.emit({
        columnKey: column.key,
        sortOrder: this.sortOrder,
      });
      this.currentSortColumn = column.key;
    } else {
      this.allFlattenedData = this.tableService.sortRows(
        column,
        this.allFlattenedData
      );
      this.currentSortColumn = column.key;

      this.updateCurrentPageData(this.currentPage, this.itemsPerPage);
    }
  }

  getCheckboxValue(row: FlattenedRow): boolean {
    const checkboxCell = row.cells.find(
      (cell) => cell.column.key === 'checkbox'
    );
    return !!(checkboxCell && checkboxCell.value);
  }

  isMobile(): boolean {
    return this.deviceService.isMobileSize();
  }

  toggleSummaryRow(rowId: string) {
    this.summaryRowExpanded[rowId] = !this.summaryRowExpanded[rowId];
  }

  isSummaryRowExpanded(rowId: string): boolean {
    return !!this.summaryRowExpanded[rowId];
  }

  hasExpandableColumns(row: FlattenedRow): boolean {
    return (
      row.cells &&
      row.cells.some(
        (cell) => cell.column && cell.column.mobileCellConfig?.summary === false
      )
    );
  }
}
