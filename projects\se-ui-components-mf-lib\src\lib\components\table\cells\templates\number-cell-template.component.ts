import {
  ChangeDetectionStrategy,
  Component,
  Inject,
  Input,
  LOCALE_ID,
} from '@angular/core';

import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import type {
  CellComponent,
  FlattenedCell,
  NumberCellConfig,
} from '../cells.model';

@Component({
  selector: 'se-number-cell',
  template: `
    <span [ngStyle]="cellConfig['ngStyle']">
      {{
        value
          | number
            : cellConfig.digitsInfo ?? DIGITS_INFO_DEFAULT
            : cellConfig.locale ?? LOCALE_DEFAULT
      }}
    </span>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NumberCellComponent implements CellComponent {
  protected readonly DIGITS_INFO_DEFAULT = '1.2-2';
  protected readonly LOCALE_DEFAULT = this.localeDefault ?? 'ca-ES';

  constructor(@Inject(LOCALE_ID) private readonly localeDefault: string) {
    // Intencionadamente vacío
  }

  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: NumberCellConfig;
}
