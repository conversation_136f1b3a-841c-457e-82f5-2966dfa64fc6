import { Type } from '@angular/core';
import { CellComponent } from './cells.model';
import { ActionsCellComponent } from './templates/actions-cell-template.component';
import { ButtonCellComponent } from './templates/button-cell-template.component';
import { CheckboxCellComponent } from './templates/checkbox-cell-template.component';
import { CollapsibleCellComponent } from './templates/collapsible-cell-template.component';
import { CurrencyCellComponent } from './templates/currency-cell-template.component';
import { DateCellComponent } from './templates/date-cell-template.component';
import { DefaultCellComponent } from './templates/default-cell-template.component';
import { SeparatedTooltipCellComponent } from './templates/separated-tooltip-cell-template.component';
import { DownloadCellComponent } from './templates/download-cell-template.component';
import { DropdownButtonCellComponent } from './templates/dropdown-button-cell-template';
import { DropdownCellComponent } from './templates/dropdown-cell-template.component';
import { EditCellComponent } from './templates/edit-cell-template.component';
import { IconCellComponent } from './templates/icon-cell-template.component';
import { IconActionsCellComponent } from './templates/icons-action-cell-template.component';
import { InputCellComponent } from './templates/input-cell-template.component';
import { LinkCellComponent } from './templates/link-cell-template.component';
import { NumberCellComponent } from './templates/number-cell-template.component';
import { StyledTextCellComponent } from './templates/styled-text-cell-template.component';
import { TagCellComponent } from './templates/tag-cell-template.component';

interface CellComponentRegistry {
  defaultCellComponent: Type<CellComponent>;
  editCellComponent: Type<CellComponent>;
  buttonCellComponent: Type<CellComponent>;
  dateCellComponent: Type<CellComponent>;
  inputCellComponent: Type<CellComponent>;
  currencyCellComponent: Type<CellComponent>;
  styledTextCellComponent: Type<CellComponent>;
  downloadCellComponent: Type<CellComponent>;
  dropdownCellComponent: Type<CellComponent>;
  tagCellComponent: Type<CellComponent>;
  checkboxCellComponent: Type<CellComponent>;
  actionsCellComponent: Type<CellComponent>;
  dropdownButtonCellComponent: Type<CellComponent>;
  iconActionsCellComponent: Type<CellComponent>;
  numberCellComponent: Type<CellComponent>;
  collapsibleCellComponent: Type<CellComponent>;
  linkCellComponent: Type<CellComponent>;
  iconCellComponent: Type<CellComponent>;
  separatedTooltipCellComponent: Type<CellComponent>;
}

export const CellComponentMap: CellComponentRegistry = {
  defaultCellComponent: DefaultCellComponent,
  editCellComponent: EditCellComponent,
  buttonCellComponent: ButtonCellComponent,
  dateCellComponent: DateCellComponent,
  inputCellComponent: InputCellComponent,
  currencyCellComponent: CurrencyCellComponent,
  styledTextCellComponent: StyledTextCellComponent,
  downloadCellComponent: DownloadCellComponent,
  dropdownCellComponent: DropdownCellComponent,
  tagCellComponent: TagCellComponent,
  checkboxCellComponent: CheckboxCellComponent,
  actionsCellComponent: ActionsCellComponent,
  dropdownButtonCellComponent: DropdownButtonCellComponent,
  iconActionsCellComponent: IconActionsCellComponent,
  numberCellComponent: NumberCellComponent,
  collapsibleCellComponent: CollapsibleCellComponent,
  linkCellComponent: LinkCellComponent,
  iconCellComponent: IconCellComponent,
  separatedTooltipCellComponent: SeparatedTooltipCellComponent,
};

// Cell components that are actionable and should not trigger row click events
export const ActionableComponents = [
  'buttonCellComponent',
  'downloadCellComponent',
  'dropdownCellComponent',
  'actionsCellComponent',
  'dropdownButtonCellComponent',
  'editCellComponent',
  'checkboxCellComponent',
  'inputCellComponent',
  'iconActionsCellComponent',
  'linkCellComponent',
];

export type CellComponentKeys = keyof CellComponentRegistry;
