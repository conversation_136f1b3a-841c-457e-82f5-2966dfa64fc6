import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import type { Nullable } from '../../models';
import {
  CHANNEL_QUERY_PARAM_NAME,
  ChannelEnum,
  ListMfConfigurationResponse,
  MfConfiguration,
  MfConfigurationPersonal,
  MfConfigurationPersonalResponse,
  MfConfigurationResponse,
} from '../../models/mf-configuration.model';
import { SeDataStorageService } from '../data-storage/data-storage.service';
import { SeHttpRequest } from '../http/http-service.model';
import { SeHttpService } from '../http/http-service.service';

const BASE_URL_CONFIGURATION = '/api/gestio-configuracio';
const BASE_URL_CONFIGURATION_PERSONAL = '/api/gestio-configuracio-personal';

@Injectable({
  providedIn: 'root',
})
export class SeMfConfigurationService {
  private configuration: MfConfiguration[] = [];

  constructor(
    private readonly httpService: SeHttpService,
    private readonly dataStorage: SeDataStorageService
  ) {
    // Intencionadamente vacío
  }

  // GET APP CONFIGURATION - LIST
  getAppConfiguration = (): Observable<ListMfConfigurationResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: BASE_URL_CONFIGURATION,
      url: `/llistat`,
      method: 'post',
      body: {},
      spinner: false,
    };

    return this.httpService.post(httpRequest);
  };

  getMfConfiguration = (MF: string): Observable<MfConfigurationResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: BASE_URL_CONFIGURATION,
      url: `/${MF}`,
      method: 'get',
      body: {},
      spinner: false,
    };

    return this.httpService.get(httpRequest);
  };

  getMfConfigurationPersonal = (
    MF: string
  ): Observable<MfConfigurationPersonalResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: BASE_URL_CONFIGURATION_PERSONAL,
      url: `/${MF}`,
      method: 'get',
      body: {},
      spinner: false,
    };

    return this.httpService.get(httpRequest);
  };

  // SAVE PERSONAL CONFIGURATION OF MF - PUT
  setPersonalConfiguration = (
    request: MfConfigurationPersonal
  ): Observable<MfConfigurationPersonalResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: BASE_URL_CONFIGURATION_PERSONAL,
      url: '',
      method: 'put',
      body: { mapConfig: request.mapConfig },
    };

    return this.httpService.put(httpRequest);
  };

  // SAVE MF Personal
  setMfConfiguration = (conf: MfConfiguration): void => {
    let index = this.configuration.findIndex(
      (value) => value.aplicacio === conf.aplicacio
    );

    index = index > -1 ? index : this.configuration.length;

    this.configuration[index] = conf;

    this.dataStorage.setItem(conf.aplicacio, conf);
  };

  // RETURN ALL CONFIGURATION OBJECT
  getConfigurationList = (): MfConfiguration[] => this.configuration;

  // SAVE MICRO FRONTENDS CONFIGURATION ON SESSION STORAGE
  setMfConfigurationList = (configuration: MfConfiguration[]): void => {
    this.configuration = configuration ?? [];
    //Filter active
    // let dataConf = this.configuration.filter(
    // 	(conf) => conf.isActive === true
    // );

    this.dataStorage.setItem('pt-configuration', this.configuration);

    this.configuration.forEach((value) => {
      this.dataStorage.setItem(value.aplicacio, value);
    });
  };

  getIsMobileApp(): boolean {
    return this.getChannel() === ChannelEnum.APP;
  }

  getChannel(): Nullable<string> {
    const storedChannel = this.dataStorage.getItem(CHANNEL_QUERY_PARAM_NAME);
    const paramChannel = new URL(window.location.href).searchParams.get(
      CHANNEL_QUERY_PARAM_NAME
    );
    if (paramChannel && paramChannel !== storedChannel) {
      this.dataStorage.setItem(CHANNEL_QUERY_PARAM_NAME, paramChannel);
    }
    return this.dataStorage.getItem(CHANNEL_QUERY_PARAM_NAME);
  }
}
