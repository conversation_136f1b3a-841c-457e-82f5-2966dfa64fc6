import {
  HttpErrorResponse,
  HttpEvent,
  HttpH<PERSON>ler,
  HttpInterceptor,
  HttpRequest,
  HttpResponse,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Observable, from, throwError } from 'rxjs';
import {
  catchError,
  filter,
  finalize,
  switchMap,
  take,
  tap,
} from 'rxjs/operators';
import { SeSpinnerService } from '../../components/spinner/spinner.service';
import { SeLoginResponse } from '../auth/auth.model';
import { SeLoginService } from '../login/login.service';
import { SeMessage, SeMessageI } from '../message/message.model';
import { SeMessageService } from '../message/message.service';
import { SeHttpBackendMessage, SeHttpResponse } from './http-service.model';

@Injectable({
  providedIn: 'root',
})
export class SeHttpInterceptorService implements HttpInterceptor {
  private allowedHttpErrors: number[] = [
    400, 401, 403, 404, 405, 408, 500, 503,
  ];
  public seguretatBaseUrl = '/api/seguretat';
  private TRACKING_HEADER: string = 'trackingId';
  // Borrar cuando todos los micros esten actualizados a la ultima version de commons-client-lib
  private TRACKING_HEADER_TEMP: string = 'x-tracking-id';
  private totalHttpRequests: number = 0;
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(
    null
  );

  constructor(
    private msgService: SeMessageService,
    private spinnerService: SeSpinnerService,
    private translateService: TranslateService,
    private loginService: SeLoginService
  ) { }

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    this.totalHttpRequests++;

    // Check if suppressErrorMessage is set in request headers
    const suppressErrorMessage = request.headers.get('Suppress-Error-Message') === 'true';

    return next.handle(request).pipe(
      tap((ev: HttpEvent<any>) => {
        if (ev instanceof HttpResponse && !suppressErrorMessage) {
          try {
            let trackingId =
              ev.headers.get(this.TRACKING_HEADER) ||
              ev.headers.get(this.TRACKING_HEADER_TEMP)!;
;
            // Tratamos el caso de body como JSON
            if (typeof ev.body === 'string')
              // Extraemos los mensajes de la respuesta si los hay
              this.extractMessages(JSON.parse(ev.body), trackingId);

            // Tratamos el caso de body como Blob
            else if ( ev.body instanceof Blob && ev.body.type === 'application/json') {
              // Lo leemos como texto
              ev.body!.text().then((text: string) => {
                // Extraemos los mensajes de la respuesta si los hay
                this.extractMessages(JSON.parse(text), trackingId);
              });
            }

          } catch (error) {
            console.warn('Error trying to parse the response body: ', error);
          }
        }
      }),
      catchError((response: HttpErrorResponse) => {
        if(!suppressErrorMessage){
          if (response instanceof HttpErrorResponse && response.status === 401) {
            // Error: token
            return this.handle401Error(request, next);
          } else if (response instanceof HttpErrorResponse) {
            let trackingId =
              response.headers.get(this.TRACKING_HEADER) ||
              response.headers.get(this.TRACKING_HEADER_TEMP)!;
            if (!trackingId)
              trackingId =
                request.headers.get(this.TRACKING_HEADER) ||
                request.headers.get(this.TRACKING_HEADER_TEMP)!;
            // Error: other
            try {
              if (
                response.error &&
                JSON.parse(response.error) instanceof SeHttpBackendMessage
              ) {
                // Show backend custom message
                  const exceptions = JSON.parse(response.error).map(
                    (error: SeHttpBackendMessage) =>
                      this.setMessage(error, trackingId)
                  );
                  this.msgService.addMessages(exceptions);
              } else if (this.allowedHttpErrors.indexOf(response.status) !== -1) {
                // Show default http error messages (4XX & 5XX)
                  const exception: SeMessageI = this.customHttpErrorMsg(
                    response,
                    trackingId
                  );
                  this.msgService.addMessages([exception]);
              } else {
                  const exception: SeMessageI = JSON.parse(response.error);
                  this.msgService.addMessages([exception]);
              }
            } catch (err) {
              // Show default http error messages (4XX & 5XX)
                const exception: SeMessageI = this.customHttpErrorMsg(
                  response,
                  trackingId
                );
                this.msgService.addMessages([exception]);
            }
          }
        }
        return throwError(response);
      }),
      finalize(() => {
        this.totalHttpRequests--;
        if (
          this.totalHttpRequests === 0 && 
          !this.spinnerService.isSpinnerManualStopEnable()
        ) {
          this.spinnerService.stop();
        }
      })
    );
  }

  // Add token
  private addToken = (
    request: HttpRequest<any>,
    token: string
  ): HttpRequest<any> =>
    request.clone({ setHeaders: { Authorization: token } });

  // Refresh token > Call endpoint
  refreshToken = (): Observable<SeLoginResponse> =>
    from(this.loginService.login(true));

  // Refresh token
  private handle401Error = (
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> => {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      return this.refreshToken().pipe(
        switchMap((response: SeLoginResponse) => {
          this.isRefreshing = false;
          this.refreshTokenSubject.next(response.content.tokenJwt);
          return next.handle(this.addToken(request, response.content.tokenJwt));
        })
      );
    } else {
      return this.refreshTokenSubject.pipe(
        filter((token) => token != null),
        take(1),
        switchMap((response) => next.handle(this.addToken(request, response)))
      );
    }
  };

  // Set message
  setMessage = (
    backendMessage: SeHttpBackendMessage,
    trackingId: string
  ): SeMessage => {
    // Translate title
    let title: string;
    const titleNotTranslated: boolean = this.translateService
      .instant(`UI_COMPONENTS.EXCEPTIONS.CODES.${backendMessage.title}`)
      .includes(backendMessage.title);
    if (titleNotTranslated) {
      /*
				Si después de traducir el titulo sigue habiendo la key en el mismo valor traducido,
				es porque no se ha traducido (no existe la key en el json de traducción).
				Por lo que se mostrará la key recibida de backend.
			*/
      title = backendMessage.title!;
    } else {
      title = `UI_COMPONENTS.EXCEPTIONS.CODES.${backendMessage.title}`;
    }

    // Translate subtitle
    let subtitle: string;
    const subtitleNotTranslated: boolean = this.translateService
      .instant(`UI_COMPONENTS.EXCEPTIONS.MSG.${backendMessage.subtitle}`)
      .includes(backendMessage.subtitle);
    if (subtitleNotTranslated) {
      /*
				Si después de traducir el subtitulo sigue habiendo la key en el mismo valor traducido,
				es porque no se ha traducido (no existe la key en el json de traducción).
				Por lo que se mostrará la key recibida de backend.
			*/
      const codiError = backendMessage?.subtitleParams?.['codiError'] ?? backendMessage.subtitle
      subtitle = this.translateService
      .instant(`UI_COMPONENTS.EXCEPTIONS.MSG.DEFAULT_MSG`, {codiError}) ;
    } else {
      subtitle = `UI_COMPONENTS.EXCEPTIONS.MSG.${backendMessage.subtitle}`;
    }

    return new SeMessage(
      backendMessage.severity,
      backendMessage.code === 666 && backendMessage.title
        ? backendMessage.title
        : title || 'UI_COMPONENTS.EXCEPTIONS.CODES.ERROR',
      backendMessage.code === 666 && backendMessage.subtitle
        ? backendMessage.subtitle
        : subtitle || 'UI_COMPONENTS.EXCEPTIONS.MSG.500',
      true,
      this.msgService.deleteMessage,
      backendMessage.titleParams,
      backendMessage.subtitleParams,
      false,
      false,
      trackingId
    );
  };

  private extractMessages(response: SeHttpResponse<any>, trackingId: string) {
    if (response?.messages?.length && response.messages.length > 0) {
      const exceptions = response.messages.map(
        (message: SeHttpBackendMessage) => this.setMessage(message, trackingId)
      );
      this.msgService.addMessages(exceptions);
    }
  }

  // Custom error messages for HTTP default errors
  customHttpErrorMsg(
    response: HttpErrorResponse,
    trackingId: string
  ): SeMessageI {
    let exception: SeMessageI = {
      severity: 'error',
      title: null,
      subtitle: null,
      closable: true,
      closableFn: this.msgService.deleteMessage,
      trackingId: trackingId,
    };
    switch (response.status) {
      case 400:
        exception.title = 'UI_COMPONENTS.EXCEPTIONS.CODES.400';
        exception.subtitle = 'UI_COMPONENTS.EXCEPTIONS.MSG.400';
        break;
      case 401:
        exception.title = 'UI_COMPONENTS.EXCEPTIONS.CODES.401';
        exception.subtitle = 'UI_COMPONENTS.EXCEPTIONS.MSG.401';
        break;
      case 403:
        exception.title = 'UI_COMPONENTS.EXCEPTIONS.CODES.403';
        exception.subtitle = 'UI_COMPONENTS.EXCEPTIONS.MSG.403';
        break;
      case 404:
        exception.title = 'UI_COMPONENTS.EXCEPTIONS.CODES.404';
        exception.subtitle = 'UI_COMPONENTS.EXCEPTIONS.MSG.404';
        break;
      case 405:
        exception.title = 'UI_COMPONENTS.EXCEPTIONS.CODES.405';
        exception.subtitle = 'UI_COMPONENTS.EXCEPTIONS.MSG.405';
        break;
      case 408:
        exception.title = 'UI_COMPONENTS.EXCEPTIONS.CODES.408';
        exception.subtitle = 'UI_COMPONENTS.EXCEPTIONS.MSG.408';
        break;
      case 500:
        exception.title = 'UI_COMPONENTS.EXCEPTIONS.CODES.500';
        exception.subtitle = 'UI_COMPONENTS.EXCEPTIONS.MSG.500';
        break;
      case 503:
        exception.title = 'UI_COMPONENTS.EXCEPTIONS.CODES.503';
        exception.subtitle = 'UI_COMPONENTS.EXCEPTIONS.MSG.503';
        break;
      default:
        exception.title = 'UI_COMPONENTS.EXCEPTIONS.MSG.ERROR';
        exception.subtitle = 'UI_COMPONENTS.EXCEPTIONS.MSG.500';
        break;
    }
    return exception;
  }
}
