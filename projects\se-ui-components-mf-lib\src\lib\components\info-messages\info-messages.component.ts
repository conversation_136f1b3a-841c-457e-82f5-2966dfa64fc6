import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SeButton } from '../button';

@Component({
  selector: 'se-info-messages',
  templateUrl: './info-messages.component.html',
  styleUrls: ['./info-messages.component.scss'],
})
export class InfoMessagesComponent {
  /**
   *  Unique id for the info message
   * @type {string}
   * @memberof InfoMessagesComponent
   */
  @Input() id: string = 'info-message-id-' + Math.random();

  /**
   * Title of the info message
   * @type {string}
   * @memberof InfoMessagesComponent
   */
  @Input() title!: string;

  /**
   * Subtitle of the info message
   * @type {string}
   * @memberof InfoMessagesComponent
   */
  @Input() subtitle: string | undefined

  /**
   * Represents the text of the info message.
   * Can be a string for a paragraph or a list of strings for a li.
   * @type {(string | string[])}
   * @memberof InfoMessagesComponent
   */
  @Input() text!: string[] | string;

  /**
   * name of the icon to be displayed in the info message
   * @type {string}
   * @memberof InfoMessagesComponent
   */
  @Input() image?: string;

  @Input() theme: 'primary' | 'secondary' | 'primary_no_bullets' | 'secondary_no_bullets' = 'primary';

  /**
  * @property {SeButton} [primaryButton] - Configuration for the primary button.
  * */
  @Input() primaryButton?: SeButton;
  /**
  * @property {SeButton} [secondaryButton] - Configuration for the secondary button.
  * */
  @Input() secondaryButton?: SeButton;
  /**
  * @event primaryButtonClick - Event emitted when the primary button is clicked.
  */
  @Output() primaryButtonClick = new EventEmitter<void>();
  /**
  * @event secondaryButtonClick - Event emitted when the secondary button is clicked.
  */
  @Output() secondaryButtonClick = new EventEmitter<void>();

  get src(): string {
    return `/mf/pt-commons-mf/images/${this.image}.png`;
  }

  get isArrayList(): boolean {
    return Array.isArray(this.text);
  }

  get list(): string[] {
    return this.isArrayList ? (this.text as string[]) : [];
  }
}
