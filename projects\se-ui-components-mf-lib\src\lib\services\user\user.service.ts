import { Injectable } from '@angular/core';
import { SeAuthService } from '..';

@Injectable({
  providedIn: 'root'
})
export class SeUserService {

  get isLegalUser(): boolean {
    const user = this.authService.getSessionStorageUser();
    return !!user.esJuridico
  }

  get isCivilServant(): boolean {
    const user = this.authService.getSessionStorageUser();
    return (user?.esEmpleadoPublico && user?.esAtesa) ?? false;
  }

  constructor(
    private authService: SeAuthService,
  ) { }

  /**
   * Devuelve el NIF del usuario que ha iniciado sesión. Si es persona
   * física devolverá el DNI y si es persona jurídica devolverá el
   * código de identificación fiscal.
   */
  public getNIF(): string {
    const user = this.authService.getSessionStorageUser();
    if (!user) return '';

    const id = this.isLegalUser ? user.companyId : user.nif;

    return id ?? '';
  }

  /**
   * Devuelve el nombre del usuario que ha iniciado sesión. Si es
   * persona física devolverá el nombre y apellidos. Si es persona
   * jurídica devolverá la razón social.
   */
  public getName(): string {
    const user = this.authService.getSessionStorageUser();
    if (!user) return '';

    const name = this.isLegalUser
      ? user.companyName
      : user.nombreCompleto;

    return name ?? '';
  }

}
