import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InputComponent } from './input.component';
import { SeFormControlErrorModule } from '../form-control-error/form-control-error.module';
import { InputTextModule } from 'primeng/inputtext';
import { SeSharedModule } from '../../shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    SeSharedModule,
    SeFormControlErrorModule,
    InputTextModule,
    FormsModule,
    TranslateModule,
    NgbTooltipModule
  ],
  declarations: [InputComponent],
  exports: [InputComponent],
})
export class SeInputModule {}
