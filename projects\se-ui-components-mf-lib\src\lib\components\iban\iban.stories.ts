import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { SeValidations } from '../../services';
import { IbanComponent } from './iban.component';
import { SeIbanModule } from './iban.module';

const meta: Meta<IbanComponent> = {
  title: 'Components/Iban',
  component: IbanComponent,
  decorators: [
    moduleMetadata({
      imports: [SeIbanModule, ReactiveFormsModule],
    }),
  ],
  args: {
    id: 'labelId',
    label: 'Iban label',
    placeholder: 'ES12 1234 1234 1234 1234 1234',
    mask: 'aa99 9999 9999 9999 9999 9999',
    slotChar: ' '
  },
  argTypes: {
    id: {
      description: 'Id for the iban field.',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
    label: {
      description: 'Label for the iban field.',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'Iban label' } },
    },
    placeholder: {
      description: 'Placeholder text for the iban field.',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'ES12 1234 1234 1234 1234 1234' } },
    },
    slotChar: {
      description: 'Separation symbol between masks.',
      control: { type: 'text' },
      table: { defaultValue: { summary: ' ' } },
    },
    mask: {
      description: 'Mask of the IBAN input.',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'aa99 9999 9999 9999 9999 9999' } },
    },
    disabled: {
      description: 'Disables the component',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
  },
  tags: ['autodocs'],
  render: (args) => ({
    template: `
    <form [formGroup]="form" style="width: 350px">
      <se-iban 
        [id]="id"
        [label]="label"
        [placeholder]="placeholder"
        [mask]="mask"
        [slotChar]="slotChar"
        [disabled]="disabled"
        formControlName="ibanShowcase"
        (key)="handleKey($event)">
      </se-iban>
    </form>
    `,
    props: {
      ...args,
      form: (() => {
        const form = new FormGroup({
          ibanShowcase: new FormControl(null, SeValidations.listValidations([
            {
              validator: SeValidations.iban,
              translationByKey: {
                ibanValue: 'UI_COMPONENTS.VALIDATIONS_ERRORS.ibanValue',
                ibanPattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.ibanPattern'
              }
            },
            {
              validator: Validators.required
            }
          ])),
        });

        form.valueChanges.subscribe(console.log)
        
        return form;
      })(),
      handleKey: console.log
    },
  }),
};

export default meta;
type Story = StoryObj<IbanComponent>;

export const DefaultInput: Story = {};

