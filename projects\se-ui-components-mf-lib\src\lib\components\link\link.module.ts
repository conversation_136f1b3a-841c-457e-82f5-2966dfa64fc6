import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {LinkComponent} from "./link.component";
import {SeSharedModule} from "../../shared/shared.module";
import {TranslateModule} from "@ngx-translate/core";

@NgModule({
  declarations: [LinkComponent],
  imports: [CommonModule, SeSharedModule, TranslateModule.forChild()],
  exports: [LinkComponent],
})
export class SeLinkModule {
}
