import { NgModule } from '@angular/core';
import { ConfirmationMessageComponent } from './confirmation-message.component';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { SeButtonModule } from '../button/button.module';
import { SafeHtmlPipe } from '../../pipes';

@NgModule({
  declarations: [ConfirmationMessageComponent],
  imports: [CommonModule, TranslateModule.forChild(), SeButtonModule, SafeHtmlPipe],
  exports: [ConfirmationMessageComponent],
})
export class SeConfirmationMessageModule {}
