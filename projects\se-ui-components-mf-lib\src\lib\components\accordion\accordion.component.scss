.se-accordion {

  &__header {
    border-block: 1px solid var(--gray-300);
    min-height: 50px;

    &--toggler {
      color: var(--color-primary-action);
      border-radius: 50%;
      border: 1px solid var(--color-primary-action);
      min-width: 32px;
      height: 32px;
      background: transparent;
      transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      text-decoration: none;
      overflow: hidden;
      position: relative;


      &:focus {
        outline: 0;
      }

      &:hover {
        background-color: var(--color-blue-300);
      }
    }

    &:hover {
      background-color: var(--color-blue-200);

      > button {
        background-color: var(--color-blue-300);
      }
    }

    .icon-button {
      all: initial;
      display: flex;
      cursor: pointer;
      border-radius: 8px;
      line-height: var(--line-sm);
      margin-left: 5px;
      align-items: center;

      &:focus-visible {
        outline: var(--color-black) auto 1px;
      }

      .tooltip-icon {
        margin-left: 0px;
        position: static;
      }
    }
  }

  &__content {
    overflow: hidden;
    border-bottom: 1px solid var(--gray-300);

    &.collapsed {
      height: 0px;
      border-bottom: none;
    }
  }
}
