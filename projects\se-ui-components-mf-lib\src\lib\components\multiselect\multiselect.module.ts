import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MultiselectComponent } from './multiselect.component';
import { MultiSelectModule } from 'primeng/multiselect';
import { SeFormControlErrorModule } from '../form-control-error/form-control-error.module';
import { SeSharedModule } from '../../shared/shared.module';
import { SeInputModule } from '../input/input.module';
import { ChevronDownIcon } from 'primeng/icons/chevrondown';
import { ChevronUpIcon } from 'primeng/icons/chevronup';
import { TimesIcon } from 'primeng/icons/times';

@NgModule({
  declarations: [MultiselectComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MultiSelectModule,
    SeFormControlErrorModule,
    SeSharedModule,
    SeInputModule,
    ChevronDownIcon,
    ChevronUpIcon,
    TimesIcon
  ],
  exports: [MultiselectComponent],
})
export class SeMultiselectModule {}
