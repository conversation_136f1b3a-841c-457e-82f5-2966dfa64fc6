import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import { CellComponent, CellConfig, FlattenedCell } from '../cells.model';

@Component({
  selector: 'se-styled-text-cell',
  template: `
    <span class="wrap-break-word" [ngStyle]="cellConfig.ngStyle">
      {{ value | translate }}
    </span>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StyledTextCellComponent implements CellComponent {
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig<{ ngStyle?: Record<string, any> }>;
}
