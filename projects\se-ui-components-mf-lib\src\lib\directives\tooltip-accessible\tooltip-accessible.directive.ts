import {
  AfterViewInit,
  Directive,
  ElementRef,
  HostListener,
  Inject,
  Input,
  Ng<PERSON>one,
  PLATFORM_ID,
  Renderer2,
  TemplateRef,
  ViewContainerRef,
} from '@angular/core';
import { PrimeNGConfig } from 'primeng/api';
import { Tooltip } from 'primeng/tooltip';

@Directive({
  selector: '[pTooltipAccessible]',
})
// tslint:disable-next-line:directive-class-suffix
export class TooltipAccessibleDirective extends Tooltip implements AfterViewInit {

  @Input('pTooltipAccessible') set textAccessible(text: string | TemplateRef<HTMLElement> | undefined) {
    this.setOption({ tooltipLabel: text });
  }

  @HostListener('document:click', ['$event']) onDocumentClick(event: PointerEvent) {
    const nativeElement: any = this.el1.nativeElement;
    const clickedInside: boolean = nativeElement.contains(event.target);
    if (clickedInside) {
      this.activate();
    } else {
      this.deactivate();
    }
  }

  constructor(
    @Inject(PLATFORM_ID) private platformId1: any,
    public el1: ElementRef,
    public zone1: NgZone,
    public config1: PrimeNGConfig,
    private renderer1: Renderer2,
    private viewContainer1: ViewContainerRef
  ) {
    super(platformId1, el1, zone1, config1, renderer1, viewContainer1);
  }

  public override ngAfterViewInit(): void {
    this.zone.runOutsideAngular(() => {
      switch (this.tooltipEvent) {
        case 'hover':
          this.initHoverForTooltipEvent();
          break;
        case 'focus':
          this.initFocusForTooltipEvent();
          break;
        case 'hover-focus':
          this.initHoverForTooltipEvent();
          this.initFocusForTooltipEvent();
          break;
      }
    });
  }

  private initHoverForTooltipEvent(): void {
    this.mouseEnterListener = this.onMouseEnter.bind(this);
    this.mouseLeaveListener = this.onMouseLeave.bind(this);
    this.clickListener = this.onInputClick.bind(this);
    this.el.nativeElement.addEventListener(
      'mouseenter',
      this.mouseEnterListener
    );
    this.el.nativeElement.addEventListener(
      'mouseleave',
      this.mouseLeaveListener
    );
    this.el.nativeElement.addEventListener('click', this.clickListener);
  }

  private initFocusForTooltipEvent(): void {
    this.focusListener = this.onFocus.bind(this);
    this.blurListener = this.onBlur.bind(this);
    this.el.nativeElement.addEventListener('focus', this.focusListener);
    this.el.nativeElement.addEventListener('blur', this.blurListener);
  }
}
