@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

.alert {
  position: relative;
  font-family: var(--font-primary);
  width: 100%;
  padding: 13px;
  border-radius: 4px;
  display: flex;
  align-items: flex-start;

  &__content {
    margin: 0 8px;
    flex-grow: 1;

    ::ng-deep.title {
      color: var(--color-gray-700);
      align-self: stretch;
      font-size: var(--text-sm);
      line-height: var(--line-md);

      p {
        font-size: var(--text-sm);
        line-height: var(--line-md);
        margin-bottom: 0;
      }

      &.bold p {
        font-weight: var(--font-bold);
      }
    }

    ::ng-deep.subtitle {
      color: var(--color-gray-700);
    }

    ::ng-deep.content-container {
      color: var(--color-gray-700);

      .text-sm {
        color: var(--color-gray-700);
      }

      p,
      span {
        font-size: var(--text-sm);
        line-height: var(--line-sm);

        &:last-child {
          margin-bottom: 0;
        }
      }

      a {
        font-size: var(--text-sm);
        color: var(--color-blue-500);
        font-weight: var(--font-regular);
        text-decoration: none;
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  &__left-content {
    width: 24px;
    height: 24px;

    .icon {
      display: inline-flex;
      width: 24px;
      height: 24px;
      font-size: 24px;
      flex-shrink: 0;
      margin: 0;
    }
  }

  &__right-content {
    width: 24px;
    height: 24px;

    .close-icon {
      color: var(--color-gray-700);
      text-align: center;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      width: 24px;
      height: 24px;
      background: transparent;
      border-radius: 99px;
      border: none;
      cursor: pointer;

      & > i {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }

      &:hover {
        border: 1px solid var(--color-gray-700);
      }
    }
  }

  &.info {
    border: 0.5px solid var(--color-blue-400);
    background-color: var(--color-blue-200);

    & .alert__left-content .icon {
      color: var(--color-blue-500);
    }
  }

  &.error {
    border: 0.5px solid var(--color-red-200);
    background: var(--color-red-50);

    & .alert__left-content .icon {
      color: var(--color-red-400);
    }
  }

  &.warning {
    border: 0.5px solid var(--color-orange-300);
    background: var(--color-orange-50);

    & .alert__left-content .icon {
      color: var(--color-orange-300);
    }
  }

  &.success {
    border: 0.5px solid var(--color-green-300);
    background: var(--color-green-50);

    & .alert__left-content .icon {
      color: var(--color-green-300);
    }
  }

  &.padding-normal {
    padding: 13px 16px !important;
  }

  &.neutral {
    border: 0.5px solid var(--color-gray-300);
    background: var(--color-gray-100);
    margin-left: 0;
    padding-left: 0;

    & .alert__content,
    & .alert__left-content {
      margin-left: 0;
      padding-left: 0;
    }
  }

  &__ng_content {
    margin-top: 8px;
    margin-bottom: 0;

    > p {
      font-size: var(--text-sm);
      line-height: var(--line-sm);
    }
  }

  &__list {
    margin-bottom: 0;
    padding-left: 28px;
    list-style: disc !important;

    > li {
      line-height: var(--line-sm);
      font-size: var(--text-xs);
      color: var(--color-gray-700) !important;

      &::marker {
        unicode-bidi: isolate;
        font-variant-numeric: tabular-nums;
        text-transform: none;
        text-indent: 0px !important;
        text-align: start !important;
        text-align-last: start !important;
      }
    }
  }

  &__list--ordered {
    padding-left: 1rem;
    margin-bottom: 0;

    > li {
      line-height: var(--line-sm);
      font-size: var(--text-xs);

      > ol {
        padding-left: 1rem;

        &.letter-parenthesis {
          padding-left: 0;
          counter-reset: alpha;
          > li {
            list-style: none;
            &:before {
              counter-increment: alpha;
              content: counter(alpha, lower-alpha) ") ";
            }
          }
        }

        &.number-parenthesis {
          padding-left: 0;
          counter-reset: decimal;
          > li {
            list-style: none;
            &:before {
              counter-increment: decimal;
              content: counter(decimal) ") ";
            }
          }
        }
      }
    }
  }
  .collapse-button {
    text-wrap: nowrap;
  }
}

//RESPONSIVE
// @media (max-width: 767px)
@include media-breakpoint-down(sm) {
  .alert {
    &__content .m-md-left {
      margin-left: -32px;
    }
  }
}
