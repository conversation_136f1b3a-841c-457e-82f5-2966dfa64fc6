import { action } from '@storybook/addon-actions';
import { moduleMetadata } from '@storybook/angular';
import { of } from 'rxjs';
import {
  SeDataStorageService,
  SeAuthService,
  SeLoginService
} from '../../services';
import { UserInfoBarComponent } from './user-info-bar.component';
import { SeUserInfoBarModule } from './user-info-bar.module';

export default {
  title: 'Components/User Info Bar',
  component: UserInfoBarComponent,
  decorators: [
    moduleMetadata({
      imports: [SeUserInfoBarModule],
      providers: [
        {
          provide: SeLoginService,
          useValue: {
            login: () =>
              of({
                content: {
                  usuario: {
                    nifPresentador: 'NIF123',
                    nombrePresentador: 'John',
                  },
                },
              }),
            logout: () => of('Logout successful'),
          },
        },
        {
          provide: SeAuthService,
          useValue: {
            validCookieToken: () => true,
          },
        },
        { provide: SeDataStorageService, useValue: {} },
      ],
    }),
  ],
  argTypes: {
    id: {
      control: 'text',
      description: 'The ID of the component.',
      table: { defaultValue: { summary: 'presentador' } },
    },
    wrapperClass: {
      control: 'text',
      description: 'Wrapper CSS class for the component.',
    },
    name: {
      control: 'text',
      description: 'Name of the presenter.',
    },
    namePrefix: {
      control: 'text',
      description: 'Prefix for the name.',
    },
    nif: {
      control: 'text',
      description: 'NIF of the presenter.',
    },
    secured: {
      control: 'boolean',
      description: 'Flag to indicate if login is required.',
      table: { defaultValue: { summary: false } },
    },
    isLogout: {
      control: 'boolean',
      description: 'Flag to indicate if logout is enabled.',
      table: { defaultValue: { summary: false } },
    },
    deleteDataStorage: {
      control: 'text',
      description: 'Key for the data to be deleted from storage upon logout.',
    },
    icon: {
      control: 'text',
      description: 'Icon for the component.',
    },
    loginEvent: {
      action: 'loginEvent triggered',
      description: 'Event triggered when login is successful.',
    },
    logoutEvent: {
      action: 'logoutEvent triggered',
      description: 'Event triggered when logout is successful.',
    },
  },
  args: {
    id: 'presentador-1',
    wrapperClass: 'custom-class',
    name: 'John Doe',
    namePrefix: 'Sr.',
    nif: 'NIF12345',
    secured: true,
    isLogout: false,
    deleteDataStorage: 'sessionKey',
    icon: 'user-icon',
  },
};

export const Default = (args: any) => ({
  props: {
    ...args,
    loginEvent: action('loginEvent'),
    logoutEvent: action('logoutEvent'),
  },
});

export const Secured = (args: any) => ({
  props: {
    ...args,
    secured: true,
    loginEvent: action('loginEvent'),
    logoutEvent: action('logoutEvent'),
  },
});

export const LogoutEnabled = (args: any) => ({
  props: {
    ...args,
    isLogout: true,
    loginEvent: action('loginEvent'),
    logoutEvent: action('logoutEvent'),
  },
});
