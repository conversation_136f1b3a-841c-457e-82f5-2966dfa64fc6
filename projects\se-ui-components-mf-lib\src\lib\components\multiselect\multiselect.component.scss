@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';

::ng-deep {
  p-multiselect {
    .p-multiselect {
      width: 100%;
    }

    .p-multiselect-token {
      background-color: var(--color-blue-500) !important;
      margin-right: 0.5rem;

      .p-multiselect-token-label {
        color: var(--color-white);
      }

      .p-multiselect-token-icon {
        color: var(--color-white);
      }
    }

    &.token-error .p-multiselect-token {
      background-color: var(--color-red-400) !important;
    }

    .closeIcon {
      margin-top: 0.4rem
    }

    .p-multiselect-clear-icon,
    .p-multiselect-trigger-icon {
      color: var(--color-blue-500);
      display: flex;
      top: 0;
      bottom: 0;
      align-self: center;
      align-items: center;
    }

    .dropdown-icon {
      --ng-icon__size: 1.5rem !important;
      display: inline-flex;
      justify-content: center;
    }

    .clear-icon {
      --ng-icon__size: 1.2rem !important;
      display: inline-flex;
      justify-content: center;
    }
  }

  .p-multiselect-items {
    padding-left: 0;
    margin-bottom: 0;

    .p-checkbox {
      margin-right: 8px;
    }
  }

  .p-multiselect-filter-container {
    margin-left: 8px;

    .p-multiselect-filter-icon {
      height: 100%;
      align-self: anchor-center;
    }
  }

  .multiselect-input {
    width: 100%;
    flex: 1;

    se-input>.se-input {
      margin-bottom: 0;
    }
  }

}

@include media-breakpoint-down(md) {
.scrollable-label-wrapper {
  max-width: 100%;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  white-space: nowrap;
}

::ng-deep #multiselect > p-overlay > div {
  max-width: 100% !important;
}
}

