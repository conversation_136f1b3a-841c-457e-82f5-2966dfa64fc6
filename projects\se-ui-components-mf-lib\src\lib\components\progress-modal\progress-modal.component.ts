import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { SeButton } from '../button';

@Component({
  selector: 'se-progress-modal',
  template: `
    <div class="se-progress-modal">
      <div class="se-progress-modal__spinner-container">
        <div class="lds-ring">
          <div class="ring-segment"></div>
          <div class="ring-segment"></div>
          <div class="ring-segment"></div>
          <div class="ring-segment"></div>
        </div>
      </div>
      <div class="se-progress-modal__text-container">
        <p class="message text-md">{{ message | translate }}</p>
        <p class="message text-md">{{ subtitle | translate }}</p>
        <div
          *ngIf="progressValue$ | async as value"
          [ngClass]="{ 'mb-4': customButton }"
        >
          <p-progressBar [value]="value" />
        </div>
        <ng-container *ngIf="customButton">
          <se-button
            [size]="customButton.size || 'small'"
            [disabled]="customButton.disabled || false"
            [btnTheme]="customButton.btnTheme || 'secondary'"
            [icon]="customButton.icon"
            [iconSize]="customButton.iconSize"
            [tooltipText]="customButton.tooltipText"
            [tooltipPosition]="customButton.tooltipPosition"
            [iconPosition]="customButton.iconPosition"
            [type]="customButton.type || 'button'"
            (onClick)="onCustomButtonClick($event)"
          >
            {{ customButton.label }}
          </se-button>
        </ng-container>
      </div>
    </div>
  `,
  styleUrls: ['./progress-modal.component.scss'],
})
export class ProgressModalComponent implements OnInit {
  private id: any = null;
  private _interval: number = 2000;

  @Input() progressValue$: Subject<number> | undefined;
  @Input() message: string = '';
  @Input() subtitle: string = '';
  @Input() customButton: SeButton | null = null;
  @Input() set interval(value: number) {
    //seconds
    if (value > 0) {
      this._interval = value * 1000; //miliseconds
      this.start();
    }
  }

  @Output() intervalOutput: EventEmitter<void> = new EventEmitter<void>();
  @Output() onCustomButton: EventEmitter<MouseEvent> =
    new EventEmitter<MouseEvent>();

  ngOnInit(): void {
    this.start();
  }

  ngOnDestroy(): void {
    this.stop();
  }

  private start = (): void => {
    //REMOVE PREVIOUS INTERVAL
    this.stop();

    this.id = setInterval(() => {
      this.intervalOutput.emit();
    }, this._interval);
  };

  private stop = (): void => {
    if (this.id) {
      clearInterval(this.id);
      this.id = null;
    }
  };

  onCustomButtonClick(event: MouseEvent): void {
    this.onCustomButton.emit(event);
  }
}
