import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { SeDocumentsService } from '../../services';
import { SeSharedModule } from '../../shared/shared.module';
import { SeAlertModule } from '../alert/alert.module';
import { SeButtonModule } from '../button/button.module';
import { SeModalModule } from '../modal/modal.module';
import { SeTableModule } from './../table/table.module';
import { UploadFilesComponent } from './upload-files.component';
import { SePanelModule } from '../panel/panel.module';
import { SeLinkModule } from '../link/link.module';

@NgModule({
  declarations: [UploadFilesComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    SeSharedModule,
    FormsModule,
    TranslateModule.forChild(),
    SeButtonModule,
    SeAlertModule,
    SeTableModule,
    SeModalModule,
    SePanelModule,
    SeLinkModule,
  ],
  exports: [UploadFilesComponent],
  providers: [SeDocumentsService],
})
export class SeUploadFilesModule {}
