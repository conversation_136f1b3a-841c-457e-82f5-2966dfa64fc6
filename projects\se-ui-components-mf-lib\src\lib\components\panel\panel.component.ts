import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Panel } from 'primeng/panel';

import { AriaLabelInput } from '../../directives/aria-label/aria-label.model';
import { CustomIdInput } from '../../directives';
import { SeButton } from '../button';
import { AllowPanelTranslations } from './panel.model';

@Component({
  styleUrls: ['./panel.component.scss'],
  selector: 'se-panel',
  template: `
    <p-panel
      #pPanel
      [ariaLabel]="toggleableButtonAriaLabel"
      [customId]="toggleableButtonId"
      [styleClass]="'se-panel ' + panelTheme + (empty ? ' empty' : '')"
      [toggleable]="colapsible"
      [collapsed]="collapsed"
      (collapsedChange)="onCollapsedChange($event)"
    >
      <ng-template pTemplate="header">
        <div
          class="se-panel__header"
          [ngClass]="{ 'is-colapsible': colapsible }"
        >
          <div class="se-panel__header-title">
            <div>
              <span
                #pHeader
                [innerHTML]="title | translate | safeHtml"
                [attr.translate]="
                  allowTranslations.allowTitleTranslation === false
                    ? 'no'
                    : null
                "
              ></span>
              <ng-icon
                class="tooltip-icon"
                *ngIf="tooltip"
                name="matInfo"
                [pTooltipAccessible]="tooltipText"
              ></ng-icon>
            </div>
            <span
              class="se-panel__header-subtitle text-sm"
              *ngIf="subtitle"
              [innerHTML]="subtitle | translate | safeHtml"
              [attr.translate]="
                allowTranslations.allowSubtitleTranslation === false
                  ? 'no'
                  : null
              "
            ></span>
          </div>
          <div
            *ngIf="(actionButton || customActions)"
            class="se-panel-header-buttons"
            [ngClass]="{'panel-toggler-separation' : colapsible}"
          >
            <se-button
              *ngIf="actionButton"
              class="button-align"
              [btnTheme]="'secondary'"
              [tooltipText]="actionButton.tooltipText"
              [tooltipPosition]="actionButton.tooltipPosition"
              [icon]="actionButton.icon || ''"
              [iconSize]="actionButton.iconSize || ''"
              [iconPosition]="actionButton.iconPosition || 'left'"
              [disabled]="actionButton.disabled || false"
              (onClick)="onButtonClick()"
              [size]="'small'"
              >{{ actionButton.label || '' | translate }}
            </se-button>
            <!-- CUSTOM ACTIONS -->
            <ng-container *ngTemplateOutlet="customActions"></ng-container>
          </div>
        </div>
      </ng-template>
      <ng-template let-isOpen pTemplate="headericons">
        <ng-icon
          [ngClass]="{
            'rotate-90': !isOpen,
            'rotate-270': isOpen
          }"
          name="matArrowBackIosNewOutline"
        >
        </ng-icon>
      </ng-template>
      <div #contenidoColapsable>
        <!-- NG-CONTENT -->
        <ng-content></ng-content>
      </div>
    </p-panel>
  `,
})
export class PanelComponent implements AfterViewInit {
  @ViewChild('pPanel') panel!: Panel;
  @ViewChild('contenidoColapsable')
  contenidoColapsable!: ElementRef<HTMLSpanElement>;
  @ViewChild('pHeader') spanHeader!: ElementRef<HTMLSpanElement>;

  @Input() id!: string;
  @Input() title!: string;
  @Input() subtitle: string | undefined;
  @Input() panelTheme: 'default' | 'secondary' | 'primary' = 'default';
  @Input() colapsible: boolean = false;
  private _collapsed: boolean = false;

  @Input()
  get collapsed(): boolean {
    return this._collapsed;
  }
  set collapsed(value: boolean) {
    this._collapsed = value;
    this.addInertAttribute(value);
  }
  @Input() actionButton: SeButton | undefined;
  @Input() customActions!: TemplateRef<any>;
  @Input() tooltip = false;
  @Input() tooltipText: string | TemplateRef<HTMLElement> | undefined;
  @Input() empty: boolean = false;
  @Input() allowTranslations: AllowPanelTranslations = {
    allowTitleTranslation: true,
    allowSubtitleTranslation: true,
  };

  @Output() actionButtonClick = new EventEmitter<void>();
  @Output() collapsedChange = new EventEmitter<boolean>();

  // aria-label
  toggleableButtonAriaLabel: AriaLabelInput = {
    querySelector:
      '.p-panel-header .p-panel-icons button.p-panel-header-icon.p-panel-toggler',
    translationKey: 'UI_COMPONENTS.CARD_BUTTONS.COLLAPSIBLE',
  };

  toggleableButtonId: CustomIdInput = {
    querySelector: '.p-panel-header .p-panel-icons button',
    id: 'collapsible-header-button',
  };

  ngAfterViewInit(): void {
    if (this.panel && this.id) {
      if (this.panel) {
        // Sets a custom id, instead the automatic one, to solve primeng accessible issues
        Object.defineProperty(this.panel, 'id', { value: this.id });
      }
    }

    if (this.spanHeader && this.id) {
      // Sets an ID for the template header element to resolve the issue with aria-labelledby.
      this.spanHeader.nativeElement.id = `${this.id}_header`;
    }

    if (this.id) {
      this.toggleableButtonId.id = this.id + '_header-button';
    }
    if (this.collapsed && this.contenidoColapsable) {
      this.addInertAttribute(this.collapsed);
    }
  }

  onButtonClick() {
    this.actionButtonClick.emit();
  }

  onCollapsedChange(value: boolean) {
    this.collapsedChange.emit(value);
    this.addInertAttribute(value);
  }

  addInertAttribute(value: boolean) {
    if (value) {
      this.contenidoColapsable?.nativeElement.setAttribute('inert', '');
    } else {
      this.contenidoColapsable?.nativeElement.removeAttribute('inert');
    }
  }
}
