:host {
  &.children-row {
    background-color: var(--color-blue-100);
  }
}

.se-table__cell {
  box-sizing: border-box;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-gray-300);
  text-align: left;
  line-height: var(--line-sm);
  vertical-align: middle;
  font-size: var(--text-sm);

  &:first-child {
    padding-left: 24px;
  }

  &:last-child {
    padding-right: 24px;
    border-right: 0;
  }

  &__mobile {
    &__header {
      display: none;
    }
  }
}

// RESPONSIVE
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

// @media only screen and (max-width: 768px) {
@include media-breakpoint-down(md) {
  tr[default-row] {
    display: flex;
    flex-direction: column;
    font-size: 16px;
  }
  .se-table__cell {
    font-size: 16px;
    border-bottom: none;

    &:first-child {
      padding-left: 16px;
    }

    &__mobile {
      display: flex;
      flex-direction: column;
      gap: 8px;
      text-align: left;

      &__header {
        display: inline;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }
}
