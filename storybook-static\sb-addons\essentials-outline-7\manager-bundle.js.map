{"version": 3, "sources": ["../../../node_modules/@storybook/addon-outline/dist/chunk-2DMOCDBJ.mjs", "global-externals:react", "global-externals:@storybook/manager-api", "global-externals:@storybook/components", "../../../node_modules/@storybook/addon-outline/dist/manager.mjs"], "sourcesContent": ["var ADDON_ID=\"storybook/outline\",PARAM_KEY=\"outline\";\n\nexport { ADDON_ID, PARAM_KEY };\n", "export default __REACT__;\nconst { Children, Component, Fragment, Profiler, PureComponent, StrictMode, Suspense, __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED, cloneElement, createContext, createElement, createFactory, createRef, forwardRef, isValidElement, lazy, memo, useCallback, useContext, useDebugValue, useEffect, useImperativeHandle, useLayoutEffect, useMemo, useReducer, useRef, useState, version } = __REACT__;\nexport { Children, Component, Fragment, Profiler, PureComponent, StrictMode, Suspense, __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED, cloneElement, createContext, createElement, createFactory, createRef, forwardRef, isValidElement, lazy, memo, useCallback, useContext, useDebugValue, useEffect, useImperativeHandle, useLayoutEffect, useMemo, useReducer, useRef, useState, version };", "export default __STORYBOOKAPI__;\nconst { ActiveTabs, Consumer, ManagerContext, Provider, addons, combineParameters, controlOrMetaKey, controlOrMetaSymbol, eventMatchesShortcut, eventToShortcut, isMacLike, isShortcutTaken, keyToSymbol, merge, mockChannel, optionOrAltSymbol, shortcutMatchesShortcut, shortcutToHumanString, types, useAddonState, useArgTypes, useArgs, useChannel, useGlobalTypes, useGlobals, useParameter, useSharedState, useStoryPrepared, useStorybookApi, useStorybookState } = __STORYBOOKAPI__;\nexport { ActiveTabs, Consumer, ManagerContext, Provider, addons, combineParameters, controlOrMetaKey, controlOrMetaSymbol, eventMatchesShortcut, eventToShortcut, isMacLike, isShortcutTaken, keyToSymbol, merge, mockChannel, optionOrAltSymbol, shortcutMatchesShortcut, shortcutToHumanString, types, useAddonState, useArgTypes, useArgs, useChannel, useGlobalTypes, useGlobals, useParameter, useSharedState, useStoryPrepared, useStorybookApi, useStorybookState };", "export default __STORYBOOKCOMPONENTS__;\nconst { A, ActionBar, AddonPanel, Badge, Bar, Blockquote, Button, Code, DL, Div, DocumentWrapper, ErrorFormatter, FlexBar, Form, H1, H2, H3, H4, H5, H6, HR, IconButton, IconButtonSkeleton, Icons, Img, LI, Link, ListItem, Loader, OL, P, Placeholder, Pre, ResetWrapper, ScrollArea, Separator, Spaced, Span, StorybookIcon, StorybookLogo, Symbols, SyntaxHighlighter, TT, TabBar, TabButton, TabWrapper, Table, Tabs, TabsState, TooltipLinkList, TooltipMessage, TooltipNote, UL, WithTooltip, WithTooltipPure, Zoom, codeCommon, components, createCopyToClipboardFunction, getStoryHref, icons, interleaveSeparators, nameSpaceClassNames, resetComponents, withReset } = __STORYBOOKCOMPONENTS__;\nexport { A, ActionBar, AddonPanel, Badge, Bar, Blockquote, Button, Code, DL, Div, DocumentWrapper, ErrorFormatter, FlexBar, Form, H1, H2, H3, H4, H5, H6, HR, IconButton, IconButtonSkeleton, Icons, Img, LI, Link, ListItem, Loader, OL, P, Placeholder, Pre, ResetWrapper, ScrollArea, Separator, Spaced, Span, StorybookIcon, StorybookLogo, Symbols, SyntaxHighlighter, TT, TabBar, TabButton, TabWrapper, Table, Tabs, TabsState, TooltipLinkList, TooltipMessage, TooltipNote, UL, WithTooltip, WithTooltipPure, Zoom, codeCommon, components, createCopyToClipboardFunction, getStoryHref, icons, interleaveSeparators, nameSpaceClassNames, resetComponents, withReset };", "import { PARAM_KEY, ADDON_ID } from './chunk-2DMOCDBJ.mjs';\nimport React, { memo, useCallback, useEffect } from 'react';\nimport { useGlobals, useStorybookApi, addons, types } from '@storybook/manager-api';\nimport { IconButton, Icons } from '@storybook/components';\n\nvar OutlineSelector=memo(function(){let[globals,updateGlobals]=useGlobals(),api=useStorybookApi(),isActive=[!0,\"true\"].includes(globals[PARAM_KEY]),toggleOutline=useCallback(()=>updateGlobals({[PARAM_KEY]:!isActive}),[isActive]);return useEffect(()=>{api.setAddonShortcut(ADDON_ID,{label:\"Toggle Outline [O]\",defaultShortcut:[\"O\"],actionName:\"outline\",showInMenu:!1,action:toggleOutline});},[toggleOutline,api]),React.createElement(IconButton,{key:\"outline\",active:isActive,title:\"Apply outlines to the preview\",onClick:toggleOutline},React.createElement(Icons,{icon:\"outline\"}))});addons.register(ADDON_ID,()=>{addons.add(ADDON_ID,{title:\"Outline\",id:\"outline\",type:types.TOOL,match:({viewMode})=>!!(viewMode&&viewMode.match(/^(story|docs)$/)),render:()=>React.createElement(OutlineSelector,null)});});\n"], "mappings": ";AAAA,IAAIA,EAAS,oBAAoBC,EAAU,UCA3C,IAAOC,EAAQ,UACT,CAAE,SAAAC,EAAU,UAAAC,EAAW,SAAAC,EAAU,SAAAC,EAAU,cAAAC,EAAe,WAAAC,EAAY,SAAAC,EAAU,mDAAAC,EAAoD,aAAAC,EAAc,cAAAC,EAAe,cAAAC,EAAe,cAAAC,EAAe,UAAAC,EAAW,WAAAC,EAAY,eAAAC,EAAgB,KAAAC,EAAM,KAAAC,EAAM,YAAAC,EAAa,WAAAC,EAAY,cAAAC,EAAe,UAAAC,EAAW,oBAAAC,EAAqB,gBAAAC,EAAiB,QAAAC,EAAS,WAAAC,EAAY,OAAAC,EAAQ,SAAAC,GAAU,QAAAC,EAAQ,EAAI,UCDpY,IAAOC,GAAQ,iBACT,CAAE,WAAAC,GAAY,SAAAC,GAAU,eAAAC,GAAgB,SAAAC,GAAU,OAAAC,EAAQ,kBAAAC,GAAmB,iBAAAC,GAAkB,oBAAAC,GAAqB,qBAAAC,GAAsB,gBAAAC,GAAiB,UAAAC,GAAW,gBAAAC,GAAiB,YAAAC,GAAa,MAAAC,GAAO,YAAAC,GAAa,kBAAAC,GAAmB,wBAAAC,GAAyB,sBAAAC,GAAuB,MAAAC,EAAO,cAAAC,GAAe,YAAAC,GAAa,QAAAC,GAAS,WAAAC,GAAY,eAAAC,GAAgB,WAAAC,EAAY,aAAAC,GAAc,eAAAC,GAAgB,iBAAAC,GAAkB,gBAAAC,EAAiB,kBAAAC,EAAkB,EAAI,iBCD5c,IAAOC,GAAQ,wBACT,CAAE,EAAAC,GAAG,UAAAC,GAAW,WAAAC,GAAY,MAAAC,GAAO,IAAAC,GAAK,WAAAC,GAAY,OAAAC,GAAQ,KAAAC,GAAM,GAAAC,GAAI,IAAAC,GAAK,gBAAAC,GAAiB,eAAAC,GAAgB,QAAAC,GAAS,KAAAC,GAAM,GAAAC,GAAI,GAAAC,GAAI,GAAAC,GAAI,GAAAC,GAAI,GAAAC,GAAI,GAAAC,GAAI,GAAAC,GAAI,WAAAC,EAAY,mBAAAC,GAAoB,MAAAC,EAAO,IAAAC,GAAK,GAAAC,GAAI,KAAAC,GAAM,SAAAC,GAAU,OAAAC,GAAQ,GAAAC,GAAI,EAAAC,GAAG,YAAAC,GAAa,IAAAC,GAAK,aAAAC,GAAc,WAAAC,GAAY,UAAAC,GAAW,OAAAC,GAAQ,KAAAC,GAAM,cAAAC,GAAe,cAAAC,GAAe,QAAAC,GAAS,kBAAAC,GAAmB,GAAAC,GAAI,OAAAC,GAAQ,UAAAC,GAAW,WAAAC,GAAY,MAAAC,GAAO,KAAAC,GAAM,UAAAC,GAAW,gBAAAC,GAAiB,eAAAC,GAAgB,YAAAC,GAAa,GAAAC,GAAI,YAAAC,GAAa,gBAAAC,GAAiB,KAAAC,GAAM,WAAAC,GAAY,WAAAC,GAAY,8BAAAC,GAA+B,aAAAC,GAAc,MAAAC,GAAO,qBAAAC,GAAsB,oBAAAC,GAAqB,gBAAAC,GAAiB,UAAAC,EAAU,EAAI,wBCIlpB,IAAIC,EAAgBC,EAAK,UAAU,CAAC,GAAG,CAACC,EAAQC,CAAa,EAAEC,EAAW,EAAEC,EAAIC,EAAgB,EAAEC,EAAS,CAAC,GAAG,MAAM,EAAE,SAASL,EAAQM,CAAS,CAAC,EAAEC,EAAcC,EAAY,IAAIP,EAAc,CAAC,CAACK,CAAS,EAAE,CAACD,CAAQ,CAAC,EAAE,CAACA,CAAQ,CAAC,EAAE,OAAOI,EAAU,IAAI,CAACN,EAAI,iBAAiBO,EAAS,CAAC,MAAM,qBAAqB,gBAAgB,CAAC,GAAG,EAAE,WAAW,UAAU,WAAW,GAAG,OAAOH,CAAa,CAAC,CAAE,EAAE,CAACA,EAAcJ,CAAG,CAAC,EAAEQ,EAAM,cAAcC,EAAW,CAAC,IAAI,UAAU,OAAOP,EAAS,MAAM,gCAAgC,QAAQE,CAAa,EAAEI,EAAM,cAAcE,EAAM,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,EAAEC,EAAO,SAASJ,EAAS,IAAI,CAACI,EAAO,IAAIJ,EAAS,CAAC,MAAM,UAAU,GAAG,UAAU,KAAKK,EAAM,KAAK,MAAM,CAAC,CAAC,SAAAC,CAAQ,IAAI,CAAC,EAAEA,GAAUA,EAAS,MAAM,gBAAgB,GAAG,OAAO,IAAIL,EAAM,cAAcb,EAAgB,IAAI,CAAC,CAAC,CAAE,CAAC", "names": ["ADDON_ID", "PARAM_KEY", "react_default", "Children", "Component", "Fragment", "Profiler", "PureComponent", "StrictMode", "Suspense", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "cloneElement", "createContext", "createElement", "createFactory", "createRef", "forwardRef", "isValidElement", "lazy", "memo", "useCallback", "useContext", "useDebugValue", "useEffect", "useImperativeHandle", "useLayoutEffect", "useMemo", "useReducer", "useRef", "useState", "version", "manager_api_default", "ActiveTabs", "Consumer", "ManagerContext", "Provider", "addons", "combineParameters", "controlOrMetaKey", "controlOrMetaSymbol", "eventMatchesShortcut", "eventToShortcut", "isMacLike", "isShortcutTaken", "keyToSymbol", "merge", "mockChannel", "optionOrAltSymbol", "shortcutMatchesShortcut", "shortcutToHumanString", "types", "useAddonState", "useArgTypes", "useArgs", "useChannel", "useGlobalTypes", "useGlobals", "useParameter", "useSharedState", "useStoryPrepared", "useStorybookApi", "useStorybookState", "components_default", "A", "ActionBar", "AddonPanel", "Badge", "Bar", "Blockquote", "<PERSON><PERSON>", "Code", "DL", "Div", "DocumentWrapper", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FlexBar", "Form", "H1", "H2", "H3", "H4", "H5", "H6", "HR", "IconButton", "IconButtonSkeleton", "Icons", "Img", "LI", "Link", "ListItem", "Loader", "OL", "P", "Placeholder", "Pre", "ResetWrapper", "ScrollArea", "Separator", "Spaced", "Span", "StorybookIcon", "StorybookLogo", "Symbols", "Syntax<PERSON><PERSON><PERSON><PERSON>", "TT", "TabBar", "TabButton", "TabWrapper", "Table", "Tabs", "TabsState", "TooltipLinkList", "TooltipMessage", "TooltipNote", "UL", "WithTooltip", "WithTooltipPure", "Zoom", "codeCommon", "components", "createCopyToClipboardFunction", "getStoryHref", "icons", "interleaveSeparators", "nameSpaceClassNames", "resetComponents", "with<PERSON><PERSON><PERSON>", "OutlineSelector", "memo", "globals", "updateGlobals", "useGlobals", "api", "useStorybookApi", "isActive", "PARAM_KEY", "toggleOutline", "useCallback", "useEffect", "ADDON_ID", "react_default", "IconButton", "Icons", "addons", "types", "viewMode"]}