import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { PaginatorModule } from 'primeng/paginator';
import { SeSharedModule } from '../../shared/shared.module';
import { SeButtonModule } from '../button';
import { SeDropdownModule } from '../dropdown/dropdown.module';
import { PaginationComponent } from './pagination.component';

@NgModule({
  declarations: [PaginationComponent],
  imports: [
    CommonModule,
    SeSharedModule,
    PaginatorModule,
    TranslateModule.forChild(),
    SeDropdownModule,
    SeButtonModule,
    ReactiveFormsModule,
  ],
  exports: [PaginationComponent],
})
export class SePaginationModule {}
