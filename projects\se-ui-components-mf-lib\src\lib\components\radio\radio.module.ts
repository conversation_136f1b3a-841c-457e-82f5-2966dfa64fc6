import { NgModule } from '@angular/core';
import { RadioComponent } from './radio.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SeFormControlErrorModule } from '../form-control-error/form-control-error.module';
import { SeSharedModule } from '../../shared/shared.module';
import { SeHighlightRadioContainerModule } from '../../directives';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  declarations: [RadioComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    TranslateModule.forChild(), 
    SeFormControlErrorModule,
    SeSharedModule,
    SeHighlightRadioContainerModule,
  ],
  exports: [RadioComponent],
})
export class SeRadioModule {}
