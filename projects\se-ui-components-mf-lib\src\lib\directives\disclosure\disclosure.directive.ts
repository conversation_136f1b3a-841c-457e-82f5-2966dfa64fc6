import { animate, AnimationBuilder, style } from '@angular/animations';
import {
  Directive,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  type AfterViewInit,
} from '@angular/core';

import type { Nullable } from '../../models';

/**
 * Directiva para contraer/expandir cualquier elemento de la pantalla. Incluye
 * una pequeña animación que tendrá en cuenta las preferencias de accesibilidad
 * establecidas de la configuración del sistema operativo del usuario en caso
 * de que tenga desactivadas las animaciones.
 *
 * @example
 *
 * ```html
 * <p
 *   seDisclosure
 *   seDisclosureTargetId="elemento-expandible"
 *   (seDisclosureChange)="onDisclosureChange($event)"
 * >
 *   Pulsar para contraer/expandir "#elemento-expandible"
 * </p>
 * <div id="elemento-expandible">
 *   <!--
 *     Este elemento se expandirá/contraerá cuando se pulse sobre el
 *     párrafo anterior.
 *   -->
 * </div>
 * ```
 */
@Directive({
  selector: '[seDisclosure]',
  host: {
    '[attr.aria-controls]':
      'seDisclosureTargetId || null',
    '[attr.aria-expanded]': 'seDisclosureTargetId ? expanded : null',
    '(click)': 'seDisclosureTargetId ? onClick() : null',
    '(keyup.Enter)': 'seDisclosureTargetId ? onKeyupEnter() : null',
    '(keyup.Space)': 'seDisclosureTargetId ? onKeyupSpace() : null',
  },
})
export class SeDisclosureDirective implements AfterViewInit {
  /**
   * Atributo obligatorio para que la directiva funcione como se espera. Hay
   * que pasarle el atributo "id" del elemento que se contraerá/expandirá al
   * pulsar sobre el elemento en el que se aplica la directiva.
   *
   * @example
   *
   * ```html
   * <p
   *   seDisclosure
   *   seDisclosureTargetId="collapsible-content">
   *   Pulsar para contraer/expandir "#collapsible-content"
   * </p>
   * <div id="collapsible-content">
   *   <!--
   *     Este elemento se expandirá/contraerá cuando se pulse sobre el
   *     párrafo anterior.
   *   -->
   * </div>
   * ```
   */
  @Input({ required: true }) set seDisclosureTargetId(
    targetId: Nullable<string>
  ) {
    if (this.seDisclosureTargetId === targetId) return;

    this.targetElementId = targetId;

    if (!targetId) {
      this.restoreDefaults();
      return;
    }

    this.initializeDisclosure();
  }

  get seDisclosureTargetId() {
    return this.targetElementId;
  }

  /**
   * Emite cuando el estado contraído/expandido cambia.
   *
   * @example
   *
   * ```html
   * <p>¿El elemento está expandido? {{ expanded }}</p>
   *
   * <p
   *   seDisclosure
   *   seDisclosureTargetId="collapsible-content"
   *   (seDisclosureChange)="
   *     log(
   *       'El elemento está:',
   *       $event.expanded ? 'expandido' : 'contraído'
   *     )
   *   "
   * >
   *   Pulsar para contraer/expandir "#collapsible-content"
   * </p>
   * <div id="collapsible-content">
   *   <!--
   *     Este elemento se expandirá/contraerá cuando se pulse sobre el
   *     párrafo anterior.
   *   -->
   * </div>
   * ```
   */
  @Output() seDisclosureChange = new EventEmitter<{ expanded: boolean }>();

  private targetElement: Nullable<HTMLElement>;
  private targetElementId: Nullable<string>;
  private expanded = true;
  private hostNativeElement: HTMLElement;
  private ownerDocument: Document;
  private defaultRole: string | null;
  private defaultTabindex: string | null;
  private collapseAnimationFactory = this.animationBuilder.build([
    style({ display: 'block', transform: 'scaleY(100%)' }),
    animate(
      '0.45s ease',
      style({ transform: 'scaleY(0)', transformOrigin: 'top', display: 'none' })
    ),
  ]);
  private expandAnimationFactory = this.animationBuilder.build([
    style({ display: 'block', transform: 'scaleY(0)' }),
    animate(
      '0.45s ease',
      style({
        transform: 'scaleY(100%)',
        transformOrigin: 'top',
        display: 'block',
      })
    ),
  ]);

  constructor(
    private elementRef: ElementRef<HTMLElement>,
    private animationBuilder: AnimationBuilder
  ) {
    this.hostNativeElement = this.elementRef.nativeElement;
    this.ownerDocument = this.hostNativeElement.ownerDocument;
    this.defaultRole = this.hostNativeElement.getAttribute('role');
    this.defaultTabindex = this.hostNativeElement.getAttribute('tabindex');
  }

  ngAfterViewInit(): void {
    this.targetElement = this.seDisclosureTargetId
      ? this.ownerDocument.getElementById(this.seDisclosureTargetId)
      : null;
  }

  private restoreDefaults(): void {
    this.defaultRole
      ? this.hostNativeElement.setAttribute('role', this.defaultRole)
      : this.hostNativeElement.removeAttribute('role');

    this.defaultTabindex
      ? this.hostNativeElement.setAttribute('tabindex', this.defaultTabindex)
      : this.hostNativeElement.removeAttribute('tabindex');
  }

  private initializeDisclosure(): void {
    if (!this.hasButtonRole()) {
      /* El atributo "role"="button" le indica a los dispositivos de tecnología
        asistiva (como lectores de pantalla) que el elemento es un botton
        clicable que dispara una acción cuando es activado por el usuario. En
        este caso es recomendable añadirle este rol ya que el elemento
        dispara la acción de expandir o contraer otro elemento de la
        pantalla. */
      this.hostNativeElement.setAttribute('role', 'button');
    }

    if (!this.isFocusable()) {
      /* Permite que el elemento host de la directiva tenga el foco para admitir
        eventos de teclado. */
      this.hostNativeElement.setAttribute('tabindex', '0');
    }
  }

  private hasButtonRole(): boolean {
    return (
      this.hostNativeElement.getAttribute('role') === 'button' ||
      this.isButtonLike()
    );
  }

  private isFocusable(): boolean {
    // Comprueba si el elemento tiene un tabindex válido
    const tabindex = this.hostNativeElement.getAttribute('tabindex');
    if (tabindex && Number(tabindex) >= 0) return true;

    // Comprueba si el elemento se ha establecido como editable
    if (this.hostNativeElement.hasAttribute('contenteditable')) return true;

    // Comprueba si es un enlace navegable (i.e. con "href")
    if (this.isNavigableLink()) return true;

    // Comprueba elementos específicos que pueden recibir el foco
    const focusableTags = ['BUTTON', 'INPUT', 'SELECT', 'TEXTAREA'];
    return focusableTags.includes(this.hostNativeElement.tagName.toUpperCase());
  }

  protected onClick(): void {
    this.toggleExpanded();
  }

  protected onKeyupEnter(): void {
    const isButtonLike = this.isButtonLike();
    const isNavigableLink = this.isNavigableLink();

    /* Cuando se emite el evento "keyup" de la tecla "Enter" sobre uno de estos
      elementos...

        - button, input[type=submit], input[type=reset] (isButtonLike)
        - Enlaces <a> con atributo "href" definido (isNavigableLink)

      ...se emite automáticamente el evento "click" además del "keyup", por lo
      que se ejecutará la función "protected onClick(): void;". Puesto que no
      queremos que se llame dos veces a la función "toggleExpanded()", si el
      elemento es como un botón (button, input[type=submit | reset]) o es un
      enlace navegable (tiene atributo "href") entonces no ejecutaremos la
      función "toggleExpanded()" en este callback. */
    if (isButtonLike || isNavigableLink) return;
    this.toggleExpanded();
  }

  protected onKeyupSpace(): void {
    /* Cuando se emite el evento "keyup" de la tecla "Space" sobre uno de estos
      elementos...

        - button, input[type=submit], input[type=reset] (isButtonLike)

      ...se emite automáticamente el evento "click" además del "keyup", por lo
      que se ejecutará la función "protected onClick(): void;". Puesto que no
      queremos que se llame dos veces a la función "toggleExpanded()", si el
      elemento es como un botón (button, input[type=submit | reset]) entonces
      no ejecutaremos la función "toggleExpanded()" en este callback. */
    if (this.isButtonLike()) return;
    this.toggleExpanded();
  }

  private isButtonLike(): boolean {
    const tagName = this.hostNativeElement.tagName.toUpperCase();
    const type = this.hostNativeElement.getAttribute('type') ?? '';

    return (
      tagName === 'BUTTON' ||
      (tagName === 'INPUT' && (type === 'submit' || type === 'reset'))
    );
  }

  private isNavigableLink(): boolean {
    return (
      this.hostNativeElement.tagName.toUpperCase() === 'A' &&
      this.hostNativeElement.hasAttribute('href')
    );
  }

  private toggleExpanded(): void {
    this.expanded = !this.expanded;
    this.expanded ? this.expand() : this.collapse();
    this.seDisclosureChange.emit({ expanded: this.expanded });
  }

  private expand(): void {
    if (!this.targetElement) return;

    this.targetElement.removeAttribute('inert');

    if (this.areAnimationsDisabled) {
      /* Si se deja vacía, la propiedad se restablece al valor que tenía antes
      de aplicar esta directiva. */
      this.targetElement.style.display = '';
      return;
    }

    this.expandAnimationFactory.create(this.targetElement).play();
  }

  private collapse(): void {
    if (!this.targetElement) return;

    this.targetElement.setAttribute('inert', '');

    if (this.areAnimationsDisabled) {
      this.targetElement.style.display = 'none';
      return;
    }

    this.collapseAnimationFactory.create(this.targetElement).play();
  }

  private get areAnimationsDisabled(): boolean {
    return window?.matchMedia(`(prefers-reduced-motion: reduce)`).matches;
  }
}
