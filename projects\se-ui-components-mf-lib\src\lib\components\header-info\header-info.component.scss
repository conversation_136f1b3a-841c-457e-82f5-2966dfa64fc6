@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

:host {
  display: block;
  background-color: #f7f7f7;

  .container:after {
    content: none !important;
  }
}

.se-header-info {
  display: flex;
  flex-direction: column;
  padding: 1rem;

  &__main-container {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  &__title-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
  }

  &__title {
    font-weight: var(--font-regular);
    font-size: var(--text-lg);
    line-height: var(--line-md);
    margin: 0;
    padding: 0;
  }

  &__info-items-container {
    display: flex;
    flex-direction: column;
  }

  &__info-items-list {
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
  }

  &__info-item-term,
  &__info-item-details {
    margin: 0;
    flex-shrink: 0;
    font-size: var(--text-base);
    line-height: 1.375em;
  }

  &__info-item-link {
    display: flex;
    align-items: center;
  }

  &__info-item-term {
    font-weight: 600;
    margin-top: 1rem;
  }

  &__info-item-details {
    margin-top: 0.25rem;
  }

  &__tags-container {
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  &__actions-container {
    display: flex;
    gap: 1rem;
  }
}

:host #se-header-info__help-button ::ng-deep .se-button {
  width: 100%;
}

@include media-breakpoint-up(lg) {
  .se-header-info {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    &__main-container {
      gap: 0.5rem;
    }

    &__title {
      font-size: var(--text-md);
    }

    &__info-items-container {
      gap: 0.5rem;
    }

    &__info-items-list {
      display: inline-flex;
      flex-direction: row;
      gap: 0.5rem;
    }

    &__info-item-term,
    &__info-item-details {
      margin: 0;
      font-size: var(--text-2xs);
      line-height: var(--line-xs);
    }

    &__info-item-term:not(:first-child) {
      padding-left: 0.5rem;
      border-left: 1px solid var(--color-gray-400);
    }

    &__tags-container {
      margin: 0;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    &__tags-container:not(:first-child) {
      margin-left: 0.5rem;
    }
  }
}
