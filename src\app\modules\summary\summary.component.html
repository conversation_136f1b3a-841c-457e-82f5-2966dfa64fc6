<div class="d-flex flex-column gap-4">
  <se-panel
    [id]="'summary_panel_id'"
    [title]="'SE_DECINF_MF.MODULE_SUMMARY.TITLE' | translate"
    [colapsible]="false"
    [collapsed]="false"
    [panelTheme]="'default'"
  >
    <ng-container *ngFor="let data of summaryDataList">
      <div class="row">
        <p class="col-12 col-md-4 fw-bold">{{ data.label | translate }}</p>
        <p class="col-12 col-md-8">{{ data.value }}</p>
      </div>
    </ng-container>
  </se-panel>

  <!--  BUTTONS -->
  <section class="d-flex justify-content-between flex-row">
    <se-button (onClick)="goBack()" [btnTheme]="'secondary'">
      {{ 'UI_COMPONENTS.BUTTONS.PREVIOUS' | translate }}
    </se-button>
    <se-button (onClick)="onSubmit()">
      {{ 'SE_DECINF_MF.MODULE_SUMMARY.SUBMIT' | translate }}
    </se-button>
  </section>
</div>
