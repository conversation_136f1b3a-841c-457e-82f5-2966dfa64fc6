.se-table-component {
  .se-table {
    display: table;
    width: 100%;
    overflow: auto;
    border-spacing: 0;
    border-collapse: collapse;
    table-layout: fixed;
    font-family: var(--font-primary);

    &__header,
    &__body {
      border: none;

      &.clickable-rows {
        ::ng-deep tr {
          cursor: pointer;

          &:hover {
            background: var(--color-blue-200);
          }
        }
      }
    }

    &__header-cell {
      overflow: hidden;
      color: var(--color-black);
      font-weight: var(--font-semibold);
      font-size: var(--text-xs);
      line-height: var(--line-sm);
      background-color: var(--color-white);
      position: sticky;
      height: 50px;
      z-index: 1;
      padding: 8px 16px;
      border-bottom: 1px solid var(--color-gray-300);
      text-align: left;
      vertical-align: middle;

      &:first-child {
        padding-left: 24px;
      }

      &:last-child {
        padding-right: 24px;
        border-right: 0;
      }

      &__resize-handle {
        display: inline-block;
        width: 10px;
        height: 100%;
        cursor: ew-resize;
      }

      &.sortable {
        .sortable-icon {
          color: var(--color-gray-600);
          margin-left: 6px;
          position: relative;
          --ng-icon__size: 20px !important;
          transform: translateY(3px);

          &.sorted-column {
            color: var(--color-blue-600);
          }
        }

        &:hover {
          cursor: pointer;
          background-color: var(--color-blue-200);
        }
      }
    }

    ::ng-deep.se-table-header-checkbox-container {
      .se-checkbox .checkbox-container {
        width: 20px;
        height: 20px;
        :focus-visible,
        input[type="checkbox"]:focus-visible,
        se-checkbox:focus-visible {
          outline: var(--color-black) auto 1px;
        }
      }
    }
  }

  &__pagination {
    width: 100%;
    // padding: 8px 24px 8px 24px;
  }

  .resize-handle {
    display: inline-block;
    width: 10px;
    height: 100%;
    position: absolute;
    top: 0;
    cursor: ew-resize;
  }

  .resize-handle-left {
    left: 0;
  }

  .resize-handle-right {
    right: 0;
  }

  .no-select {
    user-select: none;
  }

  &.gray-header {
    .se-table__header-cell {
      background-color: var(--color-gray-100);
    }
  }

  .summary-content-relative {
    position: relative;
    padding-left: 48px; // space for checkbox
  }

  .summary-checkbox-block {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    z-index: 1;

    ::ng-deep se-checkbox-cell {
      align-self: baseline;
      margin: 12px 8px;
    }
  }
}

.standard-padding-left {
  position: relative;
  padding-left: 16px;
}

// Base styles for summary mode
.se-table-summary-mode-wrapper {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  height: 100%;
  display: none;
}

.se-table-summary-row {
  display: flex;
  align-items: center;
  width: 100%;
  background: transparent;
  border: none;
  border-radius: 0;
  margin-bottom: 0;
  box-sizing: border-box;
}


.se-table-summary-content-cell {
  flex: 1 1 0;
  min-width: 0;
  padding: 0;
  border: none !important;
  background: transparent;
  width: 100%;
}

.se-table-summary-content-cell .summary-cell-block ::ng-deep se-default-cell>div.text-ellipsis {
  max-width: 90%;
}

.se-table__cell__mobile__header {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--color-black);
  line-height: var(--line-sm);
  background: none;
  padding: 8px 16px 0 0;
  text-align: left;
  vertical-align: middle;
}


// RESPONSIVE
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

// @media (max-width: 1200px)
@include media-breakpoint-between(md, xl) {
  .se-table-container {
    width: 100%;
    overflow-x: auto;
    box-sizing: border-box;

    .se-table {
      width: auto;
      min-width: 100%;
    }
  }
}

// @media (max-width: 768px)
@include media-breakpoint-down(md) {
  .se-table-component {
    ::ng-deep {
      .checkmark {
        height: 1.5rem !important;
        width: 1.5rem !important;
      }
    }

    &__pagination {
      padding: 16px 0 0 0;
    }

    .se-table-container .se-table {
      &__header {
        display: none;
      }

      &__body {
        display: block;

        ::ng-deep {
          tr[default-row] {
            display: flex;
            flex-direction: column;
            border: 1px solid var(--color-gray-300);
            border-collapse: collapse;

            &:not(:last-child):not(&.children-row) {
              margin-bottom: 16px;
            }
          }
        }
      }
    }
  }

  .se-table-container__select-all {
    padding: 1rem 1rem 0;

    ::ng-deep {
      .checkbox-container-label {
        font-size: var(--text-md);
        margin-left: 0.5rem;
      }
    }
  }

  // --- SUMMARY MODE MOBILE STYLES ---
  .se-table-summary-mode-wrapper {
    display: block;

    .se-table-container__select-all {
      padding: 1rem 10px 0;
    }
  }

  // --- SELECT ALL FORM PADDING FOR SUMMARY MODE MOBILE ---
  .summary-mode-mobile-select-all {
    padding: 1rem 12px;
  }

  tr.se-table-summary-row {
    border: 1px solid var(--color-gray-300) !important;
    border-radius: 0 !important;
    background: var(--color-white) !important;
    margin-bottom: 16px;
    padding-left: 4px;
    box-sizing: border-box;
    align-items: baseline !important;

    &:hover {
      background: var(--color-blue-200) !important;
    }

    ::ng-deep tr[default-row] {
      border: none !important;

      td {
        padding-left: 0 !important;
      }
    }
  }


  .se-table-summary-checkbox-cell {
    transform: translateY(-10px);
    border: none !important;
    background: transparent !important;
  }

  .se-table-summary-content-cell {
    border: none !important;
    background: transparent !important;

    table {
      border: 0 !important;
    }

    tr {
      border: none;
      box-shadow: none !important;
    }
  }

  .se-table-component.summary-mode {
    padding-left: 48px;
    position: relative;

    .se-table__header-cell:first-child,
    .se-table__cell:first-child {
      padding-left: 0 !important;
    }

    .se-table__header-cell.se-table-header-checkbox-container,
    .se-table__cell.se-table-header-checkbox-container {
      width: 1% !important;
      min-width: 1% !important;
      max-width: 1% !important;
      padding: 0 !important;
    }
  }

  .summary-chevron-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 2;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    width: 32px;
    transform: translateY(-4px);

    svg,
    .chevron-icon {
      width: 1rem;
      height: 1rem;
      color: var(--color-blue-600);
      fill: var(--color-blue-600);
      display: block;
    }
  }

  .summary-row-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
  }

  .summary-cell-block {
    margin-bottom: 0px;
    background: none;
    max-width: 90%;

    &.non-selectable {
      padding-left: 8px;
    }

    header {
      padding-bottom: 8px
    }

    &:last-child {
      margin-bottom: 12px;
    }

    ::ng-deep se-button-dropdown div.p-menu.p-component.p-menu-overlay.ng-star-inserted {
      position: absolute;
      top: inherit !important;
      left: inherit !important;
    }
  }
}

// Responsive: hide summary checkboxes on desktop if not in mobile mode
@include media-breakpoint-up(md) {
  .se-table-summary-mode-wrapper {
    display: none;
  }
}