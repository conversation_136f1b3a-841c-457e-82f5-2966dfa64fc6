pipeline {
    agent any

    environment {

        /******************************
            ENVIRONMENT VARIABLES
        ******************************/
        CREDENTIALS_ID = "JENKINS_API_KEY"
        RECIPIENTS_ON_ERROR = "<EMAIL>"
    }

    tools {
        jdk "JDK 11"
        maven "maven 3.6.2"
    }

    options {
        buildDiscarder(logRotator(numToKeepStr: '5', artifactNumToKeepStr: '5'))
    }

    stages {
        stage('Deploy_DEV') {
            steps {    
                withCredentials([usernamePassword(credentialsId: "${CREDENTIALS_ID}", passwordVariable: 'PASSWORD', usernameVariable: 'USERNAME')]) {

                    sh '''
                        
                        PROJECT_NAME="se-ui-components-mf-lib"
                        
                        echo "Launching deploy to DEV of ${PROJECT_NAME}..."
                        curl -X POST 'https://jenkins.indra.es/jenkinsATCMP/job/Deploy_Dev_Node18/buildWithParameters' -F PROJECT_NAME="${PROJECT_NAME}" -F BRANCH_NAME=master --user "${USERNAME}:${PASSWORD}"
                    '''
                }
            }
        }

    }

    post {

        failure {
            script {
                // send error e-mails always to the last commiter
                RECIPIENTS = sh (
                    script: 'git --no-pager show -s --format=\'%ae\'',
                    returnStdout: true).trim()
                // send error e-mails also to the RECIPIENTS_ON_ERROR
                if (RECIPIENTS_ON_ERROR?.trim()) {
                    RECIPIENTS = RECIPIENTS + ',' + RECIPIENTS_ON_ERROR
                }
            }
            step([$class: 'Mailer',
                notifyEveryUnstableBuild: true,
                recipients: "${RECIPIENTS}",
                sendToIndividuals: true])
        }
    }
}
