@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';

.se-dropdown-filter {
  position: relative;
  display: inline-block;
  width: 100%;
  max-width: 270px;

  .dropdown-header {
    padding: 5px 8px;
    background: var(--color-white);
    border: 1px solid var(--color-gray-400);
    cursor: pointer;
    display: flex;
    border-radius: 6px;
    justify-content: space-between;
    gap: 0.5rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    align-items: center;

    .dropdown-title {
      color: var(--color-gray-650);
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 8px;
      margin-right: 1.5rem;
      .selected-count-badge {
        height: 100%;
        align-content: center;
      }
      .selected-options-badge {
        align-content: center;
        se-badge ::ng-deep {
          .circle {
            border-radius: 999px !important;
            width: auto !important;
            padding: 1px 4.5px !important;
          }
        }
      }
    }

    .dropdown-title-selected {
      color: var(--color-black);
      margin-right: 1rem;

      &__clear-icon {
        margin-right: 3.5rem;
      }
    }
    #drop-icon, #clear-icon {
      color: var(--color-blue-500);
    }

    &:focus {
      box-shadow: none;
      border: 1px solid var(--color-blue-500);
      outline: 2px solid var(--color-primary-link);
      outline-offset: 1px;
    }
  }

  #drop-icon, #clear-icon {
    --ng-icon__size: 1.6rem !important;
    position: absolute;
    right: 0;
    margin-right: 12px;
  }

  #clear-icon {
    margin-right: 3rem;
    --ng-icon__size: 1.3rem !important;
  }

  .dropdown-list {
    position: absolute;
    width: auto;
    background-color: #fff;
    border: 1px solid #ccc;
    z-index: 1000;
    border-radius: 4px;
    margin-top: 4px;
    opacity: 0;
    transform: translateY(-10px);
    transition: max-height 0.4s ease, opacity 0.3s ease, transform 0.3s ease;
    pointer-events: none;
    min-width: 150px;
    max-width: 270px;

    .options-list {
      overflow-y: auto;
    }

    .action-buttons {
      display: flex;
      padding: 12px 8px;
      gap: 8px;
      se-button {
        display: flex;
        width: 100%;
        ::ng-deep button{
          width: 100%;
        }
      }
    }

    .dropdown-filter-checkbox {
      padding: 12px 8px;
    }

    &.open {
      opacity: 1;
      transform: translateY(0);
      pointer-events: auto;
    }

    .checkbox-container {
      padding: 12px 8px;
    }
  }

  .dropdown-item {
    display: block;
    padding: 5px;
    cursor: pointer;

    &:hover {
      background-color: #f9f9f9;
    }
  }
}


@include media-breakpoint-down(md) {
  .se-dropdown-filter,
  .se-dropdown-filter .dropdown-list {
    max-width: 100%;
  }
}


