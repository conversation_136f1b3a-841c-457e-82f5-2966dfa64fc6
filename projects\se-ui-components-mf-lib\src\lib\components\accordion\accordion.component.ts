import {
  Component,
  EventEmitter,
  Input,
  Output,
  SkipSelf,
  TemplateRef,
} from '@angular/core';

import { SeCheckbox } from '../checkbox/checkbox.model';
import { SeRadio } from '../radio/radio.model';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { ControlContainer, FormControl } from '@angular/forms';

@Component({
  selector: 'se-accordion',
  template: `
    <div class="se-accordion">
      <div class="se-accordion__header d-flex justify-content-between p-2">
        <div class="d-flex align-self-center me-2">
          <span *ngIf="title" [innerHTML]="title | translate | safeHtml"></span>
          <ng-container *ngIf="controlName && inputType">
            <se-checkbox
              *ngIf="inputType === 'checkbox' && inputCheckbox"
              [label]="inputCheckbox.label ?? ''"
              [id]="inputCheckbox.id"
              [disabled]="!!inputCheckbox.disabled"
              [formControlName]="controlName"
              (onClick)="handleSelect($event)"
            ></se-checkbox>

            <se-radio
              *ngIf="inputType === 'radio' && inputRadio"
              [id]="inputRadio.id || ''"
              [label]="inputRadio.label | translate"
              [name]="inputRadio.name"
              [value]="inputRadio.value"
              [disabled]="!!inputRadio.disabled"
              [formControlName]="controlName"
              (valueChanged)="handleSelect($event)"
            >
            </se-radio>
          </ng-container>

          <button
            *ngIf="tooltip"
            class="icon-button"
            [attr.aria-label]="tooltipAriaLabel | translate"
            [pTooltipAccessible]="tooltipText"
          >
            <ng-icon
              class="tooltip-icon"
              name="matInfo"
            ></ng-icon>
          </button>
        </div>
        <button class="se-accordion__header--toggler"
          *ngIf="collapsible"
          [attr.aria-expanded]="isOpen"
          [attr.aria-label]="toggleButtonAriaLabel | translate"
          (click)="handleCollapsedChange(!isOpen)">
            <ng-icon
              [ngClass]="{
                'rotate-90': isOpen,
                'rotate-270': !isOpen
              }"
              name="matArrowBackIosNewOutline"
            >
            </ng-icon>
          </button>
        </div>
        <div class="se-accordion__content"
          *ngIf="collapsible"
          [class.collapsed]="!isOpen"
          [attr.aria-hidden]="!isOpen"
          [@accordionContent]="!isOpen ? 'hidden' : 'visible'">
          <div class="p-3">
            <ng-content></ng-content>
          </div>
        </div>
      </div>
  `,
  styleUrls: ['./accordion.component.scss'],
  animations: [
    trigger('accordionContent', [
        state(
            'hidden',
            style({
                height: '0',
                display: 'none'
            })
        ),
        state(
            'visible',
            style({
                display: '*',
                height: '*'
            })
        ),
        transition('* => *', animate(100))
    ])
  ],
  viewProviders: [
    {
      provide: ControlContainer,
      useFactory: (container: ControlContainer) => container,
      deps: [[new SkipSelf(), ControlContainer]]
    }
  ]
})
export class AccordionComponent {

  @Input() title: string | undefined;
  @Input() inputCheckbox: SeCheckbox | undefined;
  @Input() inputRadio: SeRadio | undefined;
  @Input() inputType: 'checkbox' | 'radio' | undefined;
  @Input() tooltip = false;
  @Input() tooltipText: string | TemplateRef<HTMLElement> | undefined;
  @Input() tooltipAriaLabel = 'UI_COMPONENTS.TOOLTIP.ARIA_LABEL';
  @Input() controlName: string | undefined;
  @Input() collapsible: boolean = true;
  @Input() set collapsed(value: boolean) {
    this.isOpen = !value;
  }

  protected value: any;
  protected isOpen = false;
  protected toggleButtonAriaLabel = 'UI_COMPONENTS.ACCORDION.EXPAND';
  protected control: FormControl | undefined;


  @Output() onSelectionChange: EventEmitter<any> = new EventEmitter<any>();
  @Output() onCollapsedChange: EventEmitter<boolean> = new EventEmitter<boolean>();


  handleSelect(value: any): void {
    this.onSelectionChange.emit(value);
  }

  handleCollapsedChange(value: boolean): void {
    this.isOpen = value;
    this.toggleButtonAriaLabel = value
      ? 'UI_COMPONENTS.ACCORDION.COLLAPSE'
      : 'UI_COMPONENTS.ACCORDION.EXPAND';
    this.onCollapsedChange.emit(value);
  }

}
