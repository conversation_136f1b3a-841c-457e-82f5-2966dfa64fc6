/* Credits: https://www.joshwcomeau.com/css/custom-css-reset/ */

*,
*::before,
*::after {
  box-sizing: border-box;
}

:root {
  font-size: 16px !important;
}


body {
  font-family: "Open Sans", sans-serif;
  font-size: 16px;
}

* {
  margin: 0;
}

body {
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

#root,
#__next {
  isolation: isolate;
}

.rotate-90 {
  transform: rotate(90deg);
}
.rotate-180 {
  transform: rotate(180deg);
}
.rotate-270 {
  transform: rotate(270deg);
}

.p-dropdown-trigger-icon .p-icon {
  fill: currentColor
}

.p-datepicker-next-icon.p-icon,
.p-datepicker-prev-icon.p-icon {
  fill: currentColor
}

.cursor-pointer {
  cursor: pointer;
}