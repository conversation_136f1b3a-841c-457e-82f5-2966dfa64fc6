import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { lastValueFrom, map, Observable, Subject, take, takeUntil } from 'rxjs';
import {
  SeGetEnvironmentResponse,
  SeLoginResponse,
  SeLoginSimulatRequest,
  SeUser,
} from '../auth/auth.model';
import { SeAuthService } from '../auth/auth.service';
import { SeHttpService } from '../http/http-service.service';
import { LoginEndpointsService } from './login-endpoints.service';

const SIMULATED_USER_SESSION_NAME = 'pt-user';

@Injectable({
  providedIn: 'root',
})
export class SeLoginService implements OnDestroy {

  private unsubscribe: Subject<void> = new Subject();

  constructor(
    private authService: SeAuthService,
    private endpointsService: LoginEndpointsService
  ) {}

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  /**
   * Login
   * @description Call the login endpoint
   */
  login = (setSession?: boolean): Promise<SeLoginResponse> =>
    lastValueFrom(
      this.endpointsService.loginRequest()
        .pipe(
          take(1),
          map((response) => {
            if (setSession && response?.content?.usuario) {
              const userData = this.setUserData(response);

              this.authService.setSession(userData);
            }
            return response
          })
        )
    );


  /**
   * Login simulat
   * @description Call the login simulat endpoint
   */
  loginSimulat = (
    setSession: boolean,
    request: SeLoginSimulatRequest
  ): Promise<SeLoginResponse> =>
    lastValueFrom(
      this.endpointsService.loginSimulatRequest(request)
      .pipe(
        take(1),
        map((response) => {
          if (setSession && response?.content?.usuario) {
            const userData = this.setUserData(response);

            this.authService.setSession(
              userData,
              SIMULATED_USER_SESSION_NAME
            );
          }
          return response
        })
      )
    );

  private setUserData = (response: SeLoginResponse): SeUser =>
    Object.assign({}, response.content.usuario, {
      token: response.content.tokenJwt,
      tokenExpiration: response.content.tokenExpiration,
    });


  /**
   * LOGOUT
   * @description Call the logout endpoint
   */
  logout = (): Promise<string> =>
    lastValueFrom(
      this.endpointsService.logoutRequest()
      .pipe(
        take(1),
        map((response) => {
          //  Delete session
          this.authService.deleteSession();
          // Redirect
          if (response?.content?.url) this.redirect(response.content.url);
          return response.content?.url;
        })
      )
    );

  /**
   * Redirect to a URL after logging out
   * @param url Url to redirect to
   */
  redirect = (url: string): void => {
    window.location.href = url;
  };

  /**
   * Get Environment > Request
   * @returns Observable<SeGetEnvironmentResponse>
   */
  getEnvironment = (): Promise<SeGetEnvironmentResponse> =>
    lastValueFrom(
      this.endpointGetEnvironment()
      .pipe(take(1))
    )

  /**
   * getSeuUrlByEnv
   * @description Get SEU root url depending on the environment
   */
  getSeuUrlByEnv = (): Promise<SeGetEnvironmentResponse> =>
    lastValueFrom(
      this.endpointGetSeuUrlByEnv()
        .pipe(take(1))
    );


  endpointGetEnvironment = (): Observable<SeGetEnvironmentResponse> => this.endpointsService.endpointGetEnvironment()

  endpointGetSeuUrlByEnv = (): Observable<SeGetEnvironmentResponse> => this.endpointsService.endpointGetSeuUrlByEnv()
}
