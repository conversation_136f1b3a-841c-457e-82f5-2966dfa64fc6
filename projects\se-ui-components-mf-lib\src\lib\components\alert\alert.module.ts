import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { SeSharedModule } from '../../shared/shared.module';
import { SeButtonModule } from '../button/button.module';
import { AlertComponent } from './alert.component';

@NgModule({
  declarations: [AlertComponent],
  imports: [
    CommonModule, 
    SeSharedModule,
    SeButtonModule,
    TranslateModule.forChild(),
  ],
  exports: [AlertComponent],
})
export class SeAlertModule {}
