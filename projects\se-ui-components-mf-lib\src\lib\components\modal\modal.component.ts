import { Component, EventEmitter, Input, Output, TemplateRef } from '@angular/core';
import { SeModal, SeModalOutputEvents, SeModalSeverityClass } from './modal.model';
import { provideIcons } from '@ng-icons/core';
import { NG_ICONS } from '../../shared/shared.model';

@Component({
  selector: 'se-modal',
  template: `
    <div *ngIf="data" class="modal-container" [ngClass]="{ 'no-border': data.hideIcon }">
      <div class="modal-content" [ngClass]="data.severity ? severityClass[data.severity!].class : 'alert-default'">
        <!-- Header -->
        <div class="modal-header">
          <div class="modal-title">
            <!-- Back button (fullscreen) -->
            <!-- <div *ngIf="data.size === 'fullscreen'" class="back-button" (click)="closeModal(SeModalOutputEvents.CLOSE)">
              <ng-icon name="matArrowBackOutline" class="arrow-back"></ng-icon>
              <span>{{"UI_COMPONENTS.BUTTONS.BACK" | translate}}</span>
            </div> -->
            <div *ngIf="data.size === 'fullscreen'" class="back-button">
              <button
                (click)="closeModal(SeModalOutputEvents.CLOSE)">
                <ng-icon name="matArrowBackOutline" class="arrow-back"></ng-icon>
                <span>{{ "UI_COMPONENTS.BUTTONS.BACK" | translate }}</span>
              </button>
            </div>
            <!-- Icons -->
            <div>
              <ng-icon *ngIf="data.severity && !data.hideIcon" [name]="severityClass[data.severity!].icon" class="text-2xl"></ng-icon>
            </div>
            <!-- Title -->
            <span
              *ngIf="data.title"
              class="text-xl"
              [ngClass]="[data.titleTextWeight || 'semi-bold']"
              [innerHTML]="data.title | translate:data.titleParams | safeHtml"
            ></span>
          </div>

          <!-- Closable button -->
          <button
            *ngIf="data.size !== 'fullscreen'"
            class="close-button"
            (click)="closeModal(SeModalOutputEvents.CLOSE)"
            [attr.aria-label]="'UI_COMPONENTS.CONFIRMATION_MODAL.CLOSE'">
            <ng-icon name="matClose"></ng-icon>
          </button>
        </div>

        <!-- Body -->
        <div class="modal-body">
          <div *ngIf="data.subtitle" class="modal-subtitle" [ngStyle]="data.subtitleStyles"  [innerHTML]="data.subtitle | translate:data.subtitleParams | safeHtml"></div>
          <ng-content></ng-content>
        </div>

        <!-- Footer -->
        <div *ngIf="data.closable || data.secondaryButton || customActions" class="modal-footer">
          <div class="action-buttons">
            <!-- Secondary Button -->
            <se-button
              *ngIf="data.secondaryButton"
              btnTheme="secondary"
              class="secondary-button"
              (onClick)="onSecondaryButtonClick()">
              {{(data.secondaryButtonLabel || '') | translate}}
            </se-button>
            <!-- Close -->
            <se-button
              *ngIf="data.closable"
              [disabled]="!!data.closableDisabled"
              (onClick)="closeModal(SeModalOutputEvents.MAIN_ACTION)">
              {{(data.closableLabel ||'UI_COMPONENTS.BUTTONS.CLOSE') | translate}}
            </se-button>
            <!-- Custom footer -->
            <ng-container *ngTemplateOutlet="customActions"></ng-container>
          </div>
        </div>
      </div>
    </div>
    `,
  styleUrls: ['./modal.component.scss'],
  providers: [provideIcons(NG_ICONS)],
})
export class ModalComponent {

  SeModalOutputEvents = SeModalOutputEvents;

  @Input() data: SeModal | undefined;
  @Input() customActions: TemplateRef<any> | null = null;


  @Output() modalOutputEvent: EventEmitter<string> = new EventEmitter<string>();
  @Output() modalSecondaryButtonEvent: EventEmitter<string> = new EventEmitter<string>();

  severityClass: { [x: string]: SeModalSeverityClass } = {
    'error': {
      class: 'se-alert-danger',
      icon: 'matCancel'
    },
    'success': {
      class: 'se-alert-success',
      icon: 'matCheckCircle'
    },
    'warning': {
      class: 'se-alert-warning',
      icon: 'matWarning'
    },
    'info': {
      class: 'se-alert-info',
      icon: 'matInfo'
    }
  }

  closeModal(data: string): void {
    this.modalOutputEvent.emit(data)
  }

  onSecondaryButtonClick(): void {
    this.modalSecondaryButtonEvent.emit(SeModalOutputEvents.SECONDARY_ACTION)
  }
}
