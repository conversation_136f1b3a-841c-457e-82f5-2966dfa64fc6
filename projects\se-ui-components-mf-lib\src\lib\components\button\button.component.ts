import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, Output, TemplateRef, ViewChild } from '@angular/core';
import {
  SeButtonIconPositionEnum,
  SeButtonSizeEnum,
  SeButtonThemeEnum,
  SeButtonTooltipPosition,
  SeButtonTooltipPositionEnum,
  type SeButtonIconPosition,
  type SeButtonSize,
  type SeButtonTheme
} from './models';

@Component({
  selector: 'se-button',
  host: {
    "[style.pointer-events]": "'none'"
  },
  template: `
    <button
      [pTooltipAccessible]="tooltipText"
      [tooltipPosition]="tooltipPosition"
      class="se-button text-sm"
      [type]="type"
      [ngClass]="['se-button--' + size, 'se-button--' + btnTheme]"
      [class.se-button--only-icon]="!hasContent"
      [disabled]="disabled"
      [title]="title"
      [attr.aria-expanded]="ariaExpanded"
      [attr.aria-controls]="ariaControls"
      (click)="onClickEvent($event)"
      [attr.aria-label]="ariaLabel ?? ''"
    >
      <ng-icon
        *ngIf="icon"
        [name]="icon"
        class="se-button__icon se-button__icon--{{ iconPosition ?? SeButtonIconPositionEnum.LEFT }}"
        [size]="iconSize ?? '20px'"
        aria-hidden="true"
      />
      <div #buttonContent><ng-content></ng-content></div>
    </button>
  `,
  styleUrls: ['./button.component.scss'],
})
export class ButtonComponent {
  @ViewChild('buttonContent') buttonContent: ElementRef | undefined;

  @Input() size: SeButtonSize = SeButtonSizeEnum.DEFAULT;
  @Input() btnTheme: SeButtonTheme = SeButtonThemeEnum.PRIMARY;
  @Input() disabled = false;
  @Input() title: string | undefined;
  @Input() icon: string | undefined;
  @Input() iconSize?: string = '20px';
  @Input() iconPosition?: SeButtonIconPosition = SeButtonIconPositionEnum.LEFT;
  @Input() type: string = 'button';
  @Input() ariaExpanded: boolean | null | undefined;
  @Input() ariaControls: string | null | undefined;
  @Input() ariaLabel: string | null | undefined;
  @Input() tooltipText: TemplateRef<HTMLElement> | string | undefined;
  @Input() tooltipPosition: SeButtonTooltipPosition | undefined = SeButtonTooltipPositionEnum.TOP;

  @Output() onClick: EventEmitter<MouseEvent> = new EventEmitter<MouseEvent>();

  hasContent: boolean = true;

  SeButtonIconPositionEnum =SeButtonIconPositionEnum;

  constructor(private cdr: ChangeDetectorRef) {}

  ngAfterViewInit() {
    this.hasContent = !!this.buttonContent?.nativeElement.innerHTML.trim();
    this.cdr.detectChanges();
  }

  onClickEvent(event: MouseEvent): void {
    if(!this.disabled) {
      this.onClick.emit(event);
    }
  }
}
