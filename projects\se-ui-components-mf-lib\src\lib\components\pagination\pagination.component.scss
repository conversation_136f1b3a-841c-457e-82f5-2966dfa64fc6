:host ::ng-deep se-button .se-button {
  width: 28px;
  height: 28px;
}

:host ::ng-deep .paginator-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: var(--font-primary);
  background-color: var(--color-gray-200);
  padding: 8px 16px;

  .counter-dropdown-container {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    gap: 16px;

    .paginator-container__counter {
      font-size: var(--text-sm);
      margin: 0;
      line-height: var(--line-lg);
    }
  }

  .se-dropdown {
    margin: 0;
  }

  .buttons-paginator .p-paginator {
    button {
      padding: 4px, 16px, 4px, 16px;
      font-family: var(--font-primary);
      border: 1px solid var(--color-primary-action);
      border-radius: 4px;
      width: 90px;
      height: var(--line-lg);

      &.p-link:disabled {
        border: 1px solid var(--color-gray-400);
      }

      .p-icon-wrapper {
        display: none;
      }
    }
  }

  .p-paginator {
    padding: 0;
    gap: 8px;
    background: transparent;
    flex-wrap: nowrap;

    .p-paginator-pages {
      gap: 8px;
      display: flex;

      .p-paginator-page {
        color: var(--color-primary-action);
        border-radius: 4px;
        font-family: var(--font-primary);
        font-size: var(--text-sm);
        line-height: var(--line-sm);
        min-width: 28px;
        height: var(--line-lg);

        &.p-highlight {
          background: white;
          border: 1px solid var(--color-primary-action);
          cursor: default;
        }

        &:hover {
          background: var(--color-blue-600);
          color: white;
        }

        &:focus {
          outline: 2px solid var(--color-primary-link);
          outline-offset: 1px;
        }
      }
    }

    .p-paginator-last,
    .p-paginator-next,
    .p-paginator-prev,
    .p-paginator-first {
      font-family: var(--font-primary);
      font-size: var(--text-sm);
      line-height: var(--line-sm);
      min-width: 28px;
      height: var(--line-lg);
      border-radius: 4px;
      border: 1px solid var(--color-gray-600);
      color: var(--color-gray-600);

      &:not(.p-link:disabled) {
        background: white;
        color: var(--color-primary-action);
        border: 1px solid var(--color-primary-action);
      }

      &:not(.p-disabled):not(.p-highlight):hover {
        background: var(--color-blue-600);
        color: white;
        border-radius: 4px;
      }

      &:not(.p-disabled):not(.p-highlight):focus {
        outline: 2px solid var(--color-primary-link);
        outline-offset: 1px;
      }
    }
  }
}

// RESPONSIVE
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

@include media-breakpoint-down(md) {
  :host ::ng-deep .paginator-container {
    flex-direction: column;
    padding: 1rem;

    .counter-dropdown-container {
      width: 100%;
      text-align: center;
      justify-content: center;
      margin-bottom: 16px;
    }

    p-paginator {
      width: 100%;

      &.buttons-paginator .p-paginator {
        justify-content: space-between;

        .p-paginator-prev,
        .p-paginator-next {
          width: 100%;
          padding: 4px 16px;
        }
      }

      .p-paginator {
        gap: 0.5rem;
        width: 100%;
      }
    }
  }
}
