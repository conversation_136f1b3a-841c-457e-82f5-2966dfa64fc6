import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { SeButtonSizeEnum, SeButtonThemeEnum } from '../button';
import { RangeFilterComponent } from './range-filter.component';
import { SeRangeFilterModule } from './range-filter.module';

const meta: Meta<RangeFilterComponent> = {
  title: 'Components/Range filter',
  component: RangeFilterComponent,
  decorators: [
    moduleMetadata({
      imports: [
        SeRangeFilterModule,
        ReactiveFormsModule,
        TranslateModule.forChild(),
      ],
    }),
  ],
  args: {
    placeholder: 'SE_COMPONENTS.DROPDOWN.SHARES',
    closeFilterOnResetButton: false,
    resetButton: {
      size: SeButtonSizeEnum.DEFAULT,
      btnTheme: SeButtonThemeEnum.ONLY_TEXT,
      disabled: false,
      label: 'SE_COMPONENTS.DROPDOWN.RESET',
    },
    applyButton: {
      size: SeButtonSizeEnum.DEFAULT,
      btnTheme: SeButtonThemeEnum.PRIMARY,
      disabled: false,
      label: 'SE_COMPONENTS.DROPDOWN.APPLY_FILTERS',
    },
    fromInput: {
      label: 'From',
      currencyMode: true,
      currencySymbol: '€',
      placeholder: '00,00€',
    },
    toInput: {
      label: 'To',
      currencyMode: true,
      currencySymbol: '€',
      placeholder: '00,00€',
    },
    showClear: false
  },
  argTypes: {},
  tags: ['autodocs'],
  render: (args) => {
    const form = new FormGroup({
      shares: new FormControl({ from: 15, to: 20 }),
    });

    return {
      template: `
      <div [ngStyle]="{'width':'auto', 'height':'400px'}" class="d-flex justify-content-start">
      <form [formGroup]="form">
        <div class="row h-100">
          <div class="col-12 col-sm-4">
            <se-range-filter
              [formControlName]="'shares'"
              [showClear]="showClear"
              [closeFilterOnResetButton]="closeFilterOnResetButton"
              [placeholder]="placeholder|translate"
              [resetButton]="resetButton"
              [applyButton]="applyButton"
              [fromInput]="fromInput"
              [toInput]="toInput"
              (onSelectionChange)="onClick($event)">
            </se-range-filter>
          </div>
        </div>
      </form>
      </div>
      `,
      props: {
        ...args,
        form,
        onClick: (event: any) => {
          console.log('Clicked! - ', event);
          console.log('form - ', form.value);
        },
      },
    };
  },
};

export default meta;
type Story = StoryObj<RangeFilterComponent>;

export const RangeFilter: Story = {};
