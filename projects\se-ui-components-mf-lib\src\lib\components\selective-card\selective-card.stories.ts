import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { SelectiveCardComponent } from './selective-card.component';
import { SeSelectiveCardModule } from './selective-card.module';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { SeButtonModule } from '../button';

const meta: Meta<SelectiveCardComponent> = {
  title: 'Components/Selective Card',
  component: SelectiveCardComponent,
  decorators: [
    moduleMetadata({
      imports: [SeSelectiveCardModule, ReactiveFormsModule, SeButtonModule],
    }),
  ],
  tags: ['autodocs'],
  args: {
    headerTitle: 'Header title',
    items: [
      { title: 'Title 1', description: 'Description 1' },
      { title: 'Title 2', description: 'Description 2' },
    ],
    toggleButtonText: 'Seleccionar',
    enableRequestInfo: true,
    requestInfoButtonAriaLabel: '',
    requestInfoButtonTitle: 'show info',
    enableDownload: true,
    downloadButtonAriaLabel: '',
    downloadButtonTitle: 'download file',
    enableSelected: true,
    enableTitleInfo: false,
    titleInfoTooltipText: '',
    titleInfoTooltipAriaLabel: '',
    titleInfoTooltipTitle: 'info button',
    titleInfoTooltipEvent: 'hover',
    titleInfoTooltipPositionLeft: 0,
    titleInfoTooltipPositionTop: 0
  },
  argTypes: {
    toggleButtonText: {
      description: 'Card button text',
      type: 'string',
      table: {
        defaultValue: { summary: '' },
      },
    },
    headerTitle: {
      description: 'Card header title',
      type: 'string',
      table: {
        defaultValue: { summary: undefined },
      },
    },
    items: {
      description: 'Array of objects with title and description',
      control: { type: 'array', required: true },
      table: {
        defaultValue: { summary: [] },
      },
    },
    titleInfoTooltipTitle: {
      description: 'Title info icon\'s accessible title',
      type: 'string',
      table: {
        defaultValue: { summary: ""},
      },
    },
    titleInfoTooltipAriaLabel: {
      description: 'Title info icon\'s aria-label',
      type: 'string',
      table: {
        defaultValue: { summary: "" },
      },
    },
    titleInfoTooltipText: {
      description: 'Tooltip title info icon',
      type: 'string',
      table: {
        defaultValue: { summary: undefined },
      },
    },
    titleInfoTooltipEvent: {
      description: 'Tooltip event title info icon. If the event is \'hover-focus\' the tooltip is shown with tab key and mouse.',
      type: 'string',
      table: {
        defaultValue: { summary: 'hover' },
        type: { summary: 'hover | focus | hover-focus' } ,
      },
    },
    titleInfoTooltipPositionLeft: {
      description: 'Horizontal position of the tooltip',
      type: 'number',
      table: {
        defaultValue: { summary: 0 },
      },
    },
    titleInfoTooltipPositionTop: {
      description: 'Vertical position of the tooltip',
      type: 'number',
      table: {
        defaultValue: { summary: 0 },
      },
    },
    selected: {
      description: 'Whether the card is selected',
      control: { type: 'boolean' },
      table: {
        defaultValue: { summary: false },
      },
    },
    download: {
      action: 'download',
      type: { required: false, name: 'function' },
      description:
        'Output event that is triggered when the download button is clicked.',
      table: { defaultValue: { summary: 'void' }, type: { summary: 'event' } },
    },
    downloadButtonAriaLabel: {
      description: 'Download button\'s aria-label',
      type: 'string',
      table: {
        defaultValue: { summary: "" },
      },
    },
    downloadButtonTitle: {
      description: 'Download button\'s accessible title',
      type: 'string',
      table: {
        defaultValue: { summary: "" },
      },
    },
    requestInfo: {
      action: 'requestInfo',
      type: { required: false, name: 'function' },
      description:
        'Output event that is triggered when the info button is clicked.',
      table: { defaultValue: { summary: 'void' }, type: { summary: 'event' } },
    },
    requestInfoButtonAriaLabel: {
      description: 'Download button\'s aria-label',
      type: 'string',
      table: {
        defaultValue: { summary: "" },
      },
    },
    requestInfoButtonTitle: {
      description: 'Download button\'s accessible title',
      type: 'string',
      table: {
        defaultValue: { summary: "" },
      },
    },
    toggleButton: {
      action: 'toggleButton',
      type: { required: false, name: 'function' },
      description:
        'Output event that is triggered when the toggle button is clicked.',
      table: { defaultValue: { summary: 'void' }, type: { summary: 'event' } },
    },
    clickable: {
      description: 'Card is clickable',
      type: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
  },
  render: (args) => ({
    props: {
      ...args,
      form: new FormGroup({
        myControl: new FormControl(false),
      })
    },
    template: `
      <form [formGroup]="form">
        <se-selective-card
          formControlName="myControl"
          [headerTitle]="headerTitle"
          [headerItems]="headerItems"
          [items]="items"
          [toggleButtonText]="toggleButtonText"
          [enableRequestInfo]="enableRequestInfo"
          [requestInfoButtonAriaLabel]="requestInfoButtonAriaLabel"
          [requestInfoButtonTitle]="requestInfoButtonTitle"
          [enableSelected]="enableSelected"
          [enableDownload]="enableDownload"
          [downloadButtonAriaLabel]="downloadButtonAriaLabel"
          [downloadButtonTitle]="downloadButtonTitle"
          [enableTitleInfo]="enableTitleInfo"
          [titleInfoTooltipAriaLabel]="titleInfoTooltipAriaLabel"
          [titleInfoTooltipTitle]="titleInfoTooltipTitle"
          [titleInfoTooltipText]="titleInfoTooltipText"
          [titleInfoTooltipPositionLeft]="titleInfoTooltipPositionLeft"
          [titleInfoTooltipPositionTop]="titleInfoTooltipPositionTop"
          [titleInfoTooltipEvent]="titleInfoTooltipEvent"
          [titleIcon]="titleIcon"
          [stateTag]="stateTag"
          [clickable]="clickable">
        </se-selective-card>
      </form>
    `,
  }),
};

export default meta;
type Story = StoryObj<SelectiveCardComponent>;

export const Default: Story = {
  args: {
    headerTitle: 'Custom Title',
    items: [
      { title: 'Title 1', description: 'Description 1', clickable: true },
      { title: 'Title 2', description: 'Description 2' },
    ],
  },
};

export const Clicakble: Story = {
  args: {
    headerTitle: 'Custom Title',
    items: [
      { title: 'Title 1', description: 'Description 1', clickable: true },
      { title: 'Title 2', description: 'Description 2' },
    ],
    clickable: true,
  },
};

export const alignRight: Story = {
  args: {
    headerTitle: 'Custom Title',
    items: [
      { title: 'Title alineado derecha', description: '23 €', align: 'right' },
      { title: 'Title 2', description: 'Description 2' },
    ],
  },
};

export const DiferentSizeColumns: Story = {
  args: {
    headerTitle: 'Diferent Size Comuns and Text Separators',
    items: [
      { title: 'Title 1', description: 'Description 1' },
      { title: 'Column size 6', description: 'COlumn size 6', columnSize:6 },
      { title: 'Title 1', description: 'Description 1' },
      { title: 'Texto Separador', description: "", separatorTitle: true },
      { title: 'Title 2', description: 'Description 22' },
      { title: 'Title 2', description: 'Description 22' },
      { title: 'Title 2', description: 'Description 22' },
      { title: 'Title 2', description: 'Description 22' },
      { title: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.', description: '', columnSize: 12 },
    ],
  },
};

export const Selected: Story = {
  render: (args) => ({
    props: {
      ...args,
      form: new FormGroup({
        myControl: new FormControl(true),
        myControl1: new FormControl(false)
      }),
    },
    template: `
      <form [formGroup]="form">
        <se-selective-card
          formControlName="myControl1"
          [headerTitle]="headerTitle"
          [items]="items"
          [toggleButtonText]="toggleButtonText"
          [enableRequestInfo]="enableRequestInfo"
          [enableDownload]="enableDownload"
          [enableTitleInfo]="enableTitleInfo">
      </se-selective-card>
      <div style="margin-top: 8px;">
      <se-selective-card
        formControlName="myControl"
        [headerTitle]="headerTitle"
        [items]="items"
        [toggleButtonText]="toggleButtonText"
        [enableRequestInfo]="enableRequestInfo"
        [enableDownload]="enableDownload"
        [enableTitleInfo]="enableTitleInfo">
      </se-selective-card>
      </div>
      </form>
    `,
  }),
};

export const NoItems: Story = {
  args: {
    items: [],
  },
};

export const TitleInfoIconTooltip: Story = {
  args: {
    titleInfoTooltipText:'Example text',
    titleInfoTooltipEvent: 'hover-focus',
    enableTitleInfo:true
  },
};

export const WithTitleItems: Story = {
  args: {
    headerItems: [{icon:'matInfoOutline', title: "header item 1"},
      {icon:'matInfoOutline', title: "header item 2"},
      {icon:'matInfoOutline', title: "header item 3"}
    ],
    headerTitle: undefined
  },
};

export const WithTitleItemsAndTitle: Story = {
  args: {
    headerItems: [{icon:'matInfoOutline', title: "header item 1"},
      {icon:'matInfoOutline', title: "header item 2"},
      {icon:'matInfoOutline', title: "header item 3"}
    ]
  },
};

export const CustomButtons: Story = {
  render: (args) => ({
    props: {
      ...args,
      onCustomClick: () => console.log('Button pressed'),
    },
    template: `
        <se-selective-card
          formControlName="myControl1"
          [showSelectableButton]="false"
          [headerTitle]="headerTitle"
          [items]="items"
          [toggleButtonText]="toggleButtonText"
          [enableRequestInfo]="enableRequestInfo"
          [enableDownload]="enableDownload"
          [customActions]="customButton"
          [enableTitleInfo]="enableTitleInfo">
      </se-selective-card>
      <ng-template #customButton>
      <div style="flex-warp:nowrap">
      <se-button
        class="button-align"
        [btnTheme]="'secondary'"
        (onClick)="onCustomClick()"
        [size]="'small'"
        > first Button
      </se-button>
      <se-button
        class="button-align"
        [btnTheme]="'secondary'"
        (onClick)="onCustomClick()"
        [size]="'small'"
        > Secondary Button
      </se-button></div>
    </ng-template>
    `,
  }),
};
