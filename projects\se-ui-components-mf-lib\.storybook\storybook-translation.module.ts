import { HttpBackend, HttpClientModule } from '@angular/common/http';
import { NgModule } from '@angular/core';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { CustomTranslateLoader } from './get-translations.service';

function HttpLoaderFactory(httpBackend: HttpBackend) {
  return new CustomTranslateLoader(httpBackend, ['/mf/pt-commons-mf/i18n']);
}

@NgModule({
  imports: [
    HttpClientModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpBackend],
      },
    }),
  ],
  exports: [TranslateModule],
})
export class StorybookTranslateModule {
  private static hasBeenInitialized = false;

  constructor(translateService: TranslateService) {
    window.addEventListener('beforeunload', this.clearCache);

    if (!StorybookTranslateModule.hasBeenInitialized) {
      translateService.setDefaultLang('ca');
      translateService.use('ca')

      StorybookTranslateModule.hasBeenInitialized = true;
    } else {
      translateService.use('ca')
    }
  }

  clearCache() {
    Object.keys(localStorage)
      .filter((key) => key.startsWith('translation-'))
      .forEach((key) => localStorage.removeItem(key));
  }
}
