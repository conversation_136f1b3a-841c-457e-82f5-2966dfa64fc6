import { CellEventTypes } from './../cells.model';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { Column } from '../../columns/column.model';
import { FlattenedRow, RowConfig } from '../../rows/rows.model';
import { CellEventService } from '../cell-event.service';
import {
  CellComponent,
  FlattenedCell,
  CellConfig,
} from '../cells.model';
import { FormBuilder, FormGroup } from '@angular/forms';

@Component({
  selector: 'se-checkbox-cell',
  styleUrls: ['../styles/checkbox-cell-template.component.scss'],
  template: `
    <div class="checkbox-container">
      <form [formGroup]="formGroup">
        <se-checkbox
          formControlName="checkbox"
          [label]="label"
          [ariaLabel]="ariaLabel"
          [id]="checkboxId"
          [disabled]="isDisabled"
          (onClick)="onCheckboxChange($event)"
          (click)="onDisabledCheckboxClick()"
        ></se-checkbox>
      </form>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CheckboxCellComponent implements CellComponent, OnInit {
  @Input() value: boolean = false;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

  checkboxId: string = '';
  isChecked: boolean = false;
  formGroup!: FormGroup;

  constructor(
    private cellEventService: CellEventService,
    private formBuilder: FormBuilder
  ) {}

  ngOnInit() {
    this.isChecked = this.value;
    this.formGroup = this.formBuilder.group({
      checkbox: [this.isChecked],
    });
    this.checkboxId = this.column.key + '-' + this.row.id;
  }

  get rowConfig(): RowConfig {
    return this.row.rowConfig;
  }

  get label(): string {
    return this.rowConfig['label'] ?? '';
  }

  get ariaLabel(): string {
    return this.rowConfig['ariaLabel'] ?? this.label;
  }

  get isDisabled(): boolean {
    return this.rowConfig['isDisabled'] ?? false;
  }

  onCheckboxChange(event: boolean): void {
    this.cellEventService.emitEvent({
      type: CellEventTypes.CHECKBOX,
      cellName: 'checkboxCellComponent',
      cell: this.cell,
      data: { newData: event, rowId: this.row.id },
    });
  }

  onDisabledCheckboxClick() {
    if (this.row.rowConfig['isDisabled']) {
      this.onCheckboxChange(false);
    }
  }
}
