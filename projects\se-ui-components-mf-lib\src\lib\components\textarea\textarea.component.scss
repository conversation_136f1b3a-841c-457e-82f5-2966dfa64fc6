.textarea-container {
  position: relative;
  margin-bottom: 1rem;

  label {
    font-family: Open Sans;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    margin-top: 4px;
    white-space: pre-line;
    margin-bottom: 0px;
  }

  .readonly-text {
    font-family: Open Sans;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }

  .textarea-wrapper {
    position: relative;

    textarea {
      padding: 10px;
      padding-right: 30px;
      background: white;
      border: 1px solid var(--color-gray-400);
      box-sizing: border-box;
      border-radius: 4px;
      resize: both;
      width: 100%;
      min-height: 120px;
      color: var(--color-gray-700);
      font-family: var(--font-primary);
      font-size: var(--text-sm);
      line-height: var(--line-sm);

      &.invalid {
        border-color: var(--color-red-400);
      }
      
      &.valid {
        border-color: var(--color-green-300);
      }

      &:focus {
        border-color: var(--color-blue-500);
        outline: 2px solid var(--color-primary-link);
        outline-offset: 1px;
      }

      &.disabled {
        cursor: not-allowed;
        background: var(--color-gray-200);
        border-color: var(--color-gray-400);
        text-decoration: none;
        opacity: 0.8;

        .clear-button {
          display: none;
        }
      }
    }
  }

  .success-icon {
    color: var(--color-green-300);
    position: absolute;
    margin-bottom: 0.5%;
    bottom: 8px;
    right: 8px;
  }

  .clear-button {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 16px;
    height: 16px;
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 16px;
    z-index: 1;
    
    &:focus-visible {
      outline: var(--color-black) auto 1px;
    }
  }
}

