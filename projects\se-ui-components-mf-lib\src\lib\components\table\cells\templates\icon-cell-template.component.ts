// cell-template.component.ts
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import { CellComponent, CellConfig, FlattenedCell } from '../cells.model';

@Component({
  selector: 'se-icon-cell',
  template: `
    <div class="icon-cell-template">
      <ng-icon
        *ngIf="iconName"
        [name]="iconName"
        [title]="cellConfig['titleIcon']"
        [style]="cellConfig['iconStyle']"
      ></ng-icon>
      <div
        [ngClass]="{
          'text-ellipsis': cellConfig.ellipsis,
          'text-nowrap': cellConfig.nowrap
        }"
        [ngStyle]="cellConfig['ngStyle']"
        tooltipPosition="top"
        [pTooltipAccessible]="getTooltip()"
        [innerHTML]="value | translate | safeHtml"
      ></div>
    </div>
  `,
  styleUrls: ['../styles/icon-cell-template.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IconCellComponent implements CellComponent {
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

  getTooltip = (): string =>
    this.cellConfig.tooltip ? this.cellConfig.tooltipText ?? this.value : '';
  get iconName() {
    return this.cellConfig['iconName'];
  }
}
