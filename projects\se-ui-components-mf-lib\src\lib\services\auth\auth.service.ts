import { Injectable, Inject, Optional } from '@angular/core';
import { CookieService } from 'ngx-cookie-service';
import { SeDataStorageService } from '../data-storage/data-storage.service';
import { NAME_USER_STORAGE, SeUser } from './auth.model';

@Injectable({
  providedIn: 'root',
})
export class SeAuthService {
  constructor(
    private cookieService: CookieService,
    private dataStorageService: SeDataStorageService,
    @Inject(NAME_USER_STORAGE) @Optional() private nameUserStorage: string
  ) {
    this.nameUserStorage = nameUserStorage || 'pt-user';
  }

  // Session > DELETE
  deleteSession() {
    this.dataStorageService.deleteItem(this.nameUserStorage);
    this.deleteCookieToken();
  }

  // Session > SET
  setSession(userData: SeUser, sessionName?: string) {
    this.dataStorageService.setItem(
      sessionName || this.nameUserStorage,
      userData
    );
    // Por seguridad no almacenamos el token en una cookie
    //if (userData?.token && userData?.tokenExpiration) {
    //  this.setCookieToken(userData?.token, userData?.tokenExpiration);
    //}
  }

  // Session storage > SET User
  setSessionStorageUser(userData: SeUser) {
    this.dataStorageService.setItem(this.nameUserStorage, userData);
  }

  // Session storage > GET User
  getSessionStorageUser = (): SeUser =>
    this.dataStorageService.getItem(this.nameUserStorage);

  // Cookie > DELETE Token
  deleteCookieToken = (): void => {
    if (this.cookieService.check('token')) {
      /*
       * 	Note:
       *  The line below fixes the bug when there are multiple opened browser tabs,
       * 	we delete a cookie, but the other tabs still have that cookie
       */
      this.cookieService.set(
        'token',
        '',
        new Date(),
        '/',
        window.location.hostname
      );
      this.cookieService.delete('token', '/', window.location.hostname);
      // Recursive function: In case there are multiple cookies with the same name "token", we delete them all
      this.deleteCookieToken();
    }
  };

  // Cookie > SET Token
  setCookieToken = (token: string, tokenExpiration: string): void =>
    this.cookieService.set(
      'token',
      token,
      new Date(tokenExpiration),
      '/',
      window.location.hostname
    );

  // Cookie > GET Token
  getCookieToken = (): string => this.cookieService.get('token');

  // Cookie > Check token validity
  validCookieToken = (): boolean => !!this.cookieService.get('token');
}
