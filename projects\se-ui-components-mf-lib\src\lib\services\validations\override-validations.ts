import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function isEmptyInputValue(value: any): boolean {
  /**
   * Check if the object is a string or array before evaluating the length attribute.
   * This avoids falsely rejecting objects that contain a custom length attribute.
   * For example, the object {id: 1, length: 0, width: 0} should not be returned as empty.
   */
  return value == null || lengthOrSize(value) === 0;
}

/**
 * Extract the length property in case it's an array or a string.
 * Extract the size property in case it's a set.
 * Return null else.
 * @param value Either an array, set or undefined.
 */
function lengthOrSize(value: unknown): number | null {
  // non-strict comparison is intentional, to check for both `null` and `undefined` values
  if (value == null) {
    return null;
  } else if (typeof value === 'string') {
    return value.trim().length;
  } else if (Array.isArray(value)) {
    return value.length;
  } else if (value instanceof Set) {
    return value.size;
  }
  return null;
}

/**
 * @description
 * Validator that requires the control have a non-empty value.
 *
 * @returns An ValidatorFn with the `required` property and translateKey
 * if the validation check fails, otherwise `null`.
 *
 */
export const requiredValidator = (
  translateKey = 'SE_COMPONENTS.VALIDATIONS_ERRORS.required'
): ValidatorFn => {
  return (c: AbstractControl): ValidationErrors | null => {
    if (isEmptyInputValue(c.value)) {
      return {
        required: {
          translation: translateKey,
        },
      };
    }
    return null;
  };
};

/**
 * Validator that requires the control's value to match a regex pattern.
 * See `Validators.pattern` for additional information.
 */
export function patternValidator(
  pattern: string | RegExp,
  translateKey: string
): ValidatorFn {
  let regex: RegExp;
  let regexStr: string;
  if (typeof pattern === 'string') {
    regexStr = '';
    if (!pattern.startsWith('^')) regexStr += '^';
    regexStr += pattern;
    if (!pattern.endsWith('$')) regexStr += '$';
    regex = new RegExp(regexStr);
  } else {
    regexStr = pattern.toString();
    regex = pattern;
  }
  return (control: AbstractControl): ValidationErrors | null => {
    if (isEmptyInputValue(control.value)) {
      return null; // don't validate empty values to allow optional controls
    }
    const value: string = control.value;
    return regex.test(value)
      ? null
      : {
          pattern: {
            requiredPattern: regexStr,
            actualValue: value,
            translation: translateKey,
          },
        };
  };
}
