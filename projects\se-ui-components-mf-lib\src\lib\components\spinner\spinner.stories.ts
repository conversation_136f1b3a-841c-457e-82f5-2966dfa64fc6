import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { SeSpinnerModule } from './spinner.module';
import { SpinnerComponent } from './spinner.component';
import { TranslateModule } from '@ngx-translate/core';

const meta: Meta<SpinnerComponent> = {
  title: 'Components/Spinner',
  component: SpinnerComponent,
  decorators: [
    moduleMetadata({
      imports: [SeSpinnerModule, TranslateModule.forChild()]
    }),
  ],
  args: {
    message: 'Header title',
    subtitle: 'UI_COMPONENTS.SPINNER.LOADING',
    blocked: true,
  },
  argTypes: {
    blocked: {
      description: 'Block the layout.',
      type: 'boolean',
      table: {
        defaultValue: { summary: true },
      },
    },
    message: {
      description: 'Spinner message',
      control: { type: 'text' },
      table: {
        defaultValue: { summary: '' },
      },
    },
    subtitle: {
      description: 'Spinner subtitle',
      control: { type: 'text' },
      table: {
        defaultValue: { summary: 'UI_COMPONENTS.SPINNER.LOADING' },
      },
    },
  }
};

export default meta;
type Story = StoryObj<SpinnerComponent>;

export const Default: Story = {};

