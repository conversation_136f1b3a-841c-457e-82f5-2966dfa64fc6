import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { CheckboxComponent } from './checkbox.component';
import { SeCheckboxModule } from './checkbox.module';

const meta: Meta<CheckboxComponent> = {
  title: 'Components/Checkbox',
  component: CheckboxComponent,
  decorators: [
    moduleMetadata({
      imports: [SeCheckboxModule, ReactiveFormsModule],
    }),
  ],
  args: {
    label: '<b>Checkbox label too much largo</b> test prueba prove de que se vea el texto completo',
    disabled: false,
    id: 'checkbox-id',
    tooltip: false,
    tooltipText: '',
    tooltipClass: '',
    tooltipAriaLabel: '',
    tooltipEvent: 'hover',
    tooltipPositionLeft: 0,
    tooltipPositionTop: 0
  },
  argTypes: {
    disabled: {
      description: 'Determines if the checkbox is disabled or not.',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    id: {
      description: 'Checkbox ID',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'undefined' } },
    },
    label: {
      description: 'Checkbox label',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
    tooltip: {
      description: 'tooltip options',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    tooltipText: {
      description: 'tooltip message',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
    tooltipClass: {
      description: 'tooltip class',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
    tooltipAriaLabel: {
      description: 'tooltip aria label',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
    tooltipEvent: {
      description: 'Tooltip event. If the event is \'hover-focus\' the tooltip is shown with tab key and mouse.',
      type: 'string',
      table: {
        defaultValue: { summary: 'hover' },
        type: { summary: 'hover | focus | hover-focus' } ,
      },
    },
    tooltipPositionLeft: {
      description: 'Horizontal position of the tooltip',
      type: 'number',
      table: {
        defaultValue: { summary: 0 },
      },
    },
    tooltipPositionTop: {
      description: 'Vertical position of the tooltip',
      type: 'number',
      table: {
        defaultValue: { summary: 0 },
      },
    },
  },
  tags: ['autodocs'],
  render: (args) => ({
    template: `
      <form [formGroup]="form">
        <se-checkbox
          [label]="label"
          [id]="id"
          [tooltip]="tooltip"
          [tooltipText]="tooltipText"
          [tooltipEvent]="tooltipEvent"
          [tooltipAriaLabel]="tooltipAriaLabel"
          [tooltipPositionLeft]="tooltipPositionLeft"
          [tooltipPositionTop]="tooltipPositionTop"
          [tooltipClass]="tooltipClass"
          [disabled]="disabled"
          (onClick)="onClick($event)"
          [formControlName]="'value'">
        </se-checkbox>
      </form>
    `,
    props: {
      ...args,
      form: new FormGroup({
        value: new FormControl(false),
      }),
      onClick: (event: any) => {
        console.log('Clicked! - ', event);
      }
    },
  }),
};

export default meta;
type Story = StoryObj<CheckboxComponent>;


export const Disabled: Story = {

  render: (args) => ({
    template: `
      <form [formGroup]="form">
        <se-checkbox
          [label]="label"
          [id]="id"
          [tooltip]="tooltip"
          [tooltipText]="tooltipText"
          formControlName="value"
          (onClick)="onClick($event)"
          [disabled]="disabled">
        </se-checkbox>
      </form>
    `,
    props: {
      ...args,
      disabled: true,
      id: 'checkbox-id2',
      form: new FormGroup({
        value: new FormControl(true),
      }),
      onClick: (event: any) => {
        console.log('Clicked! - ', event);
      }
    },
  }),
};

export const CheckboxTooltip: Story = {
  args: {
    id: 'checkbox-id3',
    tooltip: true,
    tooltipText: 'ToolTip text',
    tooltipClass: '',
    tooltipEvent: 'hover-focus',
    tooltipAriaLabel: 'tooltip',
    tooltipPositionLeft: 3,
    tooltipPositionTop: -2
  }
};

export const WithLink: Story = { args: { label: 'Este lleva un <a href="https://www.google.com/" target="_blank">enlace</a>' } };

export const MultipleBinaryFalse: Story = {
  args: {
    id: 'checkbox-id44',
    binary: false,
  },
  render: (args) => ({
    template: `
      <form [formGroup]="form">
        <se-checkbox
          [label]="'Label 1'"
          [id]="'checkoboxm-1'"
          [value]="1"
          [binary]="false"
          [tooltip]="tooltip"
          [tooltipText]="tooltipText"
          [disabled]="disabled"
          formControlName="value"
          (onClick)="onClick($event)"
          [disabled]="disabled">
        </se-checkbox>
        <se-checkbox
          [label]="'Label 2'"
          [id]="'checkoboxm-2'"
          [value]="2"
          [binary]="false"
          [tooltip]="tooltip"
          [tooltipText]="tooltipText"
          formControlName="value"
          (onClick)="onClick($event)">
        </se-checkbox>
      </form>
      {{form.value | json}}
    `,
    props: {
      ...args,
      form: new FormGroup({
        value: new FormControl([1, 2]),
      }),
      onClick: (event: any) => {
        console.log('Clicked! - ', event);
      }
    },
  }),
};


