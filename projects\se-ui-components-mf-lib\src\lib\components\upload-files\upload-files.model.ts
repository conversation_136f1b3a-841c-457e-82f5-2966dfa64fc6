export interface FileData {
  fileBase64: string;
  type: string;
  fileDescription: string;
  name: string;
  id: string;
  size: number;
  file: File;
  extension: string;
  uploaded: boolean;
}
export interface FileUploaderError {
  message: string;
}

export interface FileUploaderTranslations {
  filesAllowed: string;
}

export interface FilesData {
  data: FileData[];
  error: boolean;
}

// File
export interface SeFile {
  name: string;
  size: number;
  extension: string;
  base64: string;
  idPadoct: string;
}

export const EMPTY_SIZE = 'emptySize';
export const ROW_CONFIG = 'rowConfig';

export enum UploadAcceptFormats {
  pdf = 'pdf',
  doc = 'doc',
  docx = 'docx',
  xls = 'xml',
  xlsx = 'xlsx',
  ppt = 'ppt',
  pptx = 'pptx',
  txt = 'txt',
  jpg = 'jpg',
  png = 'png',
  zip = 'zip',
  rar = 'rar',
  mp3 = 'mp3',
  wav = 'wav',
  mp4 = 'mp4',
  mpeg = 'mpeg',
  avi = 'avi',
  js = 'js',
  css = 'css',
  xml = 'xml',
  csv = 'csv'
}

export type UploadAcceptFormatsT = `${UploadAcceptFormats}`;

export const MIMEFormats: { [key: string]: string } = {
  pdf: 'application/pdf',
  doc: 'application/msword',
  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  xls: 'application/vnd.ms-excel',
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ppt: 'application/vnd.ms-powerpoint',
  pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  txt: 'text/plain',
  jpg: 'image/jpeg',
  png: 'image/png',
  zip: 'application/zip',
  rar: 'application/x-rar-compressed',
  mp3: 'audio/mpeg',
  wav: 'audio/wav',
  mp4: 'video/mp4',
  mpeg: 'video/mpeg',
  avi: 'video/x-msvideo',
  js: 'application/javascript',
  css: 'text/css',
  xml: 'application/xml',
  csv: 'text/csv'
};

export enum FileFormatsSeparation {
  COMMA = 'COMMA',
  SLASH = 'SLASH'
}
