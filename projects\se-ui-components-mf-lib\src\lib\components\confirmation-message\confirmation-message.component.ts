import { Component, Input, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { SeMessage, SeMessageI } from '../../services';

@Component({
  selector: 'se-confirmation-message',
  template: `
    <div
      *ngIf="data"
      class="se-confirmation-container"
      [ngClass]="wrapperClass"
    >
      <!-- Status -->
      <div
        *ngIf="data?.severity"
        class="se-confirmation-status"
        [ngClass]="{
          'confirmation-info': data.severity === 'info',
          'confirmation-warning': data.severity === 'warning',
          'confirmation-danger': data.severity === 'error',
          'confirmation-success': data.severity === 'success'
        }"
      >
        <!-- Icons -->
        <span
          *ngIf="data?.severity === 'info'"
          class="se-confirmation-icon rotate-180 sli2-exclamation-circle"
        ></span>
        <span
          *ngIf="data?.severity === 'warning'"
          class="se-confirmation-icon sli2-exclamation-circle"
        ></span>
        <span
          *ngIf="data?.severity === 'error'"
          class="se-confirmation-icon sli2-close-circle"
        ></span>
        <span
          *ngIf="data?.severity === 'success'"
          class="se-confirmation-icon sli2-check-circle"
        ></span>
      </div>

      <!-- Body -->
      <div class="se-confirmation-body">
        <button
          *ngIf="data?.closableFn || data?.closable"
          [ngClass]="{ 'se-confirmation-close-status': data.severity }"
          class="pi pi-times se-confirmation-close"
          (click)="closeMessage(data)"
          [attr.aria-label]="
            'UI_COMPONENTS.CONFIRMATION_MODAL.CLOSE' | translate
          "
        ></button>

        <h2
          *ngIf="data.title"
          class="se-confirmation-title"
          [ngClass]="{ 'pr-4': !data.severity }"
          [innerHTML]="data.title | translate : data.titleParams | safeHtml"
        ></h2>

        <se-button
          *ngIf="!data.collapsible && data?.collapsibleFn"
          (onClick)="collapsibleChange()"
        >
          {{ 'UI_COMPONENTS.CONFIRMATION_MODAL.SHOW_LESS' | translate }}
        </se-button>
        <se-button
          *ngIf="data.collapsible && data?.collapsibleFn"
          (onClick)="collapsibleChange()"
        >
          {{ 'UI_COMPONENTS.CONFIRMATION_MODAL.SHOW_MORE' | translate }}
        </se-button>
        <p *ngIf="data.titleDescription">
          {{ data.titleDescription | translate }}
        </p>
        <div *ngIf="!data?.collapsible">
          <hr *ngIf="data.titleDescription" />
          <!-- Subtitle -->
          <p
            *ngIf="data.subtitle"
            class="se-confirmation-subtitle"
            [innerHTML]="
              data.subtitle | translate : data.subtitleParams | safeHtml
            "
          ></p>

          <!-- Custom content inside card -->
          <div class="se-confirmation-custom-content">
            <ng-content></ng-content>
            <div
              class="se-confirmation-tracking-id"
              *ngIf="
                data.trackingId && data.severity === 'error' ? false : false
              "
            >
              <strong
                ><span>Tracking-ID: </span
              ></strong>
              <span
                *ngIf="data.trackingId"
                [innerHTML]="data.trackingId | safeHtml"
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./confirmation-message.component.scss'],
})
export class ConfirmationMessageComponent implements OnInit {
  @Input() data!: SeMessageI | null;
  @Input() wrapperClass: string = '';

  constructor(public sanitized: DomSanitizer) {}

  ngOnInit(): void {}

  closeMessage = (data: SeMessage): void => {
    if (!this.data) return;

    if (this.data.closableFn) {
      this.data.closableFn(data);
    } else {
      this.data = null;
    }
  };

  collapsibleChange = (): void => {
    if (!this.data) return;

    if (this.data.collapsible) {
      this.data.collapsible = false;
    } else {
      this.data.collapsible = true;
    }
  };
}
