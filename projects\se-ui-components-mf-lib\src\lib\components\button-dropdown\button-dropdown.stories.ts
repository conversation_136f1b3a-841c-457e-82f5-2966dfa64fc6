import { applicationConfig, moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { ButtonDropdownComponent } from './button-dropdown.component';
import { SeButtonDropdownModule } from './button-dropdown.module';
import { provideAnimations } from '@angular/platform-browser/animations';

/* More on how to set up stories at:
https://storybook.js.org/docs/angular/writing-stories/introduction */
type ButtonDropdownComponentStoryType = ButtonDropdownComponent & { label: string };

const meta: Meta<ButtonDropdownComponentStoryType> = {
  title: 'Components/Button Dropdown',
  component: ButtonDropdownComponent,
  decorators: [
    moduleMetadata({
      imports: [SeButtonDropdownModule],
    }),
    applicationConfig({
      providers: [provideAnimations()],
    }),
  ],
  tags: ['autodocs'],
  args: {
    items:  [
      {
        label: 'Obtenir carta de pagament per pagar en entitat bancària',
        command: () => {
            alert('New!')
        }
      },
      {
        label: 'Delete',
        command: () => {
          alert('Delete!')
        }
      },
      {
        label: 'Angular',
        url: 'http://angular.io'
      },
    ],
    label: 'Default button',
  },
  argTypes: {
    label: {
      description: 'Button content',
      control: 'text',
    },
    items: {
      description: 'An array of list definitions.',
      control: { type: 'object' },
      table: { defaultValue: { summary: '[]' } },
    },
    buttonOptions: {
      control: { type: 'object' },
      table: { defaultValue: { summary: '' } },
    }
  },
  render: (args) => ({
    props: args,
    template: `
      <se-button-dropdown
        [items]="items"
        [buttonOptions]="buttonOptions"
      >
        {{ label }}
      </se-button-dropdown>
    `,
  }),
};

export default meta;
type Story = StoryObj<ButtonDropdownComponentStoryType>;

export const Default: Story = {};
