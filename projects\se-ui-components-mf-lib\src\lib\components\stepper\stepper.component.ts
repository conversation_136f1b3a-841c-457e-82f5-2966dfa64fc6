import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { SeStep, SeStepStatus } from './stepper.model';
import { AriaLabelInput } from '../../directives/aria-label/aria-label.model';
import { Router } from '@angular/router';

@Component({
  selector: 'se-stepper',
  templateUrl: './stepper.component.html',
  styleUrls: ['./stepper.component.scss'] 
})
export class StepperComponent implements OnChanges {
  
  @Input() steps: SeStep[] = [];
  @Input() canNavigate: boolean = true;

  SeStepStatus = SeStepStatus;

  // MOBILE
  currentStepMobileIndex: number = -1;
  nextStepMobileIndex: number = -1;
  openCardMobile: boolean = false;
  
  // aria-label
  progressBarAriaLabel: AriaLabelInput = {
    querySelector: 'div[role="progressbar"]',
    translationKey: 'UI_COMPONENTS.STEPPER.ARIA_LABEL'
  }

  get accordionArrowIcon(): string {
    return this.openCardMobile
      ? 'matKeyboardArrowUpOutline'
      : 'matKeyboardArrowDownOutline';
  }

  constructor(
    private router: Router,
  ){ }

  ngOnChanges(_changes: SimpleChanges): void {
    this.currentStepMobileIndex = this.steps.findIndex(step => step.status === SeStepStatus.ON);
    this.nextStepMobileIndex = this.steps.findIndex(step => step.status === SeStepStatus.OFF);
  }

  statusClass: { [x: string]: string } = {
    'PAST': 'past',
    'OFF': 'off',
    'ON': 'on'
  }

  protected progressValue(): number {
    let progress = 0;
    if (this.steps.length > 0 && this.currentStepMobileIndex > -1) {
      const increase = 100/this.steps.length;
      progress =  increase * Number(this.currentStepMobileIndex + 1) 
    }
    return progress;
  }

  protected toggleMobileCard() {
    this.openCardMobile = !this.openCardMobile    
  }

  protected onClick = (event: MouseEvent, step: SeStep) => {
    if (step.status === SeStepStatus.PAST && this.canNavigate) {
      if(step.routerLink) {
        this.router.navigate([step.routerLink]);
        this.toggleMobileCard();
      } else if(step.url) {
        window.open(step.url, '_blank');
        this.toggleMobileCard();
      }

      
    }
    event.stopPropagation();
  }
}
