import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { ModalComponent } from './modal.component';
import { SeModalModule } from './modal.module';
import { SeModal } from './modal.model';
import { SeModalService } from './modal.service';
import { Component, Input } from '@angular/core';
import { NgbActiveModal, NgbModalModule } from '@ng-bootstrap/ng-bootstrap';
import { SeButtonModule } from '../button/button.module';

@Component({
  selector:'modal-service-button',
  template: `
    <button color="primary" (click)="openModal()"> Open Modal </button>
  `
})
class ModalServiceExampleButtonComponent {
  @Input() data!: SeModal;

  constructor(private modalService: SeModalService) { }

  openModal(): void {
    this.modalService.openModal(this.data);
  }
}

@Component({
  selector:'modal-sidebar-service-button',
  template: `
    <button color="primary" (click)="openModal()"> Open Modal </button>
  `
})
class ModalSidebarServiceExampleButtonComponent {
  @Input() data!: SeModal;
  @Input() side!: 'right' | 'left';

  constructor(private modalService: SeModalService) { }

  openModal(): void {
    this.modalService.openSidebarModal(this.data, this.side);
  }
}

@Component({
  template: `
    <se-modal 
      [data]="data"
      (modalOutputEvent)="closeModal($event)"
      (modalSecondaryButtonEvent)="closeModal('cancel')"
      [customActions]="customButton"
    > 
    </se-modal>
    <ng-template #customButton>
      <se-button
        class="button-align"
        [btnTheme]="'secondary'"
        (onClick)="onCustomClick()"> 
        Custom Button
      </se-button>
    </ng-template>
  `,
})
class ModalServiceExampleComponent {
  @Input() data!: SeModal;
  
  constructor(private activatedModalService: NgbActiveModal){}

  closeModal($event:string) {
    this.activatedModalService.close();
  }

  onCustomClick = () => console.log('Secondary Button')
}

const base_data: SeModal = {
  severity: 'info',
  closable: true,
  closableLabel: 'Tancar',
  closableDisabled: false,
  secondaryButton: false,
  secondaryButtonLabel: 'Cancel',
  title: 'Encapçalament de text',
  titleTextWeight: 'semi-bold',
  subtitle: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip.',
} 

// More on how to set up stories at: https://storybook.js.org/docs/angular/writing-stories/introduction
const meta: Meta<ModalComponent> = {
  title: 'Components/Modal',
  component: ModalComponent,
  decorators: [
    moduleMetadata(
      { 
        declarations: [
          ModalServiceExampleButtonComponent, 
          ModalSidebarServiceExampleButtonComponent,
          ModalServiceExampleComponent
        ],
        imports: [SeModalModule, SeButtonModule, NgbModalModule] 
      }
    ),
  ],
  tags: ['autodocs'],
  args: {
    data: base_data
  },
  argTypes: {
    data: {
      description: 'Sets the modal data',
      control: { type: 'object' }
    }
  },
  render: (args) => ({
    props: args,
    template: `
    <se-modal
      [data]="data"
      (modalOutputEvent)="data = null">
    </se-modal>
    `,
  }),
};

export default meta;
type Story = StoryObj<ModalComponent>;

export const InnerHTMLSubtitle: Story = {
  args: { data: {
    ...base_data, 
    subtitle: "<p>Texto con introducido con innerHTML</p><p>Parrafo que va debajo</p>",
    severity: 'success'
   } }
};

export const Error: Story = {
  args: { data: {...base_data, severity: 'error' } }
};

export const Success: Story = {
  args: { data: {...base_data, severity: 'success' } }
};

export const Info: Story = {
  args: { data: {...base_data, severity: 'info' } }
};

export const Warning: Story = {
  args: { data: {...base_data, severity: 'warning' } }
};

export const ErrorNoIcon: Story = {
  args: { data: {...base_data, hideIcon: true, severity: 'error' } }
};

export const SuccessNoIcon: Story = {
  args: { data: {...base_data,hideIcon: true, severity: 'success' } }
};

export const InfoNoIcon: Story = {
  args: { data: {...base_data,hideIcon: true, severity: 'info' } }
};

export const WarningNoIcon: Story = {
  args: { data: {...base_data,hideIcon: true, severity: 'warning' } }
};

export const NoData: Story = {
  render: (args) => ({
    props: {
      data: {
        closable: true,
        titleTextWeight: 'regular',
        title: 'Encapçalament de text',
        subtitle: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip.',
      }
    },
    template: `
    <se-modal
      [data]="data"
      (modalOutputEvent)="data = null">
    </se-modal>
    `,  
  }),
};

export const ModalService: Story = {
  render: (args) => ({
    props: {
      data: {
        ...base_data,
        component: ModalServiceExampleComponent
      }
    },
    template: `
    <modal-service-button
      [data]="data">
    </modal-service-button>
    `,  
  }),
};

export const Fullscreen: Story = {
  render: (args) => ({
    props: {
      data: {
        ...base_data,
        size: 'fullscreen',
        hideIcon: true,
        component: ModalServiceExampleComponent
      }
    },
    template: `
    <modal-service-button
      [data]="data">
    </modal-service-button>
    `,  
  }),
};

export const Sidebar: Story = {
  render: (args) => ({
    props: {
      data: {
        ...base_data,
        backdrop: false,
        component: ModalServiceExampleComponent
      },
      side: 'right'
    },
    template: `
    <modal-sidebar-service-button
      [side]="side"
      [data]="data">
    </modal-sidebar-service-button>
    `,  
  }),
};