:host se-button ::ng-deep {
  button {
    width: 100% !important;
  }
}
:host se-badge ::ng-deep {
  .circle {
    border-radius: 999px !important;
    width: auto !important;
    padding: 1px 4.5px !important;
  }
}

:host {
  .buttons-container {
    padding: 12px;
  }

  .se-dropdown-filter {
    position: relative;
    display: inline-block;
    width: auto;

    .dropdown-header {
      padding: 5px 8px;
      background: var(--color-white);
      border: 1px solid var(--color-gray-400);
      cursor: pointer;
      display: flex;
      border-radius: 6px;
      justify-content: space-between;
      gap: 0.5rem;
      transition: border-color 0.3s ease, box-shadow 0.3s ease;

      .dropdown-title {
        color: var(--color-gray-650);
        display: flex;
        gap: 8px;
        margin-right: 1.5rem;
        .selected-count-badge {
          height: 100%;
          align-content: center;
        }
      }

      .dropdown-title-selected {
        color: var(--color-black);
        margin-right: 1rem;

        &__clear-icon {
          margin-right: 2rem;
        }
      }
      #drop-icon, #clear-icon{
        color: var(--color-blue-500);
      }

      &:focus {
        box-shadow: none;
        border: 1px solid var(--color-blue-500);
        outline: 2px solid var(--color-primary-link);
        outline-offset: 1px;
      }
    }

    #drop-icon, #clear-icon {
      --ng-icon__size: 1.6rem !important;
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      align-self: center;
      margin-right: 4px;
    }

    #clear-icon {
      margin-right: 2rem;
    }

    .dropdown-list {
      position: absolute;
      width: auto;
      background-color: #fff;
      border: 1px solid #ccc;
      z-index: 1000;
      border-radius: 4px;
      margin-top: 4px;
      opacity: 0;
      transform: translateY(-10px);
      transition: max-height 0.4s ease, opacity 0.3s ease, transform 0.3s ease;
      pointer-events: none;
      min-width: 150px;

      .action-buttons {
        display: flex;
        padding: 12px 8px;
        gap: 8px;
        se-button {
          display: flex;
          width: 100%;
          ::ng-deep button {
            width: 100%;
          }
        }
      }

      .dropdown-filter-checkbox {
        height: auto;
        width: 300px;
        padding: 12px 8px 0 12px;
        overflow: hidden;
      }

      &.open {
        opacity: 1;
        transform: translateY(0);
        pointer-events: auto;
      }

      .checkbox-container {
        padding: 12px 8px;
      }
    }

    .dropdown-item {
      display: block;
      padding: 5px;
      cursor: pointer;

      &:hover {
        background-color: var(--color-gray-100);
      }
    }
  }
}
