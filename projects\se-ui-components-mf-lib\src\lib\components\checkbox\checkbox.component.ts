import { Component, Input, forwardRef, Output, EventEmitter, TemplateRef } from '@angular/core';
import {
  ControlContainer,
  ControlValueAccessor,
  FormControl,
  FormGroup,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';

@Component({
  selector: 'se-checkbox',
  template: `
    <div class="se-checkbox" [ngClass]="styleClass">
      <div class="checkbox-container" [ngClass]="{
          disabled: disabled, focused: focused,
          'invalid': control.touched && control.status === 'INVALID',
        }">
        <input
          type="checkbox"
          class="checkbox-input"
          [id]="id+'_checkbox-input'"
          [value]="value"
          [checked]="checked()"
          [disabled]="disabled"
          [attr.aria-checked]="checked()"
          (change)="onCheckboxChange($event)"
          (keyup)="onInputKey($event)"
          (blur)="onInputBlur($event)"
          [attr.aria-label]="(ariaLabel ?? label) | translate"
        />
        <span class="checkmark">
          <ng-icon name="matCheckOutline"></ng-icon>
        </span>
        <label [for]="id+'_checkbox-input'" class="checkbox-container-label text-sm" [innerHTML]="label | translate | safeHtml"></label>
      </div>
      <button
        *ngIf="tooltip"
        class="icon-button"
        [pTooltipAccessible]="tooltipText"
        [attr.aria-label]="tooltipAriaLabel"
        [tooltipEvent]="tooltipEvent"
        [positionLeft]="tooltipPositionLeft"
        [positionTop]="tooltipPositionTop"
        [tabindex]="
          tooltipText &&
          (tooltipEvent === 'focus' || tooltipEvent === 'hover-focus')
            ? 0
            : -1
        "
      >
        <ng-icon
          class="tooltip-icon"
          [ngClass]="tooltipClass"
          name="matInfo"
        ></ng-icon>
      </button>
    </div>
    <se-error-message *ngIf="binary" [control]="control"></se-error-message>
  `,
  styleUrls: ['./checkbox.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CheckboxComponent),
      multi: true,
    },
  ],
})
export class CheckboxComponent implements ControlValueAccessor {

  @Input() label: string = 'SE_COMPONENTS.CHECKBOX.SELECT';
  @Input() id: string | undefined;
  @Input() formControlName: string | number | null = null;
  @Input() tooltip = false;
  @Input() tooltipText: string | TemplateRef<HTMLElement> | undefined;
  @Input() binary: boolean = true;
  @Input() readonly: boolean = false;
  @Input() value: any;
  @Input() tooltipClass: string | undefined;
  @Input() tooltipAriaLabel: string = "";
  @Input() tooltipEvent: 'hover' | 'focus' | 'hover-focus' = 'hover';
  @Input() tooltipPositionLeft: number = 0;
  @Input() tooltipPositionTop: number = 0;
  @Input() ariaLabel: string | undefined;
  @Input() styleClass: string = '';

  @Input() set disabled(value: boolean) {
    this.setDisabledState(value);

    if(this.binary) {
      if(!value) {
        this.getFormControl()?.enable({ emitEvent: true });
      } else {
        this.getFormControl()?.disable({ emitEvent: true });
      }
    }
  }

  get disabled() { return this._disabled };

  @Output() onClick: EventEmitter<any> = new EventEmitter<any>();

  model: any;
  control!: FormControl;
  focused: boolean = false;

  private _disabled: boolean = false;

  constructor(private controlContainer: ControlContainer) { }

  ngOnInit() {
    this.control = this.getFormControl()!;

    this._disabled = this.control.disabled || this.disabled;

    this.control.registerOnDisabledChange((value) => { this.setDisabledState(value) })
  }

  private onChange: (value: boolean) => void = () => { };
  private onTouched: () => void = () => { };

  onInputKey(event:any) {
    const keyEvent = event as KeyboardEvent;

    if(keyEvent.code === 'Tab') this.focused = true;
    if (keyEvent.code === 'Enter') this.onCheckboxChange(event);
  }

  onInputBlur(event:any) {
    this.focused = false;
  }

  onCheckboxChange(event: Event) {
    if (!this.disabled && !this.readonly) {

      let newModelValue;

      if (!this.binary) {
        if (this.checked()) newModelValue = this.model.filter((val: object) => val !== this.value);
        else newModelValue = this.value ? [...this.model, this.value] : [this.value];

        if (this.control) {
          this.control.setValue(newModelValue);
        }

      } else {
        this.value = !this.value;
        newModelValue = this.value;
      }

      this.model = newModelValue;
      this.onChange(this.model);
      this.onTouched();
      this.onClick.emit(this.model);
    }
  }

  writeValue(value: any): void {
    if(!this.binary) {
      this.model = value ?? [];
    } else {
      this.model = value;
      this.value = value;
    }
  }

  registerOnChange(fn: (value: any) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this._disabled = isDisabled;
  }

  checked = () => this.binary ? this.value : this.model?.includes(this.value);

  private getFormControl() : FormControl | undefined {
    if(this.control){
      return this.control;
    }

    if(this.formControlName) {

      return (this.controlContainer.control as FormGroup).get(
        this.formControlName!.toString()
      ) as FormControl;
    }

    return;
  }
}
