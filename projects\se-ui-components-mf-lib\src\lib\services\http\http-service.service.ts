import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { defer, Observable, throwError, timeoutWith, map } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { v4 as uuid } from 'uuid';
import { SeAuthService } from '../auth/auth.service';
import { SeHttpOptions, SeHttpRequest } from './http-service.model';
import { SeMessageI } from '../message/message.model';
import { SeMessageService } from '../message/message.service';
import { SeSpinnerService } from '../../components/spinner/spinner.service';
import { SeDataStorageService } from '../data-storage/data-storage.service';
import { MfConfiguration } from '../../models/mf-configuration.model';

const DEFAULT_TIMEOUT: number = 310000;
const TRACKING_HEADER: string = 'trackingId';
// Borrar cuando todos los micros esten actualizados a la ultima version de commons-client-lib
const TRACKING_HEADER_TEMP: string = 'x-tracking-id';
const CHANNEL_HEADER: string = 'Channel';
const SUPPRESS_MESSAGE_HEADER: string = 'Suppress-Error-Message';

@Injectable({
  providedIn: 'root',
})
export class SeHttpService {
  constructor(
    private http: HttpClient,
    private translateService: TranslateService,
    private msgService: SeMessageService,
    private spinnerService: SeSpinnerService,
    private authService: SeAuthService,
    private storageService: SeDataStorageService
  ) {}

  /**
   * Private methods
   */

  // Set request data
  private setRequest<T>(requestOptions: SeHttpRequest): Observable<any> {
    // Set headers
    let headers: HttpHeaders = new HttpHeaders(
      requestOptions.headers || undefined
    );
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.append('Accept', 'application/json');
    headers = headers.append(CHANNEL_HEADER, 'web');
    let trackingId = uuid();
    headers = headers.append(TRACKING_HEADER, trackingId);
    headers = headers.append(TRACKING_HEADER_TEMP, trackingId);

    // Set session token headers
    const tokenExpiration = this.authService.getSessionStorageUser()?.tokenExpiration;
    const idSession = this.authService.getSessionStorageUser()?.idSession;

    // Calculamos token de autorización
    if (tokenExpiration || idSession) {
      let token = '';
      // Damos prioridad al token JWT si no ha expirado
      if (tokenExpiration) {
        let expirationDate = new Date(tokenExpiration);
        if (expirationDate > new Date()) {
          token = this.authService.getSessionStorageUser()?.token || '';
        }
      }
      // Si no hay token JWT, comprobamos si hay un id de sesión
      if (!token && idSession) {
        token = idSession;
      }
      // Añadimos la cabecera de autorización
      if (token) {
        headers = headers.append('Authorization', token);
      }
    }

    // Set language header
    const language: string = this.translateService.currentLang;
    if (language) {
      headers = headers.append('Language', language);
    }

    // Set origen header
    const origen: string = (this.storageService.getItem('mfConfigurationPersonal') as MfConfiguration)?.aplicacio;
    if (origen && headers.get('Origen') === null) {
      headers = headers.append('Origen', origen);
    }

    // Conditionally set Suppress-Error-Message header
    if (requestOptions.suppressErrorMessage) {
      headers = headers.append(SUPPRESS_MESSAGE_HEADER, 'true');
    }

    // Set http options
    const options: SeHttpOptions = {
      headers,
      params: requestOptions.params || {},
      responseType: requestOptions.responseType || ('text' as 'json'),
      reportProgress: requestOptions.reportProgress || true,
    };

    // Default values mapping
    requestOptions = { ...requestOptions };
    requestOptions.timeout = requestOptions.timeout || DEFAULT_TIMEOUT;
    requestOptions.url = requestOptions.baseUrl
      ? requestOptions.baseUrl + requestOptions.url
      : requestOptions.url;
    requestOptions.body = requestOptions.body || null;
    requestOptions.spinner = requestOptions.spinner !== false ? true : false;
    requestOptions.spinnerMsg =
      requestOptions.spinnerMsg || 'UI_COMPONENTS.SPINNER.LOADING';
    requestOptions.spinnerSubtitle = requestOptions.spinnerSubtitle || '';
    requestOptions.returnType = requestOptions.returnType || 'observable';
    requestOptions.parseResult =
      requestOptions.parseResult !== false ? true : false;
    requestOptions.clearExceptions = requestOptions.clearExceptions || false;

    // Show spinner (default) / or not
    if (requestOptions.spinner) {
      // console.log('this.spinnerService.start');
      this.spinnerService.start(
        requestOptions.spinnerMsg,
        requestOptions.spinnerSubtitle
      );
    }

    // Clear existing exceptions
    if (requestOptions.clearExceptions) {
      this.msgService.resetMessages();
    }

    // Set http call
    const methods = {
      get: this._get.bind(this),
      delete: this._delete.bind(this),
      post: this._post.bind(this),
      put: this._put.bind(this),
    };

    // Use requestOptions.method to access the correct method
    let method = methods[requestOptions.method];
    let result: Observable<any> = method(requestOptions, options);

    // Parse result (default) / or not
    if (result && requestOptions.parseResult) {
      result = result.pipe(
        map((response: any) => {
          try {
            return JSON.parse(response);
          } catch (error) {
            console.warn(`Error trying to parse the response: ${error}`);
            return result;
          }
        })
      );
    }

    // Return: Observable (default)/Promise
    if (requestOptions.returnType === 'promise') {
      result.toPromise();
    }

    return result;
  }

  private _get(
    requestOptions: SeHttpRequest,
    options: SeHttpOptions
  ): Observable<any> {
    const timeout = Number(requestOptions.timeout) || Infinity;
    return this.http.get(requestOptions.url, options).pipe(
      timeoutWith(
        timeout,
        defer(() => {
          this.setTimeoutException();
          // throwError() from 'rxjs' is used to create an observable that emits an error
          return throwError(new Error('Request timed out'));
        })
      )
    );
  }

  private _post(
    requestOptions: SeHttpRequest,
    options: SeHttpOptions
  ): Observable<any> {
    const timeout = Number(requestOptions.timeout) || Infinity;

    return this.http
      .post(requestOptions.url, requestOptions.body, options)
      .pipe(
        timeoutWith(
          timeout,
          defer(() => {
            this.setTimeoutException();
            return throwError(new Error('Request timed out'));
          })
        )
      );
  }

  private _put(
    requestOptions: SeHttpRequest,
    options: SeHttpOptions
  ): Observable<any> {
    const timeout = Number(requestOptions.timeout) || Infinity;

    return this.http.put(requestOptions.url, requestOptions.body, options).pipe(
      timeoutWith(
        timeout,
        defer(() => {
          this.setTimeoutException();
          return throwError(new Error('Request timed out'));
        })
      )
    );
  }

  private _delete(
    requestOptions: SeHttpRequest,
    options: SeHttpOptions
  ): Observable<any> {
    const timeout = Number(requestOptions.timeout) || Infinity;

    return this.http.delete(requestOptions.url, options).pipe(
      timeoutWith(
        timeout,
        defer(() => {
          this.setTimeoutException();
          return throwError(new Error('Request timed out'));
        })
      )
    );
  }

  // Client timeout exception
  private setTimeoutException() {
    const exception: SeMessageI = {
      severity: 'error',
      title: 'UI_COMPONENTS.EXCEPTIONS.CODES.TIMEOUT',
      subtitle: 'UI_COMPONENTS.EXCEPTIONS.MSG.TIMEOUT',
      closable: true,
      closableFn: this.msgService.deleteMessage,
    };

    this.msgService.addMessages([exception]);
  }

  /**
   * Public methods
   */

  public get<T>(requestOptions: SeHttpRequest): Observable<any> {
    return this.setRequest<T>(requestOptions);
  }

  public post<T>(requestOptions: SeHttpRequest): Observable<any> {
    return this.setRequest<T>(requestOptions);
  }

  public put<T>(requestOptions: SeHttpRequest): Observable<any> {
    return this.setRequest<T>(requestOptions);
  }

  public delete<T>(requestOptions: SeHttpRequest): Observable<any> {
    return this.setRequest<T>(requestOptions);
  }
}
