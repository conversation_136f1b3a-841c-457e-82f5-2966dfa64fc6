// RESPONSIVE
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins/breakpoints';

.action-button {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 4px;
  background: transparent;
  font-size: var(--text-lg);
  color: var(--color-blue-500);
  display: none;
  justify-content: center;
  align-items: center;

  &:hover {
    color: var(--color-blue-700);
  }

  &:focus {
    outline: 2px solid var(--color-primary-link);
    outline-offset: 1px;
  }

  @include media-breakpoint-up(md) {
    display: inline-flex;
  }
}

se-button-dropdown ::ng-deep .se-button {
  width: 30px;
  height: 30px;
  padding: 0;

  &:hover {
    background: transparent;
    color: var(--color-primary-action);
    border: 1px solid transparent;
  }
}
