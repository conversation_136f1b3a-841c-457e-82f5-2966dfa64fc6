@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins/breakpoints';
@import '../../styles/base/typography.scss';

.se-button {
  font-family: var(--font-primary);
  font-weight: var(--font-semibold);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  padding: 9px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  gap: 8px;
  width: 100%;
  font-size: var(--text-sm);
  line-height: var(--line-sm);
  pointer-events: auto;

  &:disabled {
    pointer-events: none;
  }

  &__icon {
    &--right {
      order: 2;
      min-width: 15px;
    }

    &--left {
      order: 0;
      min-width: 15px;
    }
  }

  &--only-icon {
    padding: 8px;
    gap: 0px;
  }

  &--large {
    min-height: 44px;
    min-width: 44px;
  }

  &--default {
    min-height: 40px;
    min-width: 40px;
  }

  &--small {
    padding: 4px 16px;
    ng-icon {
      display: ruby;
    }
  }

  &:focus {
    outline: 2px solid var(--color-primary-link);
    outline-offset: 1px;
  }

  &--primary {
    border: 1px solid var(--color-primary-action);
    background: var(--color-primary-action);
    color: var(--color-white);

    &:hover {
      background: var(--color-blue-600);
      border: 1px solid var(--color-blue-600);
    }

    &:active {
      background: var(--color-blue-700);
      border: 1px solid var(--color-blue-700);
    }

    &:disabled {
      cursor: not-allowed;
      background: var(--color-gray-550);
      border: 1px solid var(--color-gray-550);
    }
  }

  &--secondary {
    border: 1px solid var(--color-primary-action);
    background: var(--color-white);
    color: var(--color-primary-action);

    &:hover {
      background: var(--color-blue-200);
    }

    &:active {
      border: 1px solid var(--color-blue-700);
      color: var(--color-blue-700);
      background: var(--color-blue-300);
    }

    &:disabled {
      cursor: not-allowed;
      border: 1px solid var(--color-gray-400);
      background: var(--color-white);
      color: var(--color-gray-550);
    }
  }

  &--onlyText {
    background: transparent;
    border: 1px solid transparent;
    color: var(--color-primary-action);

    &:hover {
      background: var(--color-blue-600);
      border: 1px solid var(--color-blue-600);
      color: var(--color-white);
    }

    &:active {
      background: var(--color-blue-700);
      border: 1px solid var(--color-blue-700);
      color: var(--color-white);
    }

    &:disabled {
      cursor: not-allowed;
      background: transparent;
      border: 1px solid transparent;
      color: var(--color-gray-550);
    }
  }

  &--trueOnlyText {
    background: transparent;
    border: 1px solid transparent;
    color: var(--color-primary-action);
    padding: 0;

    &:disabled {
      cursor: not-allowed;
      background: transparent;
      border: 1px solid transparent;
      color: var(--color-gray-550);
    }

    &:focus {
      outline: none !important;
    }
  }

  &--danger {
    background: var(--color-white);
    color: var(--color-red-400);
    border: 1px solid var(--color-red-400);

    &:hover {
      background: var(--color-red-400);
      color: var(--color-white);
    }

    &:active {
      background: var(--color-red-500);
      border: 1px solid var(--color-red-500);
      color: var(--color-white);
    }

    &:disabled {
      cursor: not-allowed;
      border: 1px solid var(--color-gray-400);
      background: transparent;
      color: var(--color-gray-550);
    }
  }

  &--link {
    @extend .se-link, .secondary;
    padding: 0 !important;
    min-height: auto !important;
    justify-content: flex-start;
    text-align: start;
    &:focus {
      outline: none !important;
    }
  }
}

:host ::ng-deep button * {
  pointer-events: none;
}

//RESPONSIVE
@include media-breakpoint-up(sm) {
  .se-button {
    width: auto;
  }
}
