import {
  ValidationErrors,
  ValidatorFn,
  AbstractControl,
  FormGroup,
} from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';

/**
 * Validator: less than
 * @description Validates if a field value (validateField) is less than another field value (originalField).
 * The validation is originally implemented at a FormGroup level, but this validation function changes it to a formControl level.
 * @param originalField formControlName of the original field
 * @param validateField formControlName of the field which value has to be checked
 * @returns {ValidatorFn} Return a validator function
 */
export const validatorLessThanOther = (
  originalField: string,
  validateField: string,
  translateKey: string,
  translateService: TranslateService
): ((form: FormGroup) => ValidationErrors | null) => {
  return (form: FormGroup): ValidationErrors | null => {
    let originalControl = form.get(originalField);
    let validateControl = form.get(validateField);

    if (originalControl && validateControl) {
      let original = originalControl.value;
      let validate = validateControl.value;
      if (
        (original || original === 0) &&
        (validate || validate === 0) &&
        (original <= validate || original === null)
      ) {
        // If the field to validate is not lower than the original -> set the error to the field
        let currentErrors = validateControl.errors || {};
        currentErrors['lessThanOther'] = translateService.instant(translateKey);
        validateControl.setErrors(currentErrors);
      } else if (
        (original || original === 0) &&
        (validate || validate === 0) &&
        original > validate
      ) {
        // If the field to validate is less -> remove the error
        let currentErrors = validateControl.errors || {};
        delete currentErrors['lessThanOther'];
        validateControl.setErrors(
          Object.keys(currentErrors).length ? currentErrors : null
        );
      }
    }

    return null;
  };
};

/**
 * Validator: equals or less than
 * @description Validates if a field value (validateField) is less or equals than another field value (originalField).
 * The validation is originally implemented at a FormGroup level, but this validation function changes it to a formControl level.
 * @param originalField formControlName of the original field
 * @param validateField formControlName of the field which value has to be checked
 * @returns {ValidatorFn} Return a validator function
 */
export const validatorEqualsOrLessThanOther = (
  originalField: string,
  validateField: string,
  translateKey: string
): ((form: FormGroup) => ValidationErrors | null) => {
  return (form: FormGroup): ValidationErrors | null => {
    let original = form.get(originalField)?.value;
    let validate = form.get(validateField)?.value;
    if (
      form.get(originalField) &&
      form.get(validateField) &&
      (original || original === 0 || validate || validate === 0) &&
      (original < validate || original === null)
    ) {
      // If the field to validate is not lower than the original -> set the error to the field
      let currentErrors = form.get(validateField)?.errors || {};
      currentErrors['equalsOrLessThanOther'] = translateKey;
      form.get(validateField)?.setErrors(currentErrors);
    } else if (
      form.get(originalField) &&
      form.get(validateField) &&
      (original || original === 0) &&
      (validate || validate === 0) &&
      original >= validate
    ) {
      // If the field to validate is equals or less -> remove the error
      let currentErrors = form.get(validateField)?.errors || {};
      if (currentErrors) {
        delete currentErrors['equalsOrLessThanOther'];
        form
          .get(validateField)
          ?.setErrors(Object.keys(currentErrors).length ? currentErrors : null);
      }
    }
    return null;
  };
};

/**
 * Validator: greater than
 * @description Validates if a field value (validateField) is greater than another field value (originalField).
 * The validation is originally implemented at a FormGroup level, but this validation function changes it to a formControl level.
 * @param originalField formControlName of the original field
 * @param validateField formControlName of the field which value has to be checked
 * @returns {ValidatorFn} Return a validator function
 */
export const validatorGreaterThanOther = (
  originalField: string,
  validateField: string,
  translateKey: string
): ((form: FormGroup) => ValidationErrors | null) => {
  return (form: FormGroup): ValidationErrors | null => {
    let original = form.get(originalField)?.value;
    let validate = form.get(validateField)?.value;
    if (
      form.get(originalField) &&
      form.get(validateField) &&
      (original || original === 0 || validate || validate === 0) &&
      (original >= validate || original === null)
    ) {
      // If the field to validate is not lower than the original -> set the error to the field
      let currentErrors = form.get(validateField)?.errors || {};
      currentErrors['greaterThanOther'] = translateKey;
      form.get(validateField)?.setErrors(currentErrors);
    } else if (
      form.get(originalField) &&
      form.get(validateField) &&
      (original || original === 0) &&
      (validate || validate === 0) &&
      original < validate
    ) {
      // If the field to validate is greater -> remove the error
      let currentErrors = form.get(validateField)?.errors || {};
      if (currentErrors) {
        delete currentErrors['greaterThanOther'];
        form
          .get(validateField)
          ?.setErrors(Object.keys(currentErrors).length ? currentErrors : null);
      }
    }
    return null;
  };
};

/**
 * Validator: equals or greater than
 * @description Validates if a field value (validateField) is greater or equals than another field value (originalField).
 * The validation is originally implemented at a FormGroup level, but this validation function changes it to a formControl level.
 * @param originalField formControlName of the original field
 * @param validateField formControlName of the field which value has to be checked
 * @returns {ValidatorFn} Return a validator function
 */
export const validatorEqualsOrGreaterThanOther = (
  originalField: string,
  validateField: string,
  translateKey: string
): ((form: FormGroup) => ValidationErrors | null) => {
  return (form: FormGroup): ValidationErrors | null => {
    let original = form.get(originalField)?.value;
    let validate = form.get(validateField)?.value;
    if (
      form.get(originalField) &&
      form.get(validateField) &&
      (original || original === 0 || validate || validate === 0) &&
      (original > validate || original === null)
    ) {
      // If the field to validate is not lower than the original -> set the error to the field
      let currentErrors = form.get(validateField)?.errors || {};
      currentErrors['equalsOrGreaterThanOther'] = translateKey;
      form.get(validateField)?.setErrors(currentErrors);
    } else if (
      form.get(originalField) &&
      form.get(validateField) &&
      (original || original === 0) &&
      (validate || validate === 0) &&
      original <= validate
    ) {
      // If the field to validate is equals or Greater -> remove the error
      let currentErrors = form.get(validateField)?.errors || {};
      if (currentErrors) {
        delete currentErrors['equalsOrGreaterThanOther'];
        form
          .get(validateField)
          ?.setErrors(Object.keys(currentErrors).length ? currentErrors : null);
      }
    }
    return null;
  };
};

/**
 * Validator: different Values
 * @description Validates if a field value (validateField) is different than another field value (originalField).
 * The validation is originally implemented at a FormGroup level, but this validation function changes it to a formControl level.
 * @param originalField formControlName of the original field
 * @param validateField formControlName of the field which value has to be checked
 * @returns {ValidatorFn} Return a validator function
 */
export const validatorDifferentValues = (
  originalField: string,
  validateField: string,
  translateKey: string
): ((form: FormGroup) => ValidationErrors | null) => {
  return (form: FormGroup): ValidationErrors | null => {
    let original = form.get(originalField)?.value;
    let validate = form.get(validateField)?.value;
    if (original != null && validate != null && original === validate) {
      // If the field to validate is equal than the original -> set the error to the field
      let currentErrors = form.get(validateField)?.errors || {};
      currentErrors['differentValues'] = translateKey;
      form.get(validateField)?.setErrors(currentErrors);
    } else {
      // If the field to validate is different -> remove the error
      let currentErrors = form.get(validateField)?.errors || {};
      if (currentErrors) {
        delete currentErrors['differentValues'];
        form
          .get(validateField)
          ?.setErrors(Object.keys(currentErrors).length ? currentErrors : null);
      }
    }
    return null;
  };
};

/**
 * Validator: field required
 * @description Validates if a field of the two is filled.
 * The validation is originally implemented at a FormGroup level, but this validation function changes it to a formControl level.
 * @param requiredFields A list of the required formControlNames
 * @param translateKey Translate key to show the validation error message
 * @param fieldsTranslateKeys A list of the formControlNames translation keys
 * @returns {ValidatorFn} Return a validator function
 */
export const validatorAtLeastOneRequired = (
  requiredFields: string[],
  translateKey: string,
  fieldsTranslateKeys: string[],
  translateService: TranslateService
): ((form: FormGroup) => ValidationErrors | null) => {
  return (form: FormGroup): ValidationErrors | null => {
    const translatedFields: string = fieldsTranslateKeys
      .map((translationKey) => translateService.instant(translationKey))
      .join(', ');

    let showErrors: boolean = false;
    let isValid: boolean = false;

    requiredFields.forEach((formControlName: string) => {
      const control = form.get(formControlName);
      if (control) {
        if (control.dirty || control.touched) {
          showErrors = true;
        }
        if (control.value || control.value === 0 || control.value === false) {
          isValid = true;
        }
      }
    });

    requiredFields.forEach((formControlName: string) => {
      const control = form.get(formControlName);
      if (control) {
        let currentErrors: ValidationErrors = control.errors || {};
        if (showErrors && !isValid) {
          control.markAsTouched();
          currentErrors['fieldRequired'] = translateService.instant(
            translateKey,
            { fields: translatedFields }
          );
          control.setErrors(currentErrors);
        } else {
          delete currentErrors['fieldRequired'];
          control.setErrors(
            Object.keys(currentErrors).length ? currentErrors : null
          );
        }
      }
    });

    return null;
  };
};

/**
 * Validator: sellers
 * @description Validates if the total sellers percentage amounts to 100%
 * @returns {ValidatorFn} Return a validaton error
 */

export const validatorSellers: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  let totalPercentage = 0;
  if (c.value?.length > 0) {
    c.value.forEach((person: any) => {
      totalPercentage += Number(
        person?.roundedPercentage || person.rightPercentage
      );
    });
    if (totalPercentage != 100) {
      return { pattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.lstSellers' };
    }
  }
  return null;
};
