import { Injectable, OnDestroy } from '@angular/core';
import {
  ActivatedRoute,
  ActivatedRouteSnapshot,
  NavigationCancel,
  NavigationEnd,
  Router,
} from '@angular/router';
import { MenuItem } from 'primeng/api';
import { Subject, Observable } from 'rxjs';
import { filter, map, startWith, takeUntil } from 'rxjs/operators';
import { SeMessageService } from '../message/message.service';
import { iSnapshotData } from './routing.model';
import { SeStep, SeStepStatus } from '../../components/stepper/stepper.model';

@Injectable({
  providedIn: 'root',
})
export class SePageLayoutService implements OnDestroy {
  private unsubscribe: Subject<void> = new Subject();

  // Steps:
  private availableSteps: { [x: string]: SeStep[] } = {};
  private currentStepSubject: Subject<SeStep[]> = new Subject();

  // Current breadcrumb
  breadcrumb: MenuItem[] = [];
  breadcrumbIndex: number | null = null;

  // breadcrumb: cita previa
  availableBreadcrumb: { [x: string]: MenuItem[] } = {};

  // Page title
  title: string | null = null;

  // Global info
  globalInfo: string[] = [];

  // Element visibility
  private _isElementVisible: boolean = true;
  private _isExceptionViewerVisible: boolean = true;
  private _isStepperVisible: boolean = true;
  private _isHeaderVisible: boolean = true;

  //Try to use isStepperVisible instead of isElementVisible
  get isElementVisible(): boolean {
    return this._isElementVisible;
  }

  get isExceptionViewerVisible(): boolean {
    return this._isExceptionViewerVisible;
  }

  get isStepperVisible(): boolean {
    return this._isStepperVisible;
  }

  get isHeaderVisible(): boolean {
    return this._isHeaderVisible;
  }

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private msgService: SeMessageService
  ) {
    //this.router.events.subscribe( (event: RouterEvent) => console.log(event))
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  /**
   * Declare module steppers
   * @description For a spefici module, set all the different steppers avialable
   * @param availableSteps
   */
  setAvailableSteps = (availableSteps: { [x: string]: SeStep[] }): void => {
    this.availableSteps = { ...availableSteps };
  };

  setAvailableBreadcrumbs = (availableBreadcrumbs: {
    [x: string]: MenuItem[];
  }): void => {
    this.availableBreadcrumb = { ...availableBreadcrumbs };
  };

  setSteps = (steps: SeStep[]) => {
    this.currentStepSubject.next(steps);
  };

  getCurrentSteps(): Observable<SeStep[]> {
    return this.currentStepSubject.asObservable();
  }

  /**
   * Update page layout
   * @description When a new component/page is loaded, update the page layout (title, stepper, messages, etc.)
   */
  listenNavigation = (): void => {
    // Listen to route changes
    this.router.events
      .pipe(
        takeUntil(this.unsubscribe),
        filter(
          (event) =>
            event instanceof NavigationEnd || event instanceof NavigationCancel
        ),
        startWith(this.router),
        map(() => this.activatedRoute.snapshot),
        map((route) => {
          while (route.firstChild) {
            route = route.firstChild;
          }
          return route;
        })
      )
      .subscribe((snapshot: ActivatedRouteSnapshot) => {
        this.updatePageLayout(snapshot.data);
      });
  };

  /**
   * Update page layout
   * @description When a new component/page is loaded, update the page layout (title, stepper, messages, etc.)
   * @param {WcComponent} data
   */
  updatePageLayout = (data: iSnapshotData): void => {
    // Update page title
    this.setPageTitle(data);

    // Update page messages (errors, validations, etc.)
    this.setMessages();

    // Update steps
    this.updateSteps(data);

    // Update breadcrumb
    this.setBreadcrumb(data);

    // Update stepper visibility
    this.setStepperVisibility(data);

    //Update header visibility
    this.setHeaderVisibility(data);

    //Update exception viewer visibility
    this.setExceptionViewerVisibleVisibility(data);
  };

  // Set steps list
   updateSteps = (data: iSnapshotData): void => {
    if (data?.stepperId && data?.stepId) {
      const menuItems = this.availableSteps[data.stepperId];
      this.updateStepsStatus(data.stepId, menuItems);
    }
  };

  // Update steps status: past/off/on
  private updateStepsStatus = (stepId: string, currentSteps: SeStep[]): void => {
    const index = currentSteps.findIndex((step) => step.id === stepId);

    if (index >= 0) {
      const updatedSteps = currentSteps.map((step, i) => {
        if (i < index) {
          step.status = SeStepStatus.PAST;
        } else if (i === index) {
          step.status = SeStepStatus.ON;
        } else {
          step.status = SeStepStatus.OFF;
        }
        return step;
      });

      this.currentStepSubject.next(updatedSteps);
    }
  };

  // Set Breadcrumb list
  setBreadcrumb = (data: iSnapshotData): void => {
    this.breadcrumb = [];
    if (data?.breadcrumbCurrent && data?.breadcrumbId) {
      this.breadcrumb = this.availableBreadcrumb[data.breadcrumbCurrent];
      this.updateBreadcrumbs(data.breadcrumbId);
    }
  };

  updateBreadcrumbs = (breadcrumbId: string): void => {
    this.breadcrumbIndex = null;
    if (this.breadcrumb?.length > 0) {
      for (let index = 0; index < this.breadcrumb.length; index++) {
        if (breadcrumbId === this.breadcrumb[index].id) {
          this.breadcrumbIndex = index;
        }
      }
    }
  };

  // Page title
  setPageTitle = (data: iSnapshotData): void => {
    this.title = data?.title || null;
  };

  setStepperVisibility = (data: iSnapshotData): void => {
    this._isElementVisible = data?.isElementVisible ?? true;//Try to use isStepperVisible instead of isElementVisible
    this._isStepperVisible = data?.isElementVisible ?? true;
  };

  setHeaderVisibility = (data: iSnapshotData): void => {
    this._isHeaderVisible = data?.isHeaderVisible ?? true;
  }

  setExceptionViewerVisibleVisibility = (data: iSnapshotData): void => {
    this._isExceptionViewerVisible = data?.isExceptionViewerVisible ?? true;
  }

  // Update page messages (errors, validations, etc.)
  setMessages = (): void => this.msgService.resetMessages();

  // Global information
  setGlobalInfo = (data: string[]): void => {
    this.globalInfo = data ? [...data] : [];
  };
}
