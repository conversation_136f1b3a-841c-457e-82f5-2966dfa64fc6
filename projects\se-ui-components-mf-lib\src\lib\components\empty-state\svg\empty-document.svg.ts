import { Component } from "@angular/core";

@Component({
  selector: 'svg-empty-document',
  template: `<svg width="82" height="52" viewBox="0 0 82 52" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M78.4997 8.35156C80.3948 8.35156 81.9311 9.88784 81.9311 11.7829C81.9311 13.678 80.3948 15.2143 78.4997 15.2143H58.8919C60.787 15.2143 62.3233 16.7506 62.3233 18.6457C62.3233 20.5408 60.787 22.0771 58.8919 22.0771H69.6762C71.5713 22.0771 73.1076 23.6133 73.1076 25.5084C73.1076 27.4035 71.5713 28.9398 69.6762 28.9398H64.689C62.2995 28.9398 60.3625 30.4761 60.3625 32.3712C60.3625 33.6346 61.3429 34.7784 63.3037 35.8025C65.1987 35.8025 66.735 37.3388 66.735 39.2339C66.735 41.129 65.1987 42.6653 63.3037 42.6653H22.6174C20.7223 42.6653 19.186 41.129 19.186 39.2339C19.186 37.3388 20.7223 35.8025 22.6174 35.8025H3.49973C1.60464 35.8025 0.0683594 34.2663 0.0683594 32.3712C0.0683594 30.4761 1.60464 28.9398 3.49973 28.9398H23.1076C25.0027 28.9398 26.5389 27.4035 26.5389 25.5084C26.5389 23.6133 25.0027 22.0771 23.1076 22.0771H10.8527C8.95758 22.0771 7.4213 20.5408 7.4213 18.6457C7.4213 16.7506 8.95758 15.2143 10.8527 15.2143H30.4605C28.5654 15.2143 27.0291 13.678 27.0291 11.7829C27.0291 9.88784 28.5654 8.35156 30.4605 8.35156H78.4997ZM78.4997 22.0771C80.3948 22.0771 81.9311 23.6133 81.9311 25.5084C81.9311 27.4035 80.3948 28.9398 78.4997 28.9398C76.6046 28.9398 75.0684 27.4035 75.0684 25.5084C75.0684 23.6133 76.6046 22.0771 78.4997 22.0771Z" fill="#CBE3F4"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M52.3593 7.86392L56.919 41.1202L57.3287 44.4566C57.4607 45.5314 56.6963 46.5097 55.6215 46.6417L26.9155 50.1664C25.8407 50.2983 24.8624 49.534 24.7304 48.4592L20.3109 12.4653C20.2449 11.9279 20.6271 11.4387 21.1645 11.3727C21.1679 11.3723 21.1713 11.3719 21.1747 11.3715L23.5562 11.1044M25.4816 10.8878L27.7301 10.6355L25.4816 10.8878Z" fill="white"/>
  <path d="M53.5977 7.69412C53.5039 7.01017 52.8735 6.53173 52.1895 6.62551C51.5055 6.71928 51.0271 7.34976 51.1209 8.03372L53.5977 7.69412ZM56.919 41.1202L58.1597 40.9678C58.159 40.962 58.1583 40.9562 58.1575 40.9504L56.919 41.1202ZM57.3287 44.4566L56.088 44.6089L57.3287 44.4566ZM55.6215 46.6417L55.7738 47.8824L55.6215 46.6417ZM26.9155 50.1664L27.0678 51.4071L26.9155 50.1664ZM24.7304 48.4592L25.9711 48.3068L24.7304 48.4592ZM21.1747 11.3715L21.314 12.6137L21.1747 11.3715ZM23.6956 12.3466C24.3816 12.2696 24.8754 11.6511 24.7984 10.965C24.7215 10.279 24.1029 9.7852 23.4169 9.86215L23.6956 12.3466ZM25.3422 9.64556C24.6562 9.72252 24.1624 10.3411 24.2393 11.0271C24.3163 11.7132 24.9348 12.2069 25.6209 12.13L25.3422 9.64556ZM27.8694 11.8777C28.5555 11.8008 29.0493 11.1822 28.9723 10.4962C28.8954 9.81014 28.2768 9.31637 27.5908 9.39333L27.8694 11.8777ZM51.1209 8.03372L55.6806 41.29L58.1575 40.9504L53.5977 7.69412L51.1209 8.03372ZM55.6784 41.2725L56.088 44.6089L58.5694 44.3043L58.1597 40.9678L55.6784 41.2725ZM56.088 44.6089C56.1359 44.9986 55.8588 45.3532 55.4692 45.401L55.7738 47.8824C57.5339 47.6663 58.7855 46.0643 58.5694 44.3043L56.088 44.6089ZM55.4692 45.401L26.7632 48.9257L27.0678 51.4071L55.7738 47.8824L55.4692 45.401ZM26.7632 48.9257C26.3735 48.9735 26.0189 48.6965 25.9711 48.3068L23.4897 48.6115C23.7058 50.3715 25.3078 51.6232 27.0678 51.4071L26.7632 48.9257ZM25.9711 48.3068L21.5516 12.3129L19.0702 12.6176L23.4897 48.6115L25.9711 48.3068ZM21.5516 12.3129C21.5697 12.4607 21.4646 12.5952 21.3168 12.6134L21.0122 10.132C19.7895 10.2821 18.9201 11.395 19.0702 12.6176L21.5516 12.3129ZM21.3168 12.6134C21.3159 12.6135 21.315 12.6136 21.314 12.6137L21.0353 10.1293C21.0276 10.1302 21.0199 10.1311 21.0122 10.132L21.3168 12.6134ZM21.314 12.6137L23.6956 12.3466L23.4169 9.86215L21.0353 10.1293L21.314 12.6137ZM25.6209 12.13L27.8694 11.8777L27.5908 9.39333L25.3422 9.64556L25.6209 12.13Z" fill="#004E9B"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M51.1172 9.95489L55.2445 40.094L55.6157 43.1176C55.7353 44.0917 55.052 44.9771 54.0894 45.0953L28.3826 48.2517C27.42 48.3699 26.5428 47.6761 26.4232 46.702L22.5523 15.1765C22.4177 14.0802 23.1973 13.0823 24.2937 12.9477L26.4619 12.6814" fill="#F3FAFF"/>
  <path d="M30.5684 5C30.5684 3.48121 31.7996 2.25 33.3184 2.25H52.7666C53.4956 2.25 54.1949 2.5395 54.7105 3.05485L60.1054 8.44642C60.6215 8.9622 60.9115 9.66193 60.9115 10.3916V38.1765C60.9115 39.6953 59.6803 40.9265 58.1615 40.9265H33.3184C31.7996 40.9265 30.5684 39.6953 30.5684 38.1765V5Z" fill="white" stroke="#004E9B" stroke-width="2.5"/>
  <path d="M53.8281 2.17578V7.86067C53.8281 8.67285 54.4865 9.33126 55.2987 9.33126H59.1877" stroke="#004E9B" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M34.873 34.332H47.6181M34.873 9.33203H47.6181H34.873ZM34.873 15.2144H55.9515H34.873ZM34.873 21.5869H55.9515H34.873ZM34.873 27.9595H55.9515H34.873Z" stroke="#106BC4" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>`

})
export class SvgEmptyDocumentComponent { }