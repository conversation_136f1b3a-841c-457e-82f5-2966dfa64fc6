import {
  ChangeDetectionStrategy,
  Component,
  Input,
  Output,
  EventEmitter,
  TemplateRef,
} from '@angular/core';
import { FlattenedCell } from '../../cells/cells.model';
import { FlattenedRow, RowComponent } from '../rows.model';
import { ActionableComponents } from '../../cells/cell-component-registry';

@Component({
  selector: 'tr[default-row]',
  template: `
    <td
      class="se-table__cell"
      [ngStyle]="getNgStyle(cell, row)"
      *ngFor="let cell of row.cells; trackBy: trackById"
      (click)="onCellClick($event, cell)"
    >
      <div class="se-table__cell__mobile">
        <header
          class="se-table__cell__mobile__header"
          [innerHTML]="cell.column.header | translate | safeHtml"
        ></header>
        <ng-container cellTemplate [cell]="cell" [row]="row"> </ng-container>
      </div>
    </td>
  `,
  styleUrls: ['../styles/default-row.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DefaultRowComponent implements RowComponent {
  @Input() row!: FlattenedRow;
  @Output() rowClick: EventEmitter<FlattenedRow> =
    new EventEmitter<FlattenedRow>();

  onCellClick(event: MouseEvent, cell: FlattenedCell): void {
    if (
      !ActionableComponents.includes(
        cell.cellConfig.cellComponentName ?? 'defaultCellComponent'
      )
    ) {
      this.rowClick.emit(this.row);
    }
  }

  trackById(index: number, cell: FlattenedCell): string {
    return cell.id;
  }

  getNgStyle(cell: FlattenedCell, row: FlattenedRow): { [klass: string]: any } {
    return {
      'text-align': cell.cellConfig.align ?? 'left',
      'background-color': row.rowConfig.background ?? '',
      ...row.rowConfig['ngStyle'],
    };
  }

  getTooltip = (
    cell: FlattenedCell
  ): string | TemplateRef<HTMLElement> | undefined =>
    cell.cellConfig.tooltip ? cell.cellConfig.tooltipText ?? cell.value : '';
}
