import { Component, Inject, Input, LOCALE_ID, OnInit } from '@angular/core';
import { FormControl, FormGroupDirective } from '@angular/forms';
import { BehaviorSubject } from 'rxjs';

import { formatNumber } from '@angular/common';
import type { Nullable } from '../../models';
import { ControlErrorMsg } from './form-control-error.model';

@Component({
  selector: 'se-error-message',
  template: `
    <div class="se-error-container"
      *ngIf="
        control &&
        control.invalid && control.errors &&
        (control.dirty || control.touched || isFormSubmitted())
      "
    >
      <div *ngFor="let error of translations$ | async" class="error-message text-xs">
        <div class="error-message__icon d-flex align-self-start">
          <ng-icon class="d-flex align-self-center" name="matCancel"></ng-icon>
        </div>
        <span [innerHTML]="error.translation | translate: error.translateParams"></span>
      </div>
    </div>
  `,
  styleUrls: ['./form-control-error.component.scss'],
})
export class ErrorMessageComponent implements OnInit {

  @Input() control!: FormControl;
  @Input() currency: Nullable<
    Partial<{
      isCurrency: Nullable<boolean>;
      symbol: Nullable<string>;
      decimals: Nullable<number>;
    }>
  >;

  translations$: BehaviorSubject<ControlErrorMsg[]> = new BehaviorSubject<ControlErrorMsg[]>([]);

  constructor(
    private fgDirective: FormGroupDirective,
    @Inject(LOCALE_ID) private locale: string
  ) {}

  ngOnInit(): void {
    this.control.statusChanges.subscribe(() => this.getErrorMessage());
    this.control.updateValueAndValidity();
  }

  getErrorMessage(): void {
    let translations: ControlErrorMsg[] = [];

    for (const errorKey in this.control.errors) {
      if (errorKey in this.control.errors) {
        const translationMsg = this.getSpecificErrorMessage(errorKey);

        if (!translations.find(translation => translation.translation === translationMsg.translation)) {
          translations.push(translationMsg);
        }
      }
    };

    this.translations$.next(translations);
  }

  isFormSubmitted(): boolean {
    return this.fgDirective.submitted;
  }

  private getSpecificErrorMessage(errorKey: string): {translation: string, translateParams: { [key: string]: string }} {
    const errorMessages: { [key: string]: string } = {
      required: 'SE_COMPONENTS.VALIDATIONS_ERRORS.required',
      email: 'SE_COMPONENTS.VALIDATIONS_ERRORS.email',
      minlength: 'SE_COMPONENTS.VALIDATIONS_ERRORS.minlength',
      maxlength: 'SE_COMPONENTS.VALIDATIONS_ERRORS.maxlength',
      min: 'SE_COMPONENTS.VALIDATIONS_ERRORS.min',
      max: 'SE_COMPONENTS.VALIDATIONS_ERRORS.max',
      pattern: 'SE_COMPONENTS.VALIDATIONS_ERRORS.pattern',
    };

    const errorParams: { [key: string]: {[key: string]: string} } = {
      minlength: {
        minlength: this.control.errors?.['minlength']?.['requiredLength']
      },
      maxlength: {
        maxlength: this.control.errors?.['maxlength']?.['requiredLength']
      },
      min: {
        min: this.formatNumber(this.control.errors?.['min']?.['min'])
      },
      max: {
        max: this.formatNumber(this.control.errors?.['max']?.['max'])
      },
    }

    const translation =
      this.control.errors?.[errorKey]?.['translation'] ||
      this.control.errors?.[errorKey]?.['translationByKey']?.[errorKey] ||
      errorMessages[errorKey] || 'SE_COMPONENTS.VALIDATIONS_ERRORS.pattern';

    const translateParams =
      this.control.errors?.[errorKey]?.['translateParams'] ||
      this.control.errors?.[errorKey]?.['translationByKeyParams']?.[errorKey] ||
      errorParams[errorKey] || {}

    return {translation:translation, translateParams:translateParams}
  }

  private formatNumber(value: number): string {
    if (!this.currency?.isCurrency || !this.currency?.symbol) {
      return String(value);
    }

    const decimals = String(this.currency.decimals ?? 0);
    const formattedNumber = formatNumber(
      value,
      this.locale,
      `1.${decimals}-${decimals}`
    );

    return `${formattedNumber} ${this.currency.symbol}`;
  }
}
