$padding-card: 16px;

// RESPONSIVE
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

.selective-card {
  font-family: var(--font-primary);
  padding: $padding-card;
  border-radius: 4px;
  border: 1px solid var(--color-gray-400);
  background: var(--color-white);
  display: flex;
  justify-content: space-between;
  gap: 8px;

  &.selected {
    border:2px solid var(--color-blue-500);
    background: var(--color-blue-200);
  }

  &--clickable{
    cursor: pointer;

    &:hover, &:focus{
      border: 2px solid var(--color-blue-500);
      background: var(--color-blue-200);
      padding: $padding-card - 1px;
    }
  }

  &__left {
    flex-grow: 2;

    .header {
      margin-bottom: 8px;

      .title-input {
        display: inline-flex;
        align-items: center;
        border: none;
        gap: 8px;
        font-weight: var(--font-semibold);
        margin-bottom: 0;

        .info-icon {
          color: var(--color-orange-300);

          &:focus {
            outline: 2px solid var(--color-orange-700);
          }

          &:hover {
            color: var(--color-orange-700);
          }
        }
      }

      .header-title-container {
        display: flex;

        .header-item-container {
          display: flex;
          .header-item {
            display: flex;
            flex-direction: row;
            align-self: center;
            font-size: var(--text-md);
            font-weight: normal;
            margin-left: 8px;

            .header-item-title {
              margin-right: 8px
            }

            ng-icon {
              margin-right: 8px;
              align-self: center;
              font-size: var(--text-md);
            }
            &:first-child {
              margin-left: 16px;
            }
          }
          .no-title-item:first-child {
            margin-left: 0 !important;
          }
        }

      }
    }

    .content {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-start;

      &-column-container {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-auto-rows: auto;
        gap: 16px;
      }

      &-column {
        display: flex;
        flex-direction: column;
      }

      &-title {
        margin-bottom: 8px;
      }

      &-description {
        margin-right: 8px;
        color: var(--color-gray-600);
      }

      .clickable-title:hover {
        cursor: pointer;
        text-decoration: underline;
      }
    }
  }

  &__right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 8px;

    ::ng-deep se-button {
      button {
        white-space: nowrap;
      }
    }

    .icon-container {
      width: 56px;
      display: flex;
      flex-direction: row;
      align-self: flex-end;
      justify-content: end;
      gap: 8px;

      .icon-button {
        color: var(--color-blue-500);
        width: 24px;
        height: 24px;
        font-size: 24px;

        &:focus {
          outline: 2px solid var(--color-black);
        }

        &:hover {
          color: var(--color-blue-700);
        }
      }
    }
  }

  .icon-button {
    all: initial;
    width: 16px;
    height: 16px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 8px;

    &:focus-visible {
      outline: var(--color-black) auto 1px;
    }
  }
}

@include media-breakpoint-down(md) {
  .selective-card {
    flex-direction: column;
    .header-title-container {
      flex-direction: column;
      .header-item-container {
        flex-direction: column;
        .header-item {
          align-self: baseline !important;
          margin-left:0 !important;
          .header-item-title {
            margin: 8px 0;
          }
          ng-icon {
            margin-right: 8px;
          }
        }
      }
    }
  }
}