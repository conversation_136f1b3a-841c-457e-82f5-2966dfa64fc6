import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'se-link',
  template: ` <a
    [href]="href"
    [target]="target"
    class="text-sm se-link"
    [ngClass]="[
      size,
      linkTheme,
      disabled ? 'disabled' : '',
      iconPosition,
      dashed ? 'dashed' : ''
    ]"
    [tabindex]="disabled ? '-1' : '0'"
    (click)="onClickEvent($event)"
    (keydown)="onKeydownEvent($event)"
    [attr.aria-label]="ariaLabel"
    [pTooltipAccessible]="tooltipText"
  >
    <ng-content></ng-content>
    <ng-icon
      *ngIf="iconName"
      aria-hidden="true"
      class="link-icon"
      [size]="iconSize ?? '20px'"
      tool
      [name]="iconName"
    >
    </ng-icon>
  </a>`,
})
export class LinkComponent {
  @Input() href: string = '';
  @Input() disabled: boolean = false;
  @Input() dashed: boolean = false;
  @Input() iconName: string = '';
  @Input() iconPosition: 'left' | 'right' = 'right';
  @Input() iconSize?: string = '20px';
  @Input() target: '_blank' | '_self' | '_parent' | '_top' = '_blank';
  @Input() size: 'regular' | 'semibold' = 'regular';
  @Input() linkTheme: 'primary' | 'secondary' | 'onlyText' | 'inverse' =
    'secondary';
  @Input() ariaLabel = '';
  @Input() tooltipText = '';

  @Output() onClick: EventEmitter<Event> = new EventEmitter();

  onClickEvent(event: Event) {
    if (this.disabled || !this.href) {
      event.preventDefault();
      event.stopPropagation();
    }
    if (!this.disabled) {
      this.onClick.emit(event);
    }
  }

  onKeydownEvent(event: KeyboardEvent) {
    if (event.code === 'Enter' || event.code === 'Space') {
      this.onClickEvent(event);
    }
  }
}
