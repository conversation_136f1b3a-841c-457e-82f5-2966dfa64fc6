import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SelectiveCardComponent } from './selective-card.component';
import { SeButtonModule } from '../button/button.module';
import { SeSharedModule } from '../../shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { SeTagModule } from '../tag';

@NgModule({
  declarations: [SelectiveCardComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    SeButtonModule,
    SeTagModule,
    SeSharedModule,
    TranslateModule.forChild(),
  ],
  exports: [SelectiveCardComponent],
})
export class SeSelectiveCardModule {}
