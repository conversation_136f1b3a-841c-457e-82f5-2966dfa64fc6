import { NgModule } from '@angular/core';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { BehaviorSubject } from 'rxjs';

@NgModule({
  imports: [RouterModule],
  providers: [
    {
      provide: ActivatedRoute,
      useValue: {
        snapshot: {
          paramMap: {
            get(id: string) {
              return 'initial';
            },
          },
        },
        params: new BehaviorSubject({}),
      },
    },
  ],
})
export class MockRoutingModule {}
