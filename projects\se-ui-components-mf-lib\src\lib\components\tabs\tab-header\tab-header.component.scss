$border-non-selected: -2px;
$border-selected: -5px;

::ng-deep .tab-label {
  font-family: var(--font-primary);
  font-weight: var(--font-weight-regular);
  padding: 8px 32px 12px;
  cursor: pointer;
  box-sizing: border-box;
  box-shadow: inset 0 $border-non-selected 0 var(--color-gray-400);
  outline: none;
  height: 100%;
  align-content: space-evenly;
  display: flex;
  align-items: center;
  gap: 8px;

  > span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  se-badge .badge-container .circle {
    margin-right: 0 !important;
  }

  &:not(.active):not(.disabled) {
    background-color: var(--color-gray-200);
  }

  &:hover,
  &:focus {
    box-shadow: inset 0 $border-selected 0 var(--color-gray-400);
  }

  &.active {
    box-shadow: inset 0 $border-selected 0 var(--color-pink-500);
    font-weight: var(--font-semibold);

    &:is(:hover, :focus) {
      background-color: var(--color-pink-100);
    }
  }

  &.disabled {
    cursor: not-allowed;
    color: var(--color-gray-400);
    box-shadow: inset 0 $border-non-selected 0 var(--color-gray-300);

    &:hover,
    &:focus {
      box-shadow: inset 0 $border-non-selected 0 var(--color-gray-300);
      background-color: transparent;
    }
  }
}

// RESPONSIVE
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

@include media-breakpoint-down(md) {
  .tab-label {
    padding: 8px 8px 12px;
  }
}
