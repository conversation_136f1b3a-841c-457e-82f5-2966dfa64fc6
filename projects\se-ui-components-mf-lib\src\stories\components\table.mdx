export const Table = ({ headers, data, isCssVariables = false }) => (
  <table>
    <thead>
      <tr>
        {headers.map((header, index) => (
          <th key={index}>{header}</th>
        ))}
      </tr>
    </thead>
    <tbody>
      {data.map((item, index) => (
        <tr key={index}>
          {headers.map((header, cellIndex) => (
            <td key={cellIndex}>
              {isCssVariables &&
              header.toLowerCase() === "value" &&
              item[header.toLowerCase()].startsWith("--")
                ? getComputedStyle(document.documentElement).getPropertyValue(
                    item[header.toLowerCase()]
                  )
                : item[header.toLowerCase()]}
            </td>
          ))}
        </tr>
      ))}
    </tbody>
  </table>
);
