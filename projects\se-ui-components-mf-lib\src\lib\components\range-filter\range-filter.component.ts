import { formatCurrency } from '@angular/common';
import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  forwardRef,
  HostListener,
  Inject,
  Input,
  LOCALE_ID,
  Output,
  ViewChild,
} from '@angular/core';
import {
  ControlValueAccessor,
  FormControl,
  FormGroup,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { Nullable } from '../../models';
import { generateUniqueId } from '../../shared/utils/generate-unique-id/generate-unique-id';
import { SeButton } from '../button';
import { SeInput } from '../input/input.model';
import { RangeInterface } from './range-filter.model';
import { NgIcon } from '@ng-icons/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'se-range-filter',
  templateUrl: './range-filter.component.html',
  styleUrls: ['./range-filter.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RangeFilterComponent),
      multi: true,
    },
  ],
})
/**
 * Component to filter amounts within a specified range.
 * Implements ControlValueAccessor to integrate with Angular forms.
 */
export class RangeFilterComponent
  implements ControlValueAccessor, AfterViewInit
{
  @ViewChild('ngIcon', {static:false}) set ngIcon(value: NgIcon) {
    if(value) {
      const ngIconSvg = ((value?.['elementRef'] as ElementRef).nativeElement?.firstElementChild as SVGElement);
      
      ngIconSvg.id = `dropdown-ng-icon-svg-${this.id}`;
    }
  }

  /**
   * Placeholder text for the input fields.
   * @type {string}
   * @default 'SE_COMPONENTS.DROPDOWN.SHARES'
   */
  @Input() placeholder: string = 'SE_COMPONENTS.DROPDOWN.SHARES';

  /**
   * ID for the component.
   * @type {Nullable<string>}
   */
  @Input() id: Nullable<string> = 'range_filter_' + generateUniqueId();

  /**
   * Reference to the "from" input field.
   * @type {Nullable<SeInput>}
   */
  @Input() fromInput: Nullable<SeInput>;

  /**
   * Reference to the "to" input field.
   * @type {Nullable<SeInput>}
   */
  @Input() toInput: Nullable<SeInput>;

  /**
   * Name of the form control.
   * @type {Nullable<string>}
   */
  @Input() formControlName: Nullable<string>;

  /**
   * Reference to the reset button.
   * @type {Nullable<SeButton>}
   */
  @Input() resetButton: Nullable<SeButton>;

  /**
   * Reference to the apply button.
   * @type {Nullable<SeButton>}
   */
  @Input() applyButton: Nullable<SeButton>;

  /**
   * Close filter on reset button click
   * @type {boolean}
   * @default false
   */
  @Input() closeFilterOnResetButton: boolean = false;

  /**
   * Height of the dropdown scroll area.
   * @type {string}
   * @default '460px'
   */
  @Input() scrollHeight: string = '460px';
  
  @Input() showClear: boolean = false;

  /**
   * Event emitted when the apply button is clicked.
   * @type {EventEmitter<RangeInterface>}
   */
  @Output() onApply: EventEmitter<RangeInterface> =
    new EventEmitter<RangeInterface>();

  rangeForm: FormGroup = new FormGroup({
    from: new FormControl({ value: null, disabled: false }),
    to: new FormControl({ value: null, disabled: false }),
  });

  private defaultFormValue: Nullable<any>;

  dropdownOpen = false;

  value: RangeInterface = {
    from: this.from || null,
    to: this.to || null,
  };

  get from(): number {
    return this.rangeForm.get('from')?.value as number;
  }

  get to(): number {
    return this.rangeForm.get('to')?.value as number;
  }

  get badgeText(): string {
    let message = '';
    if (this.from && this.to) {
      if (this.toInput?.currencyMode && this.fromInput?.currencyMode) {
        message = `${
          formatCurrency(this.from, this.locale, '€', 'EUR', '1.2-2') || ''
        } - ${
          formatCurrency(this.to, this.locale, '€', 'EUR', '1.2-2') || ''
        }`;
      } else {
        message = `${this.from} - ${this.to}`;
      }

      return message;
    }
    
    if(this.from) {
      const label = this.translateService.instant('UI_COMPONENTS.RANGE_FILTER.LOWER_TAG_LABEL');
      if (this.fromInput?.currencyMode) {
        message = `${label} ${
          formatCurrency(this.from, this.locale, '€', 'EUR', '1.2-2') || ''
        }`;
      } else {
        message = `${label} ${this.from}`;
      }
    }

    if(this.to) {
      const label = this.translateService.instant('UI_COMPONENTS.RANGE_FILTER.TOP_TAG_LABEL');
      if (this.toInput?.currencyMode) {
        message = `${label} ${
          formatCurrency(this.to, this.locale, '€', 'EUR', '1.2-2') || ''
        }`;
      } else {
        message = `${label} ${this.to}`;
      }
    }

    return message;
  }

  constructor(
    @Inject(LOCALE_ID) private locale: string,
    private translateService: TranslateService
  ) {}

  private onChange: (value: any) => void = () => {};

  private onTouched: () => void = () => {};

  ngAfterViewInit(): void {
    this.defaultFormValue = this.rangeForm.value;
  }

  setDefaultFormValue(): void {
    this.rangeForm.patchValue(this.defaultFormValue);
    this.writeValue(this.defaultFormValue);
    this.onApply.emit(this.value);
  }

  toggleDropdown(): void {
    this.dropdownOpen = !this.dropdownOpen;
    if (this.dropdownOpen && this.applyButton) {
      this.defaultFormValue = this.rangeForm.value;
    }
    this.onTouched();
  }

  getIcon(): string {
    return this.dropdownOpen ? 'matExpandLessOutline' : 'matExpandMoreOutline';
  }

  handleKeyboardEvent(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      this.toggleDropdown();
    }
  }

  writeValue(value: RangeInterface): void {
    if (value) {
      this.rangeForm.markAsTouched();
      this.rangeForm.markAsDirty();
      this.rangeForm.patchValue(value);
    }
    this.value = value || { from: null, to: null };
    this.onChange(this.value);
    this.onTouched();
  }

  registerOnChange(fn: (value: any) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  emitApply(): void {
    this.writeValue(this.rangeForm.value);
    this.onApply.emit(this.value);
    this.closeDropdown();
  }

  resetForm(): void {
    this.rangeForm.reset();
    this.writeValue(this.rangeForm.value);
    this.onApply.emit(this.value);
    if(this.closeFilterOnResetButton) this.dropdownOpen = false;
  }

  closeDropdown(): void {
    this.dropdownOpen = false;
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent): void {
    const targetElement = event.target as HTMLElement;
    event.stopPropagation();
    if (
      this.dropdownOpen &&
      !targetElement.closest(`.se-dropdown-filter#${this.id}`) &&
      !targetElement.closest(`svg#dropdown-ng-icon-svg-${this.id}`)
    ) {
      if (this.applyButton) {
        this.setDefaultFormValue();
      }
      this.closeDropdown();
    }
  }
}
