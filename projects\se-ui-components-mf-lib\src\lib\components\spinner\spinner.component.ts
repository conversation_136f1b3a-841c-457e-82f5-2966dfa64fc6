import { Component, Input } from '@angular/core';

@Component({
  selector: 'se-spinner',
  template: `
    <p-blockUI [blocked]="blocked">
      <div class="spinner-container">
        <div class="lds-ring">
          <div class="ring-segment"></div>
          <div class="ring-segment"></div>
          <div class="ring-segment"></div>
          <div class="ring-segment"></div>
        </div>
      </div>
      <div class="text-container">
        <h3 class="spinner-title">{{ message | translate }}</h3>
        <p class="spinner-subtitle">{{ subtitle | translate }}</p>
      </div>
    </p-blockUI>
  `,
  styleUrls: ['./spinner.component.scss'],
})
export class SpinnerComponent {
  @Input() blocked: boolean = true;
  @Input() message: string = 'UI_COMPONENTS.SPINNER.LOADING';
  @Input() subtitle = '';
}
