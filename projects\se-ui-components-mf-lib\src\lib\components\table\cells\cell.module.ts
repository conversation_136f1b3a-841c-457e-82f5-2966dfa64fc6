import { CommonModule, DatePipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { SeSharedModule } from '../../../shared/shared.module';
import { SeButtonDropdownModule } from '../../button-dropdown';
import { SeButtonModule } from '../../button/button.module';
import { SeCheckboxModule } from '../../checkbox/checkbox.module';
import { SeDropdownModule } from '../../dropdown/dropdown.module';
import { SeInputModule } from '../../input/input.module';
import { SeModalModule } from '../../modal/modal.module';
import { SeTagModule } from '../../tag/tag.module';
import { CellTemplateDirective } from './cell-template.directive';
import { ActionsCellComponent } from './templates/actions-cell-template.component';
import { ButtonCellComponent } from './templates/button-cell-template.component';
import { CheckboxCellComponent } from './templates/checkbox-cell-template.component';
import { CurrencyCellComponent } from './templates/currency-cell-template.component';
import { DateCellComponent } from './templates/date-cell-template.component';
import { DefaultCellComponent } from './templates/default-cell-template.component';
import { DownloadCellComponent } from './templates/download-cell-template.component';
import { DropdownButtonCellComponent } from './templates/dropdown-button-cell-template';
import { DropdownCellComponent } from './templates/dropdown-cell-template.component';
import { EditCellComponent } from './templates/edit-cell-template.component';
import { IconActionsCellComponent } from './templates/icons-action-cell-template.component';
import { InputCellComponent } from './templates/input-cell-template.component';
import { StyledTextCellComponent } from './templates/styled-text-cell-template.component';
import { TagCellComponent } from './templates/tag-cell-template.component';
import { NumberCellComponent } from './templates/number-cell-template.component';
import { CollapsibleCellComponent } from './templates/collapsible-cell-template.component';
import { LinkCellComponent } from './templates/link-cell-template.component';
import { SeLinkModule } from '../../link/link.module';
import { IconCellComponent } from './templates/icon-cell-template.component';
import { SeparatedTooltipCellComponent } from './templates/separated-tooltip-cell-template.component';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    FormsModule,
    ReactiveFormsModule,
    SeSharedModule,
    SeButtonModule,
    SeDropdownModule,
    SeInputModule,
    SeTagModule,
    SeModalModule,
    SeCheckboxModule,
    SeButtonDropdownModule,
    SeLinkModule,
  ],
  declarations: [
    CellTemplateDirective,
    DefaultCellComponent,
    EditCellComponent,
    DownloadCellComponent,
    DropdownCellComponent,
    ButtonCellComponent,
    DateCellComponent,
    InputCellComponent,
    CurrencyCellComponent,
    StyledTextCellComponent,
    TagCellComponent,
    CheckboxCellComponent,
    ActionsCellComponent,
    DropdownButtonCellComponent,
    IconActionsCellComponent,
    NumberCellComponent,
    CollapsibleCellComponent,
    LinkCellComponent,
    IconCellComponent,
    SeparatedTooltipCellComponent,
  ],
  exports: [CellTemplateDirective],
  providers: [DatePipe],
})
export class CellsModule {}
