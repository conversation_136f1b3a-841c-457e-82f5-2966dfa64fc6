import {
  AbstractControl,
  FormControl,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';

/**
 * IBAN: ISO7064
 * @description Calculates the MOD 97 10 of the passed IBAN as specified in ISO7064.
 * @param iban
 * @returns {number}
 */
const mod97 = (iban: string): number => {
  let remainder: string = iban;
  let block: string;

  while (remainder.length > 2) {
    block = remainder.slice(0, 9);
    remainder = (parseInt(block, 10) % 97) + remainder.slice(block.length);
  }

  return parseInt(remainder, 10) % 97;
};

/**
 * IBAN: ISO13616
 * @description Prepare an IBAN for mod 97 computation by moving the first 4 chars to the end and transforming the letters to
 * numbers (A = 10, B = 11, ..., Z = 35), as specified in ISO13616.
 * @param {string} iban the IBAN
 * @returns {string} the prepared IBAN
 */
const iso13616 = (iban: string): string => {
  var A = 'A'.charCodeAt(0);
  var Z = 'Z'.charCodeAt(0);
  iban = iban.toUpperCase();
  iban = iban.substr(4) + iban.substr(0, 4);

  return iban
    .split('')
    .map(function (n) {
      var code = n.charCodeAt(0);
      if (code >= A && code <= Z) {
        // A = 10, B = 11, ... Z = 35
        return code - A + 10;
      } else {
        return n;
      }
    })
    .join('');
};

/**
 * Validator: IBAN (Internation Bank Account Number)
 * @description Validates if a field value has a valid IBAN format
 * @returns {ValidatorFn} Returns a validaton error
 */
export const validatorIban: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  const ibanES = /^[ES]{2}[0-9]{22}$/;

  if (c.value && c.value.toString().trim() !== '') {
    // Format IBAN
    let IBAN: string = c.value;
    IBAN = IBAN.toUpperCase();
    IBAN = IBAN.trim();
    IBAN = IBAN.replace(/\s/g, '');

    // Validate format
    if (!ibanES.test(IBAN)) {
      // Structural value -> KO
      return { ibanPattern: 'SE_COMPONENTS.VALIDATIONS_ERRORS.ibanPattern' };
    } else {
      // Structural value -> OK

      /* Validate logic value */

      /**
       * Alternative to ISO13616. Simpler solutions than the iso13616() function?
       * Put into an array:
       * [0] -> Country code ('ES')
       * [1] -> Check digits (dígito de control)
       * [2] -> The rest of the digits
       */
      // const code: string[] = IBAN.match(/^([A-Z]{2})(\d{2})([A-Z\d]+)$/);
      // digits = (code[3] + code[1] + code[2]).replace(/[A-Z]/g, function (letter) {
      // 	return `${letter.charCodeAt(0) - 55}`;
      // });

      // ISO13616
      const digits: string = iso13616(IBAN);

      // ISO7064
      if (mod97(digits) !== 1) {
        return { ibanValue: 'SE_COMPONENTS.VALIDATIONS_ERRORS.ibanValue' };
      }

      return null;
    }
  }
  return null;
};

/**
 * Validator: IBAN allowed bank
 * @description Validates if an IBAN beongs to one of the allowed banking entitites.
 * @returns {ValidatorFn} Returns a validaton error
 */
export const validatorIbanAllowedBanks: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  const ibanES = /^[ES]{2}[0-9]{22}$/;

  /**
   * Allowed banks
   * [0] -> Caixabank = 2100
   * [1] -> BBVA = 0182
   * [2] -> Santander = 0049
   * [3] -> Banc Sabadell = 0081
   * [4] -> Ibercaja = 2085
   */
  const allowedBanks: string[] = ['2100', '0182', '0049', '0081', '2085'];

  if (c.value && c.value.toString().trim() !== '') {
    // Format IBAN
    let IBAN: string = c.value;
    IBAN = IBAN.toUpperCase();
    IBAN = IBAN.trim();
    IBAN = IBAN.replace(/\s/g, '');

    // Validate format
    if (!ibanES.test(IBAN)) {
      // Structural value -> KO
      return { ibanPattern: 'SE_COMPONENTS.VALIDATIONS_ERRORS.ibanPattern' };
    } else {
      // Structural value -> OK

      // Validate allowed banks
      const validBank: boolean =
        allowedBanks.filter((bank) => bank === IBAN.slice(4, 8))?.length > 0
          ? true
          : false;
      if (!validBank) {
        return {
          ibanPattern: 'SE_COMPONENTS.VALIDATIONS_ERRORS.ibanValue',
        };
      }

      return null;
    }
  }

  return null;
};

// Merge external form & internal form errors
const setErrors = (
  externalFormErrors: ValidationErrors,
  internalFormErrors: ValidationErrors
): ValidationErrors | null => {
  let errors: ValidationErrors | null;

  if (externalFormErrors && !internalFormErrors) {
    errors = externalFormErrors;
  } else if (!externalFormErrors && internalFormErrors) {
    errors = internalFormErrors;
  } else if (externalFormErrors && internalFormErrors) {
    errors = { ...externalFormErrors, ...internalFormErrors };
  } else {
    errors = null;
  }

  return errors;
};

/**
 * Validator: IBAN internal form
 * @description Validates the IBAN component internal form.
 * @returns {ValidatorFn} Returns a validaton error
 */
type AnyFormControl = AbstractControl<any, any>;

export const validatorIbanInternalForm = (
  isDesktop: boolean,
  externalFormValidations: ValidatorFn[]
): ValidatorFn => {
  return ((control: AbstractControl<any, any>): ValidationErrors | null => {
    const internalForm = control as FormGroup;

    // Set an IBAN value with the internalForm inputs values
    let IBAN: string = '';
    if (
      isDesktop &&
      (internalForm.get('controlCode')?.value ||
        internalForm.get('entity')?.value ||
        internalForm.get('office')?.value ||
        internalForm.get('controlDigitAccountNumber')?.value ||
        internalForm.get('accountNumber1')?.value ||
        internalForm.get('accountNumber2')?.value)
    ) {
      IBAN = `${internalForm.get('country')?.value}${
        internalForm.get('controlCode')?.value
      }${internalForm.get('entity')?.value}${
        internalForm.get('office')?.value
      }${internalForm.get('controlDigitAccountNumber')?.value}${
        internalForm.get('accountNumber1')?.value
      }${internalForm.get('accountNumber2')?.value}`;
      IBAN = IBAN.toUpperCase();
      IBAN = IBAN.trim();
      IBAN = IBAN.replace(/\s/g, '');
    } else if (!isDesktop && internalForm.get('ibanMobile')?.value) {
      IBAN = `${internalForm.get('country')?.value}${
        internalForm.get('ibanMobile')?.value
      }`;
      IBAN = IBAN.toUpperCase();
      IBAN = IBAN.trim();
      IBAN = IBAN.replace(/\s/g, '');
    }

    // Set a temporary form control with the internalForm values and the externalForm validations
    const tmpFormControl = new FormControl(IBAN, externalFormValidations);

    /**
     * Set internal form errors:
     * Get the externalForm errors and add them into all of the internalForm controls.
     * The "new FormControl(...)" is used in order to get the internalForm error without any previous externalForm error
     */
    if (isDesktop) {
      [
        'controlCode',
        'entity',
        'office',
        'controlDigitAccountNumber',
        'accountNumber1',
        'accountNumber2',
      ].forEach((key) => {
        const control = internalForm.get(key) as AnyFormControl;
        control.setErrors(
          setErrors(
            tmpFormControl.errors as ValidationErrors,
            new FormControl(control.value, control.validator)
              .errors as ValidationErrors
          )
        );
      });
    } else {
      internalForm.get('ibanMobile')?.setErrors(tmpFormControl.errors);
    }

    return null;
  }) as ValidatorFn;
};
