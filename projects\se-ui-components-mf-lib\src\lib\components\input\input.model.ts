import { TemplateRef } from '@angular/core';

export interface SeInput {
  ariaLabel?: string | undefined;
  currencyMode?: boolean;
  currencySymbol?: string;
  decimals?: number;
  disabled?: boolean;
  formControlName?: string;
  icon?: string;
  id?: string;
  inline?: boolean;
  label?: string;
  labelAlign?: 'left' | 'center' | 'right';
  max?: number | undefined;
  maxLength?: number | undefined;
  min?: number | undefined;
  placeholder?: string;
  readonly?: boolean;
  showClear?: boolean;
  showValidation?: boolean;
  tooltip?: boolean;
  tooltipText?: string | TemplateRef<HTMLElement> | undefined;
  type?: string;
}

export enum SeInputModeEnum {
  NONE = 'none',
  TEXT = 'text',
  DECIMAL = 'decimal',
  NUMERIC = 'numeric',
  TEL = 'tel',
  SEARCH = 'search',
  EMAIL = 'email',
  URL = 'url',
}

export type SeInputMode = `${SeInputModeEnum}`;
