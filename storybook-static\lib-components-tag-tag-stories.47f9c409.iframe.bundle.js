(self.webpackChunkse_ui_components_mf_lib=self.webpackChunkse_ui_components_mf_lib||[]).push([[125],{"./projects/se-ui-components-mf-lib/src/lib/components/tag/tag.stories.ts":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Tag:()=>Tag,default:()=>tag_stories});var _class,tslib_es6=__webpack_require__("./node_modules/tslib/tslib.es6.mjs"),tag_component=__webpack_require__("./projects/se-ui-components-mf-lib/src/lib/components/tag/tag.component.ts.css?ngResource!=!./node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=CiAgICAgIC50YWcgewogICAgICAgIC8qIFRleHQgKi8KICAgICAgICBmb250LWZhbWlseTogT3BlbiBTYW5zOwogICAgICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICAgICBmb250LXN0eWxlOiBub3JtYWw7CiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsKICAgICAgICBsaW5lLWhlaWdodDogMTZweDsKICAgICAgICBjb2xvcjogdmFyKC0tdGV4dG9zLWludmVyc28sICNmZmYpOwoKICAgICAgICAvKiBCbG9jayAqLwogICAgICAgIGRpc3BsYXk6IGlubGluZS1mbGV4OwogICAgICAgIHBhZGRpbmc6IDRweCA4cHg7CiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICBnYXA6IDRweDsKICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgIH0KCiAgICAgIC50YWcgLmNsb3NlLWJ1dHRvbiB7CiAgICAgICAgY29sb3I6IHZhcigtLXRleHRvcy1pbnZlcnNvLCAjZmZmKTsKICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgICAgICB3aWR0aDogMTZweDsKICAgICAgICBoZWlnaHQ6IDE2cHg7CiAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7CiAgICAgICAgYm9yZGVyLXJhZGl1czogOTlweDsKICAgICAgICBib3JkZXI6IG5vbmU7CiAgICAgICAgY3Vyc29yOiBwb2ludGVyOwoKICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLXRleHRvcy1pbnZlcnNvLCAjZmZmKTsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC50YWcucHJpbWFyeSB7CiAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tZGVncmFkYWRvcy1henVsLTUwMCwgIzEwNmJjNCk7CiAgICAgICAgY29sb3I6IHZhcigtLXRleHRvcy1pbnZlcnNvLCAjZmZmKTsKICAgICAgfQoKICAgICAgLnRhZy5vbmx5VGV4dCB7CiAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7CiAgICAgICAgY29sb3I6IHZhcigtLXRleHRvcy1kYXJrLCAjMzMzKTsKCiAgICAgICAgLmNsb3NlLWJ1dHRvbiB7CiAgICAgICAgICBjb2xvcjogdmFyKC0tdGV4dG9zLWRhcmssICMzMzMpOwoKICAgICAgICAgICY6aG92ZXIgewogICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS10ZXh0b3MtZGFyaywgIzMzMyk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICAudGFnLmRhbmdlciB7CiAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tZXJyb3ItNTAwLCAjZDAwMjFiKTsKICAgICAgICBjb2xvcjogdmFyKC0tdGV4dG9zLWludmVyc28sICNmZmYpOwogICAgICB9CiAgICA%3D!./projects/se-ui-components-mf-lib/src/lib/components/tag/tag.component.ts"),tag_component_default=__webpack_require__.n(tag_component),core=__webpack_require__("./node_modules/@angular/core/fesm2022/core.mjs");let TagComponent=((_class=class TagComponent{constructor(){this.tagTheme="primary",this.close=new core.EventEmitter}}).propDecorators={tagTheme:[{type:core.Input}],close:[{type:core.Output}]},_class);TagComponent=(0,tslib_es6.gn)([(0,core.Component)({selector:"se-tag",template:'\n    <div class="tag" [ngClass]="[tagTheme]">\n      <span><ng-content></ng-content></span>\n      <button class="close-button"><i>X</i></button>\n    </div>\n  ',styles:[tag_component_default()]})],TagComponent);const tag_stories={title:"Components/Tag",component:TagComponent,tags:["autodocs"],args:{tagTheme:"primary"},argTypes:{tagTheme:{description:"Changes the tag theme style.",options:["primary","secondary","onlyText","danger"],control:{type:"select"},table:{defaultValue:{summary:"-"}}},close:{action:"close",description:"Emitted when the close button is clicked.",table:{type:{summary:"EventEmitter<void>"}}}},render:args=>({props:args,template:'\n    <div style="max-width: 850px">\n      <se-tag\n        [tagTheme]="tagTheme"\n        (close)="close()"\n      >\n        Tag Content\n      </se-tag>\n    </div>\n    '})},Tag={args:{tagTheme:"primary"}}},"./node_modules/css-loader/dist/runtime/api.js":module=>{"use strict";module.exports=function(cssWithMappingToString){var list=[];return list.toString=function toString(){return this.map((function(item){var content="",needLayer=void 0!==item[5];return item[4]&&(content+="@supports (".concat(item[4],") {")),item[2]&&(content+="@media ".concat(item[2]," {")),needLayer&&(content+="@layer".concat(item[5].length>0?" ".concat(item[5]):""," {")),content+=cssWithMappingToString(item),needLayer&&(content+="}"),item[2]&&(content+="}"),item[4]&&(content+="}"),content})).join("")},list.i=function i(modules,media,dedupe,supports,layer){"string"==typeof modules&&(modules=[[null,modules,void 0]]);var alreadyImportedModules={};if(dedupe)for(var k=0;k<this.length;k++){var id=this[k][0];null!=id&&(alreadyImportedModules[id]=!0)}for(var _k=0;_k<modules.length;_k++){var item=[].concat(modules[_k]);dedupe&&alreadyImportedModules[item[0]]||(void 0!==layer&&(void 0===item[5]||(item[1]="@layer".concat(item[5].length>0?" ".concat(item[5]):""," {").concat(item[1],"}")),item[5]=layer),media&&(item[2]?(item[1]="@media ".concat(item[2]," {").concat(item[1],"}"),item[2]=media):item[2]=media),supports&&(item[4]?(item[1]="@supports (".concat(item[4],") {").concat(item[1],"}"),item[4]=supports):item[4]="".concat(supports)),list.push(item))}},list}},"./node_modules/css-loader/dist/runtime/noSourceMaps.js":module=>{"use strict";module.exports=function(i){return i[1]}},"./projects/se-ui-components-mf-lib/src/lib/components/tag/tag.component.ts.css?ngResource!=!./node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=CiAgICAgIC50YWcgewogICAgICAgIC8qIFRleHQgKi8KICAgICAgICBmb250LWZhbWlseTogT3BlbiBTYW5zOwogICAgICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICAgICBmb250LXN0eWxlOiBub3JtYWw7CiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsKICAgICAgICBsaW5lLWhlaWdodDogMTZweDsKICAgICAgICBjb2xvcjogdmFyKC0tdGV4dG9zLWludmVyc28sICNmZmYpOwoKICAgICAgICAvKiBCbG9jayAqLwogICAgICAgIGRpc3BsYXk6IGlubGluZS1mbGV4OwogICAgICAgIHBhZGRpbmc6IDRweCA4cHg7CiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICBnYXA6IDRweDsKICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgIH0KCiAgICAgIC50YWcgLmNsb3NlLWJ1dHRvbiB7CiAgICAgICAgY29sb3I6IHZhcigtLXRleHRvcy1pbnZlcnNvLCAjZmZmKTsKICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgICAgICB3aWR0aDogMTZweDsKICAgICAgICBoZWlnaHQ6IDE2cHg7CiAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7CiAgICAgICAgYm9yZGVyLXJhZGl1czogOTlweDsKICAgICAgICBib3JkZXI6IG5vbmU7CiAgICAgICAgY3Vyc29yOiBwb2ludGVyOwoKICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLXRleHRvcy1pbnZlcnNvLCAjZmZmKTsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC50YWcucHJpbWFyeSB7CiAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tZGVncmFkYWRvcy1henVsLTUwMCwgIzEwNmJjNCk7CiAgICAgICAgY29sb3I6IHZhcigtLXRleHRvcy1pbnZlcnNvLCAjZmZmKTsKICAgICAgfQoKICAgICAgLnRhZy5vbmx5VGV4dCB7CiAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7CiAgICAgICAgY29sb3I6IHZhcigtLXRleHRvcy1kYXJrLCAjMzMzKTsKCiAgICAgICAgLmNsb3NlLWJ1dHRvbiB7CiAgICAgICAgICBjb2xvcjogdmFyKC0tdGV4dG9zLWRhcmssICMzMzMpOwoKICAgICAgICAgICY6aG92ZXIgewogICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS10ZXh0b3MtZGFyaywgIzMzMyk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICAudGFnLmRhbmdlciB7CiAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tZXJyb3ItNTAwLCAjZDAwMjFiKTsKICAgICAgICBjb2xvcjogdmFyKC0tdGV4dG9zLWludmVyc28sICNmZmYpOwogICAgICB9CiAgICA%3D!./projects/se-ui-components-mf-lib/src/lib/components/tag/tag.component.ts":(module,__unused_webpack_exports,__webpack_require__)=>{var ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___=__webpack_require__("./node_modules/css-loader/dist/runtime/noSourceMaps.js"),___CSS_LOADER_EXPORT___=__webpack_require__("./node_modules/css-loader/dist/runtime/api.js")(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);___CSS_LOADER_EXPORT___.push([module.id,"\n      .tag {\n        /* Text */\n        font-family: Open Sans;\n        font-size: 12px;\n        font-style: normal;\n        font-weight: 400;\n        line-height: 16px;\n        color: var(--textos-inverso, #fff);\n\n        /* Block */\n        display: inline-flex;\n        padding: 4px 8px;\n        align-items: center;\n        gap: 4px;\n        border-radius: 4px;\n      }\n\n      .tag .close-button {\n        color: var(--textos-inverso, #fff);\n        text-align: center;\n        display: flex;\n        justify-content: center;\n        width: 16px;\n        height: 16px;\n        background: transparent;\n        border-radius: 99px;\n        border: none;\n        cursor: pointer;\n\n        &:hover {\n          border: 1px solid var(--textos-inverso, #fff);\n        }\n      }\n\n      .tag.primary {\n        background: var(--degradados-azul-500, #106bc4);\n        color: var(--textos-inverso, #fff);\n      }\n\n      .tag.onlyText {\n        background: transparent;\n        color: var(--textos-dark, #333);\n\n        .close-button {\n          color: var(--textos-dark, #333);\n\n          &:hover {\n            border: 1px solid var(--textos-dark, #333);\n          }\n        }\n      }\n\n      .tag.danger {\n        background: var(--error-500, #d0021b);\n        color: var(--textos-inverso, #fff);\n      }\n    ",""]),module.exports=___CSS_LOADER_EXPORT___.toString()}}]);