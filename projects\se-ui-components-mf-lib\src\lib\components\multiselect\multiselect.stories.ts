import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { MultiselectComponent } from './multiselect.component';
import { SeMultiselectModule } from './multiselect.module';

const meta: Meta<MultiselectComponent> = {
  title: 'Components/Multiselect',
  component: MultiselectComponent,
  decorators: [
    moduleMetadata({
      imports: [SeMultiselectModule, BrowserAnimationsModule, ReactiveFormsModule],
    }),
  ],
  tags: ['autodocs'],
  args: {
    options: [
      { label: 'New York', id: 'NY' },
      { label: 'Rome', id: 'RM' },
      { label: 'London', id: 'LDN' },
      { label: 'Istanbul', id: 'IST' },
      { label: 'Paris', id: 'PRS' }
    ],
    disabled: false,
    ariaLabel: 'Select items',
  },
  argTypes: {
    options: {
      description: 'Array of options',
      table: {
        defaultValue: { summary: [] },
      },
    },
  },
  render: (args) => ({
    props: {
      ...args,
      form: new FormGroup({
        value: new FormControl({ value: [], disabled: args.disabled }),
      }),
    },
    template: `
    <form [formGroup]="form">
      <se-multiselect
        [options]="options"
        [formControl]="form.controls['value']"
        [display]="'comma'"
        [showHeader]="true"
        [ariaLabel]="ariaLabel"
        placeholder="Select items">
      </se-multiselect>
    </form>
    `,
  }),
};

export default meta;
type Story = StoryObj<MultiselectComponent>;

export const Default: Story = {
  args: {
    options: [
      { label: 'New York', id: 'NY' },
      { label: 'Rome', id: 'RM' },
      { label: 'London', id: 'LDN' },
      { label: 'Istanbul', id: 'IST' },
      { label: 'Paris', id: 'PRS' }
    ],
  },
};

export const NoItems: Story = {
  args: {
    options: [],
  },
};
