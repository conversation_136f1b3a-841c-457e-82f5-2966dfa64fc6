.se-confirmation-container,
.se-alert-container {
	display: flex;
	align-items: stretch;
	box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.15);
	border-radius: 5px;
	background: var(--color-white);
	overflow: auto;

	.se-confirmation-status {
		border-top-left-radius: 5px;
		border-bottom-left-radius: 5px;
		position: relative;
		-webkit-clip-path: polygon(0 0, 100% 0, 80% 100%, 0 100%);
		clip-path: polygon(0 0, 100% 0, 80% 100%, 0 100%);
		-webkit-box-align: start;
		background-size: cover;
		background-repeat: no-repeat;
		background-position: 50% 50%;
		padding: 15px;

		&.confirmation-info {
			// background-image: url(/assets/img/vitralls/vitrall_blue.svg)
			// Uri encoded
			background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='1920px' height='1009px' viewBox='0 0 1920 1009' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3C!-- Generator: sketchtool 48.2 (47327) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3E192DD842-893D-47BB-AB3C-616991B5452F%3C/title%3E%3Cdesc%3ECreated with sketchtool.%3C/desc%3E%3Cdefs%3E%3Cpolygon id='path-1' points='0 0 1920 0 1920 1008.5 0 1008.5'%3E%3C/polygon%3E%3C/defs%3E%3Cg id='VITRALL' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' transform='translate(0.000000, -3367.000000)'%3E%3Cg id='Vitrall-Blau' transform='translate(0.000000, 3367.000000)'%3E%3Cmask id='mask-2' fill='white'%3E%3Cuse xlink:href='%23path-1'%3E%3C/use%3E%3C/mask%3E%3Cuse id='Mask' fill='%230092DC' xlink:href='%23path-1'%3E%3C/use%3E%3Cg id='Group' mask='url(%23mask-2)'%3E%3Cg transform='translate(-498.000000, -649.000000)'%3E%3Cg id='2-focus' stroke-width='1' fill='none' transform='translate(280.000000, 0.000000)'%3E%3Cpolygon id='Triangle-Copy-2' fill-opacity='0.38' fill='%23004E9B' points='1598 1709 1285 545 1911 545'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-5' fill-opacity='0.3' fill='%23004E9B' transform='translate(361.500000, 1127.000000) rotate(180.000000) translate(-361.500000, -1127.000000) ' points='361.5 1799 0 455 723 455'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-3' fill-opacity='0.3' fill='%23004E9B' transform='translate(1780.380898, 624.924727) rotate(-60.000000) translate(-1780.380898, -624.924727) ' points='1780.3809 1271.98503 1432.36042 -22.1355732 2128.40138 -22.1355732'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-4' fill-opacity='0.3' fill='%23004E9B' transform='translate(1980.979956, 1648.599704) rotate(240.000000) translate(-1980.979956, -1648.599704) ' points='1980.97996 2679.46473 1426.53064 617.734681 2535.42927 617.734681'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-4' fill-opacity='0.3' fill='%23004E9B' transform='translate(1175.552234, 1158.126073) rotate(150.000000) translate(-1175.552234, -1158.126073) ' points='1175.55223 2030.5225 706.33503 285.729642 1644.76944 285.729642'%3E%3C/polygon%3E%3C/g%3E%3Cpolygon id='Triangle-Copy-3' fill-opacity='0.38' fill='%23004E9B' transform='translate(1029.797297, 1101.961946) rotate(30.000000) translate(-1029.797297, -1101.961946) ' points='1029.7973 1960.11622 568.240215 243.807668 1491.35438 243.807668'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-6' fill-opacity='0.38' fill='%23004E9B' transform='translate(2548.797297, 1056.961946) rotate(-30.000000) translate(-2548.797297, -1056.961946) ' points='2548.7973 1915.11622 2087.24022 198.807668 3010.35438 198.807668'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-3' fill-opacity='0.38' fill='%23004E9B' transform='translate(1379.395013, 1413.163450) rotate(120.000000) translate(-1379.395013, -1413.163450) ' points='1395.68084 2646.18225 756.256189 180.144654 2002.53384 224.011277'%3E%3C/polygon%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
		}

		&.confirmation-warning {
			// background-image: url(/assets/img/vitralls/vitrall_orange.svg)
			// Uri encoded
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='1853' height='974' viewBox='0 0 1853 974'%3E%3Cdefs%3E%3Cpath id='a' d='M0 0h1853v974H0z'/%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cmask id='b' fill='%23fff'%3E%3Cuse xlink:href='%23a'/%3E%3C/mask%3E%3Cuse fill='%23FF8D00' fill-opacity='.5' xlink:href='%23a'/%3E%3Cg fill='%23FF8D00' fill-opacity='.25' mask='url(%23b)'%3E%3Cpath d='M1332.317 1023.557L1030.264-99.957h604.105zM139.063-186.826L487.92 1110.426h-697.713z'/%3E%3Cpath d='M2049.09 289.465L799.623-44.177 1135.47-626zM2563.433 467.753L1107.899 1926.227 572.842 999.296z'/%3E%3Cpath d='M503.702-237.395L1737.728 994.634 953.444 1447.53z'/%3E%3Cpath d='M99.71 1154.966l442.4-1657.418L1313.59-56.95z'/%3E%3Cpath d='M2393.723 1111.532L1179.843-100.384l771.48-445.503z'/%3E%3Cpath d='M-187.186 156.558l2369.487 655.634-638.006 1020.597z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E%0A");
		}
		&.confirmation-danger {
			// background-image: url(/assets/img/vitralls/vitrall_red.svg)
			// Uri encoded
			background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='1920px' height='1009px' viewBox='0 0 1920 1009' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3C!-- Generator: sketchtool 48.2 (47327) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3E3DD664F1-1485-4B43-8D65-55BAF5F2F7E4%3C/title%3E%3Cdesc%3ECreated with sketchtool.%3C/desc%3E%3Cdefs%3E%3Cpolygon id='path-1' points='0 0 1920 0 1920 1008.5 0 1008.5'%3E%3C/polygon%3E%3C/defs%3E%3Cg id='VITRALL' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' transform='translate(0.000000, -6227.000000)'%3E%3Cg id='Vitrall-Vermell' transform='translate(0.000000, 6227.000000)'%3E%3Cmask id='mask-2' fill='white'%3E%3Cuse xlink:href='%23path-1'%3E%3C/use%3E%3C/mask%3E%3Cuse id='Mask' fill-opacity='0.5' fill='%23FF0032' xlink:href='%23path-1'%3E%3C/use%3E%3Cg id='Group' mask='url(%23mask-2)' fill-opacity='0.25'%3E%3Cg transform='translate(-498.000000, -649.000000)'%3E%3Cg id='2-focus' stroke-width='1' fill='none' transform='translate(280.000000, 0.000000)'%3E%3Cpolygon id='Triangle-Copy-2' fill='%23FF0032' points='1598 1709 1285 545 1911 545'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-5' fill='%23FF0032' transform='translate(361.500000, 1127.000000) rotate(180.000000) translate(-361.500000, -1127.000000) ' points='361.5 1799 0 455 723 455'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-3' fill='%23FF0032' transform='translate(1780.380898, 624.924727) rotate(-60.000000) translate(-1780.380898, -624.924727) ' points='1780.3809 1271.98503 1432.36042 -22.1355732 2128.40138 -22.1355732'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-4' fill='%23FF0032' transform='translate(1980.979956, 1648.599704) rotate(240.000000) translate(-1980.979956, -1648.599704) ' points='1980.97996 2679.46473 1426.53064 617.734681 2535.42927 617.734681'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-4' fill='%23FF0032' transform='translate(1175.552234, 1158.126073) rotate(150.000000) translate(-1175.552234, -1158.126073) ' points='1175.55223 2030.5225 706.33503 285.729642 1644.76944 285.729642'%3E%3C/polygon%3E%3C/g%3E%3Cpolygon id='Triangle-Copy-3' fill='%23FF0032' transform='translate(1029.797297, 1101.961946) rotate(30.000000) translate(-1029.797297, -1101.961946) ' points='1029.7973 1960.11622 568.240215 243.807668 1491.35438 243.807668'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-6' fill='%23FF0032' transform='translate(2548.797297, 1056.961946) rotate(-30.000000) translate(-2548.797297, -1056.961946) ' points='2548.7973 1915.11622 2087.24022 198.807668 3010.35438 198.807668'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-3' fill='%23FF0032' transform='translate(1379.395013, 1413.163450) rotate(120.000000) translate(-1379.395013, -1413.163450) ' points='1395.68084 2646.18225 756.256189 180.144654 2002.53384 224.011277'%3E%3C/polygon%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
		}
		&.confirmation-success {
			// background-image: url(/assets/img/vitralls/vitrall_green.svg)
			// Uri encoded
			background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='1920px' height='1009px' viewBox='0 0 1920 1009' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3C!-- Generator: sketchtool 48.2 (47327) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3E55F7F219-BF1D-4AA3-9FFC-6D2DD7FE74F5%3C/title%3E%3Cdesc%3ECreated with sketchtool.%3C/desc%3E%3Cdefs%3E%3Cpolygon id='path-1' points='0 0 1920 0 1920 1008.5 0 1008.5'%3E%3C/polygon%3E%3C/defs%3E%3Cg id='VITRALL' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' transform='translate(0.000000, -4805.000000)'%3E%3Cg id='Vitrall-Verd' transform='translate(0.000000, 4805.000000)'%3E%3Cmask id='mask-2' fill='white'%3E%3Cuse xlink:href='%23path-1'%3E%3C/use%3E%3C/mask%3E%3Cuse id='Mask' fill-opacity='0.5' fill='%23018935' xlink:href='%23path-1'%3E%3C/use%3E%3Cg id='Group' mask='url(%23mask-2)' fill-opacity='0.25'%3E%3Cg transform='translate(-498.000000, -649.000000)'%3E%3Cg id='2-focus' stroke-width='1' fill='none' transform='translate(280.000000, 0.000000)'%3E%3Cpolygon id='Triangle-Copy-2' fill='%23018935' points='1598 1709 1285 545 1911 545'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-5' fill='%23018935' transform='translate(361.500000, 1127.000000) rotate(180.000000) translate(-361.500000, -1127.000000) ' points='361.5 1799 0 455 723 455'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-3' fill='%23018935' transform='translate(1780.380898, 624.924727) rotate(-60.000000) translate(-1780.380898, -624.924727) ' points='1780.3809 1271.98503 1432.36042 -22.1355732 2128.40138 -22.1355732'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-4' fill='%23018935' transform='translate(1980.979956, 1648.599704) rotate(240.000000) translate(-1980.979956, -1648.599704) ' points='1980.97996 2679.46473 1426.53064 617.734681 2535.42927 617.734681'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-4' fill='%23018935' transform='translate(1175.552234, 1158.126073) rotate(150.000000) translate(-1175.552234, -1158.126073) ' points='1175.55223 2030.5225 706.33503 285.729642 1644.76944 285.729642'%3E%3C/polygon%3E%3C/g%3E%3Cpolygon id='Triangle-Copy-3' fill='%23018935' transform='translate(1029.797297, 1101.961946) rotate(30.000000) translate(-1029.797297, -1101.961946) ' points='1029.7973 1960.11622 568.240215 243.807668 1491.35438 243.807668'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-6' fill='%23018935' transform='translate(2548.797297, 1056.961946) rotate(-30.000000) translate(-2548.797297, -1056.961946) ' points='2548.7973 1915.11622 2087.24022 198.807668 3010.35438 198.807668'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-3' fill='%23018935' transform='translate(1379.395013, 1413.163450) rotate(120.000000) translate(-1379.395013, -1413.163450) ' points='1395.68084 2646.18225 756.256189 180.144654 2002.53384 224.011277'%3E%3C/polygon%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
		}

		.se-confirmation-icon {
			color: var(--color-white);
			font-size: 40px;
		}
	}

	.se-alert-status-2 {
		border-top-left-radius: 5px;
		border-bottom-left-radius: 5px;
		position: relative;
		width: 80px;
		padding: 15px;
		&.alert-danger {
			background-color: #ffeff1;
			.se-alert-icon {
				color: #ff0032;
				font-size: 40px;
			}
		}
		&.alert-success {
			background-color: #eefef4;
			.se-alert-icon {
				color: #018935;
				font-size: 40px;
			}
		}
	}

	.se-alert-body-2 {
		padding: 20px;
		width: 100%;
		position: relative;

		&.alert-danger {
			background-color: #ffeff1;
		}
		&.alert-success {
			background-color: #eefef4;
		}
		.se-alert-title {
			font-family: var(--font-primary);
			font-weight: var(--font-bold);
			white-space: pre-wrap;
			display: inline-block;
		}
	}

	.se-confirmation-body {
		padding: 20px;
		width: 100%;
		position: relative;
		background-color: var(--color-white);

		.se-confirmation-close {
			font-size: 16px;
			position: absolute;
			top: 20px;
			right: 20px;
			cursor: pointer;
			color: var(--color-gray-700);
			border: none;
			background-color: transparent;
			padding: 0;

			&:hover {
				color: var(--color-gray-700);
			}
		}

		.se-confirmation-title {
			color: var(--color-gray-700);
			font-family: var(--font-primary);
			font-weight: var(--font-bold);
			font-size: 20px;
			margin-bottom: 15px;
			white-space: pre-wrap;
			display: inline-block;
		}

		.se-confirmation-button {
			padding-top: unset;
			padding-bottom: 15px;
			margin-left: 15px;
			color: var(--color-blue-500);
			text-decoration: underline var(--color-blue-500);
		}

		.se-confirmation-subtitle {
			color: var(--color-gray-500);
			font-family: var(--font-primary);
			font-size: 16px;
			margin-bottom: 15px;
			white-space: pre-wrap;
		}

		.se-confirmation-tracking-id {
			font-size: 12px;
		}

		a {
			color: var(--color-blue-500);
			font-family: var(--font-primary);
			font-weight: var(--font-semibold);
		}
	}

	&.se-confirmation-visible-overflow {
		overflow: visible;
	}

	.se-body-padding-custom {
		padding: 40px;
	}

	/* As a modal */
	.modal-content {
		border-radius: 5px;

		.se-confirmation-container {
			margin-bottom: 0px;
			overflow: visible !important;

			b,
			strong {
				font-weight: bolder !important;
			}

			.se-confirmation-body {
				border-radius: 5px;
			}
		}
	}

	.se-confirmation-custom-csv {
		.se-confirmation-body {
			padding: 40px;
			.p-button-label {
				font-size: 14px;
				padding: 0px;
			}
			.se-confirmation-body {
				padding: 0px;
			}
		}
	}

	// RESPONSIVE
	@import "bootstrap/scss/functions";
	@import "bootstrap/scss/variables";
	@import "bootstrap/scss/mixins/breakpoints";
	// @media (max-width: 767px) {
	@include media-breakpoint-down(md) {
		.se-confirmation-container {
			flex-direction: column;

			.se-confirmation-status {
				border-top-left-radius: 5px;
				border-top-right-radius: 5px;
				border-bottom-left-radius: 0px;
				width: 100%;
				height: 60px;
				clip-path: polygon(0% 0%, 100% 0%, 100% 80%, 0% 100%);

				.se-confirmation-icon {
					font-size: 30px;
				}

				.se-confirmation-body {
					.se-confirmation-title {
						font-size: 16px;
					}
					.se-confirmation-subtitle {
						font-size: 14px;
					}
				}
			}

			//se-confirmation-custom-csv*************
			.se-confirmation-custom-csv {
				flex-direction: row;

				.se-confirmation-container {
					width: auto;
					flex-direction: row;
				}

				.se-confirmation-body {
					padding: 40px 16px;
				}

				.se-confirmation-title {
					font-size: 16px;
				}

				.se-confirmation-close {
					top: 15px;
					right: 10px;
				}

				.se-confirmation-status {
					width: 92px;
					height: auto;
					padding: 15px;
					clip-path: polygon(0 0, 100% 0, 80% 100%, 0 100%);
				}

				.se-button-container button {
					width: auto;
				}

				se-button:not(:last-of-type) button {
					margin-right: 0px;
				}
			}
		}

		//BUTON CLOSE RESPONSIVE
		.se-confirmation-container:not(.se-confirmation-custom-csv) {
			.se-confirmation-close-status.se-confirmation-close {
				top: -46px;
				right: 15px;
				color: white;
			}
		}
	}

	// @media (min-width: 769px) {
	@include media-breakpoint-up(md) {
		.se-confirmation-title {
			padding-right: 1.5rem;
		}
	}
}
