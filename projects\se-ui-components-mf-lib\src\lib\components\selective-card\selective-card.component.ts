import {
  Component,
  EventEmitter,
  forwardRef,
  Input,
  Output,
  TemplateRef,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { Router } from '@angular/router';
import { Nullable } from '../../models';
import {
  HeaderItem,
  ItemSelectableCard,
  SelectiveCardMode,
  SelectiveCardTag,
} from './selective-card.model';

@Component({
  selector: 'se-selective-card',
  template: `
    <div
      class="selective-card"
      [ngClass]="{ selected: selected, 'selective-card--clickable': clickable }"
      [attr.tabindex]="clickable ? '0' : null"
      [attr.role]="clickable ? 'button' : null"
      (click)="handleCardClick()"
      (keydown)="handleKeyDown($event)"
    >
      <div class="selective-card__left">
        <div class="header">
          <div class="header-title-container">
            <div class="title-input text-lg">
              <ng-icon *ngIf="titleIcon" [name]="titleIcon"></ng-icon>
              <span *ngIf="headerTitle">{{ headerTitle }}</span>
              <button
                *ngIf="enableTitleInfo"
                (click)="onRequestTitleInfo($event)"
                class="icon-button"
                [attr.aria-label]="titleInfoTooltipAriaLabel"
                [attr.title]="titleInfoTooltipTitle"
                [pTooltipAccessible]="titleInfoTooltipText"
                [tooltipEvent]="titleInfoTooltipEvent"
                [positionLeft]="titleInfoTooltipPositionLeft"
                [positionTop]="titleInfoTooltipPositionTop"
                [tabindex]="
                  titleInfoTooltipText &&
                  (titleInfoTooltipEvent === 'focus' ||
                    titleInfoTooltipEvent === 'hover-focus')
                    ? 0
                    : -1
                "
              >
                <ng-icon class="info-icon text-md" name="matInfo"></ng-icon>
              </button>
            </div>
            <div *ngIf="headerItems" class="header-item-container">
              <div
                *ngFor="let item of headerItems"
                class="header-item"
                [ngClass]="{ 'no-title-item': !headerTitle }"
              >
                <ng-icon
                  class="mr-2"
                  *ngIf="item.icon"
                  [name]="item.icon"
                ></ng-icon>
                <div class="header-item-title">{{ item.title }}</div>
              </div>
            </div>
            <se-tag
              class="ml-2"
              *ngIf="stateTag"
              [tagTheme]="stateTag.tagTheme"
              [closable]="false"
              >{{ stateTag.title }}</se-tag
            >
          </div>
        </div>
        <div class="content">
          <div class="row w-100">
            <ng-container *ngFor="let item of items">
              <div
                *ngIf="!item.separatorTitle; else separatorBlock"
                [ngClass]="[
                  item.columnSize ? 'col-md-' + item.columnSize : 'col-md-4',
                  item.align === 'right' ? 'align-items-end' : ''
                ]"
                class="content-column my-2 col-12 col-sm-6"
              >
                <span
                  class="content-title text-md semi-bold"
                  [ngClass]="{ 'clickable-title': item.clickable }"
                  (click)="handleItemClick($event, item)"
                >
                  {{ item.title | translate }}
                </span>
                <span
                  class="content-description text-sm"
                  [attr.translate]="item.translateNoAttr ? 'no' : undefined"
                >
                  {{ item.description | translate }}
                </span>
              </div>
              <ng-template #separatorBlock>
                <div class="col-12 my-2">
                  <span class="text-base semi-bold">
                    {{ item.title | translate }}
                  </span>
                </div>
              </ng-template>
            </ng-container>
          </div>
        </div>
      </div>
      <div class="selective-card__right">
        <se-button
          *ngIf="showSelectableButton"
          [icon]="selectableButtonIcon"
          [iconPosition]="'left'"
          (onClick)="handleToggleButton($event)"
          btnTheme="secondary"
          size="small"
        >
          {{ toggleButtonText }}
        </se-button>
        <ng-container *ngIf="customActions">
          <ng-container *ngTemplateOutlet="customActions"></ng-container>
        </ng-container>
        <div class="icon-container">
          <button
            *ngIf="enableRequestInfo"
            class="icon-button"
            [attr.aria-label]="requestInfoButtonAriaLabel"
            [attr.title]="requestInfoButtonTitle"
            (click)="onRequestInfo($event)"
          >
            <ng-icon name="matInfoOutline"></ng-icon>
          </button>
          <button
            *ngIf="enableDownload"
            class="icon-button"
            [attr.aria-label]="downloadButtonAriaLabel"
            [attr.title]="downloadButtonTitle"
            (click)="onDownload($event)"
          >
            <ng-icon name="matFileDownloadOutline"></ng-icon>
          </button>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./selective-card.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SelectiveCardComponent),
      multi: true,
    },
  ],
})
export class SelectiveCardComponent implements ControlValueAccessor {
  selected: boolean = false;

  @Input() headerTitle!: string;
  @Input() headerItems: HeaderItem[] | undefined;
  @Input() items: ItemSelectableCard[] = [];
  @Input() toggleButtonText = '';
  @Input() enableRequestInfo = true;
  @Input() requestInfoButtonAriaLabel = 'Icon button';
  @Input() requestInfoButtonTitle = '';
  @Input() enableDownload = true;
  @Input() downloadButtonAriaLabel = 'Icon button';
  @Input() downloadButtonTitle = '';
  @Input() enableTitleInfo = false;
  @Input() enableSelected = true;
  @Input() titleInfoTooltipText = '';
  @Input() titleInfoTooltipAriaLabel = 'Icon button';
  @Input() titleInfoTooltipTitle = '';
  @Input() titleInfoTooltipEvent: 'hover' | 'focus' | 'hover-focus' = 'hover';
  @Input() titleInfoTooltipPositionLeft: number = 0;
  @Input() titleInfoTooltipPositionTop: number = 0;
  @Input() selectiveCardMode: SelectiveCardMode = SelectiveCardMode.SELECTIVE;
  @Input() itemID: string = '';
  @Input() stateTag: SelectiveCardTag | undefined;
  @Input() showSelectableButton = true;
  @Input() customActions: Nullable<TemplateRef<any>>;
  @Input() titleIcon: string | undefined;
  @Input() selectableButtonIcon!: string;
  @Input() clickable: boolean = false;

  @Output() download = new EventEmitter<string>();
  @Output() requestInfo = new EventEmitter<void>();
  @Output() requestTitleInfo = new EventEmitter<void>();
  @Output() toggleButton = new EventEmitter<any>();
  @Output() onItemClick = new EventEmitter<ItemSelectableCard>();
  @Output() onCardClick = new EventEmitter<void>();

  constructor(private router: Router) {
    //empty
  }

  private onChange: (value: boolean) => void = () => {};
  private onTouched: () => void = () => {};

  writeValue(value: boolean): void {
    this.selected = value;
  }

  registerOnChange(fn: (value: boolean) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  handleToggleButton(event: Event) {
    if (this.selectiveCardMode == SelectiveCardMode.SELECTIVE) {
      this.toggleSelected(event);
    } else {
      this.onDownload(event);
    }
  }

  toggleSelected(event: Event) {
    event.stopPropagation();
    if (this.enableSelected) {
      this.selected = !this.selected;
      this.onChange(this.selected);
    }

    this.onTouched();
    this.toggleButton.emit();
  }

  onDownload(event: Event) {
    event.stopPropagation();
    this.download.emit(this.itemID);
  }

  onRequestInfo(event: Event) {
    event.stopPropagation();
    this.requestInfo.emit();
  }

  onRequestTitleInfo(event: Event) {
    event.stopPropagation();
    this.requestTitleInfo.emit();
  }

  handleItemClick(event: Event, item: ItemSelectableCard) {
    event.stopPropagation();
    if (item.clickable) {
      this.onItemClick.emit(item);
    } else {
      this.handleCardClick();
    }
  }

  handleCardClick() {
    if (this.clickable) {
      this.onCardClick.emit();
    }
  }

  handleKeyDown(event: KeyboardEvent) {
    if (this.clickable && (event.key === 'Enter' || event.key === ' ')) {
      this.handleCardClick();
    }
  }
}
