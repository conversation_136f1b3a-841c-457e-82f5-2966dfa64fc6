(self.webpackChunkse_ui_components_mf_lib=self.webpackChunkse_ui_components_mf_lib||[]).push([[595],{"./projects/se-ui-components-mf-lib/src/lib/components/badge/badge.stories.ts":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Badge:()=>Badge,default:()=>badge_stories});var _class,dist=__webpack_require__("./node_modules/@storybook/angular/dist/index.mjs"),tslib_es6=__webpack_require__("./node_modules/tslib/tslib.es6.mjs"),badge_component=__webpack_require__("./projects/se-ui-components-mf-lib/src/lib/components/badge/badge.component.ts.css?ngResource!=!./node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=CiAgICAgIC5iYWRnZS1jb250YWluZXIgewogICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICBmb250LWZhbWlseTogT3BlbiBTYW5zOwogICAgICB9CiAgICAgIC5jaXJjbGUgewogICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4OwoKICAgICAgICAvKiB0ZXh0ICovCiAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICAgIGZvbnQtc3R5bGU6IG5vcm1hbDsKICAgICAgICBmb250LXdlaWdodDogNjAwOwogICAgICAgIGxpbmUtaGVpZ2h0OiAxNnB4OwogICAgICB9CiAgICA%3D!./projects/se-ui-components-mf-lib/src/lib/components/badge/badge.component.ts"),badge_component_default=__webpack_require__.n(badge_component),core=__webpack_require__("./node_modules/@angular/core/fesm2022/core.mjs");let BadgeComponent=((_class=class BadgeComponent{constructor(){this.textColor="white",this.textInsideCircle="",this.badgeTheme="info"}get circleStyle(){let backgroundColor="";if(this.color)backgroundColor=this.color;else switch(this.badgeTheme){case"info":backgroundColor="var(--refuerzo-informativo-500, #004E9B)";break;case"success":backgroundColor="var(--refuerzo-positivo-500, #018935)";break;case"warning":backgroundColor="var(--advertencias-500, #FF8D00)";break;case"error":backgroundColor="var(--error-500, #D0021B)"}return{"background-color":backgroundColor,color:this.textColor,width:this.textInsideCircle?"16px":"8px",height:this.textInsideCircle?"16px":"8px"}}}).propDecorators={color:[{type:core.Input}],textColor:[{type:core.Input}],textInsideCircle:[{type:core.Input}],label:[{type:core.Input}],badgeTheme:[{type:core.Input}]},_class);BadgeComponent=(0,tslib_es6.gn)([(0,core.Component)({selector:"se-badge",template:'\n    <div class="badge-container">\n      <div class="circle" [ngStyle]="circleStyle">\n        {{ textInsideCircle }}\n      </div>\n      <ng-content></ng-content>\n      <span *ngIf="label">{{ label }}</span>\n    </div>\n  ',styles:[badge_component_default()]})],BadgeComponent);var common=__webpack_require__("./node_modules/@angular/common/fesm2022/common.mjs");let SeBadgeModule=class SeBadgeModule{};SeBadgeModule=(0,tslib_es6.gn)([(0,core.NgModule)({imports:[common.CommonModule],declarations:[BadgeComponent],exports:[BadgeComponent]})],SeBadgeModule);const badge_stories={title:"Components/Badge",component:BadgeComponent,decorators:[(0,dist.moduleMetadata)({imports:[SeBadgeModule]})],args:{badgeTheme:"info",textColor:"white",textInsideCircle:"5",label:"Notifications"},argTypes:{color:{description:"Color of the circle (overrides badgeTheme if set).",control:{type:"color"},table:{defaultValue:{summary:""}}},badgeTheme:{description:"Theme of the badge.",options:["info","success","warning","error"],control:{type:"select"},table:{defaultValue:{summary:"info"}}},textColor:{description:"Color of the text inside the circle.",control:{type:"color"},table:{defaultValue:{summary:"white"}}},textInsideCircle:{description:"Text inside the circle.",control:{type:"text"},table:{defaultValue:{summary:""}}},label:{description:"Label at the right of the circle.",control:{type:"text"},table:{defaultValue:{summary:""}}}},tags:["autodocs"],render:args=>({template:'\n      <se-badge\n        [color]="color"\n        [badgeTheme]="badgeTheme"\n        [textColor]="textColor"\n        [textInsideCircle]="textInsideCircle"\n        [label]="label">\n      </se-badge>\n    ',props:{...args}})},Badge={}},"./node_modules/@storybook/angular/dist/client/decorators.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.componentWrapperDecorator=exports.applicationConfig=exports.moduleMetadata=void 0;const ComputesTemplateFromComponent_1=__webpack_require__("./node_modules/@storybook/angular/dist/client/angular-beta/ComputesTemplateFromComponent.js"),NgComponentAnalyzer_1=__webpack_require__("./node_modules/@storybook/angular/dist/client/angular-beta/utils/NgComponentAnalyzer.js");exports.moduleMetadata=metadata=>storyFn=>{const story=storyFn(),storyMetadata=story.moduleMetadata||{};return metadata=metadata||{},{...story,moduleMetadata:{declarations:[...metadata.declarations||[],...storyMetadata.declarations||[]],entryComponents:[...metadata.entryComponents||[],...storyMetadata.entryComponents||[]],imports:[...metadata.imports||[],...storyMetadata.imports||[]],schemas:[...metadata.schemas||[],...storyMetadata.schemas||[]],providers:[...metadata.providers||[],...storyMetadata.providers||[]]}}},exports.applicationConfig=function applicationConfig(config){return storyFn=>{const story=storyFn(),storyConfig=story.applicationConfig;return{...story,applicationConfig:storyConfig||config?{...config,...storyConfig,providers:[...config?.providers||[],...storyConfig?.providers||[]]}:void 0}}};exports.componentWrapperDecorator=(element,props)=>(storyFn,storyContext)=>{const story=storyFn(),currentProps="function"==typeof props?props(storyContext):props,template=(0,NgComponentAnalyzer_1.isComponent)(element)?(0,ComputesTemplateFromComponent_1.computesTemplateFromComponent)(element,currentProps??{},story.template):element(story.template);return{...story,template,...currentProps||story.props?{props:{...currentProps,...story.props}}:{}}}},"./node_modules/@storybook/angular/dist/client/index.js":function(__unused_webpack_module,exports,__webpack_require__){"use strict";var __createBinding=this&&this.__createBinding||(Object.create?function(o,m,k,k2){void 0===k2&&(k2=k);var desc=Object.getOwnPropertyDescriptor(m,k);desc&&!("get"in desc?!m.__esModule:desc.writable||desc.configurable)||(desc={enumerable:!0,get:function(){return m[k]}}),Object.defineProperty(o,k2,desc)}:function(o,m,k,k2){void 0===k2&&(k2=k),o[k2]=m[k]}),__exportStar=this&&this.__exportStar||function(m,exports){for(var p in m)"default"===p||Object.prototype.hasOwnProperty.call(exports,p)||__createBinding(exports,m,p)};Object.defineProperty(exports,"__esModule",{value:!0}),exports.applicationConfig=exports.componentWrapperDecorator=exports.moduleMetadata=void 0,__webpack_require__("./node_modules/@storybook/angular/dist/client/globals.js"),__exportStar(__webpack_require__("./node_modules/@storybook/angular/dist/client/public-api.js"),exports),__exportStar(__webpack_require__("./node_modules/@storybook/angular/dist/client/public-types.js"),exports);var decorators_1=__webpack_require__("./node_modules/@storybook/angular/dist/client/decorators.js");Object.defineProperty(exports,"moduleMetadata",{enumerable:!0,get:function(){return decorators_1.moduleMetadata}}),Object.defineProperty(exports,"componentWrapperDecorator",{enumerable:!0,get:function(){return decorators_1.componentWrapperDecorator}}),Object.defineProperty(exports,"applicationConfig",{enumerable:!0,get:function(){return decorators_1.applicationConfig}})},"./node_modules/@storybook/angular/dist/client/public-api.js":function(__unused_webpack_module,exports,__webpack_require__){"use strict";var __createBinding=this&&this.__createBinding||(Object.create?function(o,m,k,k2){void 0===k2&&(k2=k);var desc=Object.getOwnPropertyDescriptor(m,k);desc&&!("get"in desc?!m.__esModule:desc.writable||desc.configurable)||(desc={enumerable:!0,get:function(){return m[k]}}),Object.defineProperty(o,k2,desc)}:function(o,m,k,k2){void 0===k2&&(k2=k),o[k2]=m[k]}),__exportStar=this&&this.__exportStar||function(m,exports){for(var p in m)"default"===p||Object.prototype.hasOwnProperty.call(exports,p)||__createBinding(exports,m,p)},__importDefault=this&&this.__importDefault||function(mod){return mod&&mod.__esModule?mod:{default:mod}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.raw=exports.forceReRender=exports.configure=exports.storiesOf=void 0;const core_client_1=__webpack_require__("@storybook/core-client"),render_1=__webpack_require__("./node_modules/@storybook/angular/dist/client/render.js"),decorateStory_1=__importDefault(__webpack_require__("./node_modules/@storybook/angular/dist/client/decorateStory.js"));__exportStar(__webpack_require__("./node_modules/@storybook/angular/dist/client/public-types.js"),exports);const api=(0,core_client_1.start)(render_1.renderToCanvas,{decorateStory:decorateStory_1.default,render:render_1.render});exports.storiesOf=(kind,m)=>api.clientApi.storiesOf(kind,m).addParameters({renderer:"angular"});exports.configure=(...args)=>api.configure("angular",...args),exports.forceReRender=api.forceReRender,exports.raw=api.clientApi.raw},"./node_modules/@storybook/angular/dist/client/public-types.js":(__unused_webpack_module,exports)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0})},"./node_modules/@storybook/angular/dist/index.mjs":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";var _client_index__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/@storybook/angular/dist/client/index.js");__webpack_require__.o(_client_index__WEBPACK_IMPORTED_MODULE_0__,"applicationConfig")&&__webpack_require__.d(__webpack_exports__,{applicationConfig:function(){return _client_index__WEBPACK_IMPORTED_MODULE_0__.applicationConfig}}),__webpack_require__.o(_client_index__WEBPACK_IMPORTED_MODULE_0__,"moduleMetadata")&&__webpack_require__.d(__webpack_exports__,{moduleMetadata:function(){return _client_index__WEBPACK_IMPORTED_MODULE_0__.moduleMetadata}})},"./node_modules/css-loader/dist/runtime/api.js":module=>{"use strict";module.exports=function(cssWithMappingToString){var list=[];return list.toString=function toString(){return this.map((function(item){var content="",needLayer=void 0!==item[5];return item[4]&&(content+="@supports (".concat(item[4],") {")),item[2]&&(content+="@media ".concat(item[2]," {")),needLayer&&(content+="@layer".concat(item[5].length>0?" ".concat(item[5]):""," {")),content+=cssWithMappingToString(item),needLayer&&(content+="}"),item[2]&&(content+="}"),item[4]&&(content+="}"),content})).join("")},list.i=function i(modules,media,dedupe,supports,layer){"string"==typeof modules&&(modules=[[null,modules,void 0]]);var alreadyImportedModules={};if(dedupe)for(var k=0;k<this.length;k++){var id=this[k][0];null!=id&&(alreadyImportedModules[id]=!0)}for(var _k=0;_k<modules.length;_k++){var item=[].concat(modules[_k]);dedupe&&alreadyImportedModules[item[0]]||(void 0!==layer&&(void 0===item[5]||(item[1]="@layer".concat(item[5].length>0?" ".concat(item[5]):""," {").concat(item[1],"}")),item[5]=layer),media&&(item[2]?(item[1]="@media ".concat(item[2]," {").concat(item[1],"}"),item[2]=media):item[2]=media),supports&&(item[4]?(item[1]="@supports (".concat(item[4],") {").concat(item[1],"}"),item[4]=supports):item[4]="".concat(supports)),list.push(item))}},list}},"./node_modules/css-loader/dist/runtime/noSourceMaps.js":module=>{"use strict";module.exports=function(i){return i[1]}},"./projects/se-ui-components-mf-lib/src/lib/components/badge/badge.component.ts.css?ngResource!=!./node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=CiAgICAgIC5iYWRnZS1jb250YWluZXIgewogICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICBmb250LWZhbWlseTogT3BlbiBTYW5zOwogICAgICB9CiAgICAgIC5jaXJjbGUgewogICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4OwoKICAgICAgICAvKiB0ZXh0ICovCiAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICAgIGZvbnQtc3R5bGU6IG5vcm1hbDsKICAgICAgICBmb250LXdlaWdodDogNjAwOwogICAgICAgIGxpbmUtaGVpZ2h0OiAxNnB4OwogICAgICB9CiAgICA%3D!./projects/se-ui-components-mf-lib/src/lib/components/badge/badge.component.ts":(module,__unused_webpack_exports,__webpack_require__)=>{var ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___=__webpack_require__("./node_modules/css-loader/dist/runtime/noSourceMaps.js"),___CSS_LOADER_EXPORT___=__webpack_require__("./node_modules/css-loader/dist/runtime/api.js")(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);___CSS_LOADER_EXPORT___.push([module.id,"\n      .badge-container {\n        display: flex;\n        align-items: center;\n        font-family: Open Sans;\n      }\n      .circle {\n        border-radius: 50%;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        margin-right: 10px;\n\n        /* text */\n        font-size: 12px;\n        font-style: normal;\n        font-weight: 600;\n        line-height: 16px;\n      }\n    ",""]),module.exports=___CSS_LOADER_EXPORT___.toString()}}]);