{"version": 3, "sources": ["../../../node_modules/@storybook/addon-links/dist/chunk-JT3VIYBO.mjs", "global-externals:@storybook/manager-api", "../../../node_modules/@storybook/addon-links/dist/manager.mjs"], "sourcesContent": ["var ADDON_ID=\"storybook/links\",PARAM_KEY=\"links\",constants_default={NAVIGATE:`${ADDON_ID}/navigate`,REQUEST:`${ADDON_ID}/request`,RECEIVE:`${ADDON_ID}/receive`};\n\nexport { ADDON_ID, PARAM_KEY, constants_default };\n", "export default __STORYBOOKAPI__;\nconst { ActiveTabs, Consumer, ManagerContext, Provider, addons, combineParameters, controlOrMetaKey, controlOrMetaSymbol, eventMatchesShortcut, eventToShortcut, isMacLike, isShortcutTaken, keyToSymbol, merge, mockChannel, optionOrAltSymbol, shortcutMatchesShortcut, shortcutToHumanString, types, useAddonState, useArgTypes, useArgs, useChannel, useGlobalTypes, useGlobals, useParameter, useSharedState, useStoryPrepared, useStorybookApi, useStorybookState } = __STORYBOOKAPI__;\nexport { ActiveTabs, Consumer, ManagerContext, Provider, addons, combineParameters, controlOrMetaKey, controlOrMetaSymbol, eventMatchesShortcut, eventToShortcut, isMacLike, isShortcutTaken, keyToSymbol, merge, mockChannel, optionOrAltSymbol, shortcutMatchesShortcut, shortcutToHumanString, types, useAddonState, useArgTypes, useArgs, useChannel, useGlobalTypes, useGlobals, useParameter, useSharedState, useStoryPrepared, useStorybookApi, useStorybookState };", "import { ADDON_ID, constants_default } from './chunk-JT3VIYBO.mjs';\nimport { addons } from '@storybook/manager-api';\n\naddons.register(ADDON_ID,api=>{addons.getChannel().on(constants_default.REQUEST,({kind,name})=>{let id=api.storyId(kind,name);api.emit(constants_default.RECEIVE,id);});});\n"], "mappings": ";AAAA,IAAIA,EAAS,kBAAb,IAAiDC,EAAkB,CAAC,SAAS,GAAGC,aAAoB,QAAQ,GAAGA,YAAmB,QAAQ,GAAGA,WAAkB,ECA/J,IAAOC,EAAQ,iBACT,CAAE,WAAAC,EAAY,SAAAC,EAAU,eAAAC,EAAgB,SAAAC,EAAU,OAAAC,EAAQ,kBAAAC,EAAmB,iBAAAC,EAAkB,oBAAAC,EAAqB,qBAAAC,EAAsB,gBAAAC,EAAiB,UAAAC,EAAW,gBAAAC,EAAiB,YAAAC,EAAa,MAAAC,EAAO,YAAAC,EAAa,kBAAAC,EAAmB,wBAAAC,EAAyB,sBAAAC,EAAuB,MAAAC,EAAO,cAAAC,EAAe,YAAAC,EAAa,QAAAC,EAAS,WAAAC,EAAY,eAAAC,EAAgB,WAAAC,EAAY,aAAAC,EAAc,eAAAC,EAAgB,iBAAAC,EAAkB,gBAAAC,EAAiB,kBAAAC,CAAkB,EAAI,iBCE5cC,EAAO,SAASC,EAASC,GAAK,CAACF,EAAO,WAAW,EAAE,GAAGG,EAAkB,QAAQ,CAAC,CAAC,KAAAC,EAAK,KAAAC,CAAI,IAAI,CAAC,IAAIC,EAAGJ,EAAI,QAAQE,EAAKC,CAAI,EAAEH,EAAI,KAAKC,EAAkB,QAAQG,CAAE,CAAE,CAAC,CAAE,CAAC", "names": ["ADDON_ID", "constants_default", "ADDON_ID", "manager_api_default", "ActiveTabs", "Consumer", "ManagerContext", "Provider", "addons", "combineParameters", "controlOrMetaKey", "controlOrMetaSymbol", "eventMatchesShortcut", "eventToShortcut", "isMacLike", "isShortcutTaken", "keyToSymbol", "merge", "mockChannel", "optionOrAltSymbol", "shortcutMatchesShortcut", "shortcutToHumanString", "types", "useAddonState", "useArgTypes", "useArgs", "useChannel", "useGlobalTypes", "useGlobals", "useParameter", "useSharedState", "useStoryPrepared", "useStorybookApi", "useStorybookState", "addons", "ADDON_ID", "api", "constants_default", "kind", "name", "id"]}