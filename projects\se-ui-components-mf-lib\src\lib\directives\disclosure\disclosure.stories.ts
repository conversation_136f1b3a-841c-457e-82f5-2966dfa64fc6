import { provideAnimations } from '@angular/platform-browser/animations';
import { action } from '@storybook/addon-actions';
import { moduleMetadata, type Meta } from '@storybook/angular';

import { SeDisclosureDirective } from './disclosure.directive';
import { SeDisclosureModule } from './disclosure.module';

export default {
  title: 'Directives/Disclosure (expand-collapse)',
  component: SeDisclosureDirective,
  decorators: [
    moduleMetadata({
      imports: [SeDisclosureModule],
      providers: [provideAnimations()],
    }),
  ],
  argTypes: {
    seDisclosureTargetId: { type: 'string' },
  },
  args: {
    seDisclosureTargetId: 'example-paragraph',
  },
  tags: ['autodocs'],
  render: (args) => ({
    props: {
      ...args,
      onSeDisclosureChange: action('seDisclosureChange'),
    },
    template: `
      <p
        seDisclosure
        [seDisclosureTargetId]="seDisclosureTargetId"
        (seDisclosureChange)="onSeDisclosureChange($event)"
      >
        Pulsa este párrafo para expandir. O, focalízalo con <kbd>tab</kbd> y
        pulsa <kbd>Enter</kbd> o <kbd>Space</kbd>.
      </p>
      <div [attr.id]="seDisclosureTargetId">
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Repellat,
        accusantium itaque sequi obcaecati vero similique magnam laudantium
        numquam, nulla porro nihil architecto velit eligendi consequuntur expedita
        accusamus officiis iure earum.
      </div>
    `,
  }),
} as Meta<SeDisclosureDirective>;

export const Default = {};
