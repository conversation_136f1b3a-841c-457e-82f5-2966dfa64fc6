import { TemplateRef } from "@angular/core";
import { Nullable } from "primeng/ts-helpers";
import { SeButton } from "../button";

export interface SeAlertMessage {
  title: string;
  subtitle?: string;
  type: AlertType;
  list: Array<string>;
  id?: string;
  content?: TemplateRef<HTMLElement> | string;
  orderedListData?: OrderedListData;
  closeButton?: boolean;
  showAlertIcon?: boolean;
  collapsed?: boolean;
  filtered?: boolean;
  minFilteredListLength?: number;
  collapseButton?: Nullable<SeButton>;
  filterButton?: Nullable<SeButton>;
  contentClass?: string;
  alertClass?: string;
}

export enum SeAlertType {
  INFO = 'info',
  ERROR = 'error',
  WARNING = 'warning',
  SUCCESS = 'success',
  NEUTRAL = 'neutral',
}

export type AlertType = `${SeAlertType}`; // this equals 'info' | 'error' | 'warning' | 'success'

export interface OrderedListData {
  items?: OrderedItemsData[];
  orderedType: OrderedType;
}

export interface OrderedItemsData {
  text: string;
  subList?: string[];
  subListOrderedType?: OrderedType | 'number-parenthesis' | 'letter-parenthesis';
}

export type OrderedType = '1' | 'A' | 'a' | 'I' | 'i';
