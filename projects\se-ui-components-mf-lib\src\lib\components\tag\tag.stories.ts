import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';

import { TagComponent } from './tag.component';
import { SeTagModule } from './tag.module';
import { SeTagThemeEnum } from './models';

type TagComponentStoryType = TagComponent & { label: string };

/* More on how to set up stories at:
https://storybook.js.org/docs/angular/writing-stories/introduction */
const meta: Meta<TagComponentStoryType> = {
  title: 'Components/Tag',
  component: TagComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [SeTagModule],
    }),
  ],
  args: {
    tagTheme: SeTagThemeEnum.PRIMARY,
    closable: true,
    label: 'Tag content',
  },
  argTypes: {
    tagTheme: {
      description: 'Changes the tag theme style.',
      options: Object.values(SeTagThemeEnum),
      control: { type: 'select' },
      table: {
        defaultValue: { summary: SeTagThemeEnum.PRIMARY },
      },
    },
    closable: {
      description: 'Allow on close the tag',
      control: { type: 'boolean' },
      type: 'boolean',
      table: { defaultValue: { summary: true } },
    },
    onClose: {
      action: 'close',
      description: 'Emitted when the close button is clicked.',
      table: {
        type: {
          summary: 'EventEmitter<void>',
        },
      },
    },
    label: {
      description: 'The text content of the tag',
      control: 'text',
    },
  },
  render: (args) => ({
    props: args,
    template: `
      <se-tag
        [tagTheme]="tagTheme"
        [closable]="closable"
        (onClose)="close()"
      >
        {{ label }}
      </se-tag>
    `,
  }),
};

export default meta;
type Story = StoryObj<TagComponentStoryType>;

export const Tag: Story = { args: { tagTheme: SeTagThemeEnum.PRIMARY } };
export const NoClose: Story = {
  args: { tagTheme: SeTagThemeEnum.PRIMARY, closable: false },
};
export const Secondary: Story = {
  args: { tagTheme: SeTagThemeEnum.SECONDARY },
};
export const Danger: Story = { args: { tagTheme: SeTagThemeEnum.DANGER } };
export const onlyText: Story = { args: { tagTheme: SeTagThemeEnum.ONLY_TEXT } };
export const Pink: Story = { args: { tagTheme: SeTagThemeEnum.PINK } };
export const purple: Story = { args: { tagTheme: SeTagThemeEnum.PURPLE } };
export const Success: Story = { args: { tagTheme: SeTagThemeEnum.SUCCESS } };
export const Warning: Story = { args: { tagTheme: SeTagThemeEnum.WARNING } };
export const Info: Story = { args: { tagTheme: SeTagThemeEnum.INFO } };
export const Gray: Story = { args: { tagTheme: SeTagThemeEnum.GRAY } };