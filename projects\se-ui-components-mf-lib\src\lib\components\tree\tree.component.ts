import { Component, Input } from '@angular/core';
import { TreeNode } from 'primeng/api';

@Component({
  selector: 'se-tree',
  styleUrls: ['./tree.component.scss'],
  template: `
    <p-tree [value]="tree">
      <ng-template let-node pTemplate="default">
      <ng-icon class="tree-node-icon" [name]="node.icon"></ng-icon>
      {{ node.label }}
    </ng-template>

    </p-tree>
  `
})
export class TreeComponent {
  @Input() tree: TreeNode[] = [];
}
