import { SeHttpResponse } from '../http/http-service.model';

export type iDocumentPadoctStatus =
  | 'NOT_UPLOADED'
  | 'UPLOADED'
  | 'PENDING'
  | 'VALIDATING_ERROR'
  | 'TECHNICAL_ERROR'
  | 'SIGN_ERROR'
  | 'STAGED'
  | 'SIGNED'
  | 'SIGNAT'
  | 'PENDING_SIGN';

/**
 * DOWNLOAD FILE
 */

// Request
export interface RequestDownloadFile {
  id: string;
}

export interface RequestDownloadNamedZipFiles {
  documents: Record<string, string>;
}

// Response
export interface ResponseDownloadFile extends SeHttpResponse {
  content: iDocumentPadoct;
}

export interface RequestJoinDocuments {
  ids: string[];
}

export interface ResponseDownloadFiles extends SeHttpResponse {
  content: { documentResponseList: iDocumentPadoct[] };
}

// Entity model
export interface iDocumentPadoct {
  id: string;
  idPadoct: string;
  idEntity: string;
  idFunctionalModule: WcDocumentsFunctionalModulesT;
  sigedaType: string;
  codSigedaType: string;
  nomSigedaType: string;
  documentType: string;
  format: string;
  nom: string;
  description: string;
  csv: string;
  indActivo: boolean;
  base64File: string;
  status: iDocumentPadoctStatus;
  size: number;
  key: string;
  codeDescriptionComplementary: string;
}

/**
 * DOWNLOAD FILE BY CSV
 */

// Response
export interface ResponseDownloadCSV extends SeHttpResponse {
  content: iDocumentCSV;
}

// Entity model
export interface iDocumentCSV {
  nom: string;
  contingut: string;
  tipus: string;
}

/**
 * SEND EMAIL DOCUMENT
 */

// Request
export interface SendEmailDocumentRequest {
  listDocumentsIds?: string[];
  listDocumentsCsv?: string[];
  email: string;
}

// Response
export interface SendEmailResponse {
  content: boolean;
}

export enum MimeTypes {
  'application/pdf' = '.pdf',
  'text/plain' = '.txt',
  'text/csv' = '.csv',
  'application/vnd.ms-excel' = '.xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' = '.xlsx',
  'application/msword' = '.doc',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document' = '.docx',
  'application/vnd.oasis.opendocument.text' = '.odt',
  'application/vnd.oasis.opendocument.spreadsheet' = '.ods',
  'image/gif' = '.gif',
  'image/jpeg' = '.jpg',
  'image/png' = '.png',
}

// Documents: functional modules
export enum WcDocumentsFunctionalModules {
  'TAX600' = 'TAX600',
  'PAGOS_INDEGUTS' = 'PAGOS_INDEGUTS',
  'INSPECTION_SIGNATURE' = 'INSPECTION_SIGNATURE',
}
export declare type WcDocumentsFunctionalModulesT =
  keyof typeof WcDocumentsFunctionalModules;

// Documents: endpoint services
export enum WcDocumentsServices {
  DOWNLOAD = 'DOWNLOAD',
  DOWNLOAD_RECEIPTS = 'DOWNLOAD_RECEIPTS',
}

export declare type WcDocumentsServicesT = keyof typeof WcDocumentsServices;

export interface SeDocument {
  type: string;
  subtype: string;
  name?: string;
  description: string;
  required?: boolean;
  allowedFiles?: string[];
  allowedSize?: number;
  multiple?: boolean;
}

export interface NamedDocument {
  idPadoct: string;
  customName: string;
}
