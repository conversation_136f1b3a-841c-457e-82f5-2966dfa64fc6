import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  SeDataStorageService,
  SeAuthService,
  SeLoginResponse,
  SeLoginService,
} from '../../services';

@Component({
  selector: 'se-user-info-bar',
  template: `
    <div
      [id]="id"
      class="user-info-bar"
      [ngClass]="wrapperClass"
      [ngClass]="{ 'has-buttons': isLogout }"
    >
      <div class="user-info">
        <i *ngIf="icon" [ngClass]="[icon]" class="icon">
          <ng-icon name="matPerson2Outline"></ng-icon>
        </i>
        <span *ngIf="namePrefix" class="name-prefix text-md">{{ namePrefix }}</span>
        <span *ngIf="name" class="name text-md">{{ name }}</span>
        <span *ngIf="nif" class="nif text-md">NIF: {{ nif | uppercase }}</span>
      </div>

      <div *ngIf="isLogout && authService.validCookieToken()" class="user-login">
        <se-button
          size="small"
          (onClick)="logout()"
        >
          <i class="logout-icon"><ng-icon name="matLogoutOutline"></ng-icon></i>
          {{ 'UI_COMPONENTS.USER_INFO_BAR.LOGOUT' | translate }}
        </se-button>
      </div>
    </div>
  `,
  styleUrls: ['./user-info-bar.component.scss'],
})
export class UserInfoBarComponent implements OnInit {
  @Input() id: string = 'user-info-bar';
  @Input() wrapperClass?: string;
  @Input() name?: string;
  @Input() namePrefix?: string;
  @Input() nif?: string;
  @Input() secured: boolean = false;
  @Input() isLogout: boolean = false;
  @Input() deleteDataStorage: string | null = null;
  @Input() icon?: string;

  @Output() loginEvent: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() logoutEvent: EventEmitter<boolean> = new EventEmitter<boolean>();

  constructor(
    private loginService: SeLoginService,
    public authService: SeAuthService,
    private dataStorage: SeDataStorageService
  ) {}

  ngOnInit(): void {
    if (this.secured) this.login();
  }

	login(): void {
    this.loginService.login(true).then((response: SeLoginResponse) => {
      if (response?.content?.usuario) {
        this.nif = response.content.usuario.nifPresentador;
        this.name = response.content.usuario.nombrePresentador;
        this.loginEvent.emit(true);
      }
    });
  }

  logout(): void {
    this.loginService.logout().then((response: string) => {
      if (response) {
        if (this.deleteDataStorage)
          this.dataStorage.deleteItem(this.deleteDataStorage);
        this.logoutEvent.emit(true);
      }
    });
  }
}
