import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { BadgeComponent } from './badge.component';
import { SeBadgeModule } from './badge.module';

const meta: Meta<BadgeComponent> = {
  title: 'Components/Badge',
  component: BadgeComponent,
  decorators: [
    moduleMetadata({
      imports: [SeBadgeModule],
    }),
  ],
  args: {
    badgeTheme: 'info',
    textColor: 'white',
    textInsideCircle: '525',
    label: 'Notifications',
  },
  argTypes: {
    color: {
      description: 'Color of the circle (overrides badgeTheme if set).',
      control: { type: 'color' },
      table: { defaultValue: { summary: '' } },
    },
    badgeTheme: {
      description: 'Theme of the badge.',
      options: ['info', 'success', 'warning', 'error'],
      control: { type: 'select' },
      table: { defaultValue: { summary: 'info' } },
    },
    textColor: {
      description: 'Color of the text inside the circle.',
      control: { type: 'color' },
      table: { defaultValue: { summary: 'white' } },
    },
    textInsideCircle: {
      description: 'Text inside the circle.',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
    label: {
      description: 'Label at the right of the circle.',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
  },
  tags: ['autodocs'],
  render: (args) => ({
    template: `
      <se-badge
        [color]="color"
        [badgeTheme]="badgeTheme"
        [textColor]="textColor"
        [textInsideCircle]="textInsideCircle"
        [label]="label">
      </se-badge>
    `,
    props: {
      ...args,
    },
  }),
};

export default meta;
type Story = StoryObj<BadgeComponent>;

export const Badge: Story = {};

export const OnlyCircle: Story = {
  args: {
    textInsideCircle: '',
  },
};
