import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { SeAriaLabelDirective } from './aria-label.directive';
import { SeAriaLabelModule } from './aria-label.module';
import { SeSharedModule } from '../../shared/shared.module';

const meta: Meta<SeAriaLabelDirective> = {
  title: 'Directives/Aria Label',
  component: SeAriaLabelDirective,
  decorators: [
    moduleMetadata({
      imports: [SeAriaLabelModule,SeSharedModule],
    }),
  ],
  args: {
    ariaLabelInput: {
      querySelector: 'div.test',
      translationKey: 'ariaLabel'
    } 
  },
  tags: ['autodocs'],
  render: (args) => ({
    props: {
      ...args,
    },
    template: `
      <div [ariaLabel]="ariaLabelInput">
        <div class="test">
          <ng-icon class="info-icon text-md" name="matInfo"></ng-icon>
        </div>
      </div>
    `,
    styles: [
      `
        .icon-button {
          all: initial;
          width: 16px;
          height: 16px;
          font-size: 16px;
          cursor: pointer;
          border-radius: 8px;
      
          &:focus-visible {
            outline: var(--color-black) auto 1px;
          }
        }
      `
    ]
  }),
};

export default meta;
type Story = StoryObj<SeAriaLabelDirective>;

export const Default: Story = {};
