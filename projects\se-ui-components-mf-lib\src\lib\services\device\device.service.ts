import { Injectable } from '@angular/core';
import { fromEvent, map } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DeviceService {

  userAgent = navigator.userAgent;

  constructor() { }

  /**
   * Check: mobile
   * @description Check if the device is a mobile
   * @returns {boolean} Returns true/false depending whether the device is a mobile o not.
   */
  isMobile: () => boolean = (): boolean => (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS/i.test(this.userAgent)) || window.innerWidth < 768 ? true : false;

  /**
   * Check: mobile or tablet
   * @description Check if the device is a mobile or tablet
   * @returns {boolean} Returns true/false depending whether the device is a mobile/tablet o not.
   */
  isMobileOrTablet: () => boolean = (): boolean => window.innerWidth >= 768 && window.innerWidth < 992 ? true : false;

  /**
   * Check: desktop
   * @description Check if the device is a PC (desktop)
   * @returns {boolean} Returns true/false depending whether the device is a PC (desktop) o not.
   */
  isDesktop: () => boolean = (): boolean => !(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS/i.test(this.userAgent)) || window.innerWidth >= 992 ? true : false;

  isMobileSize: () => boolean = (): boolean => window.innerWidth < 768 ? true : false;

  isTabletSize: () => boolean = (): boolean => window.innerWidth >= 768 && window.innerWidth < 992 ? true : false;  

  isDesktopSize: () => boolean = (): boolean => window.innerWidth >= 992 ? true : false;

  isMobileSize$ = fromEvent(window, 'resize').pipe(map(() => this.isMobile()));
  isTabletSize$ = fromEvent(window, 'resize').pipe(map(() => this.isTabletSize()));
  isDesktopSize$ = fromEvent(window, 'resize').pipe(map(() => this.isDesktopSize()));
}