<header class="se-header-info container">
  <section class="se-header-info__main-container">
    <section class="se-header-info__title-bar">
      <h1
        *ngIf="title"
        class="se-header-info__title"
        [class.text-truncate]="!expanded"
        [innerHTML]="title | translate"
      ></h1>
      <se-button
        class="d-lg-none"
        [icon]="disclosureIconName"
        [iconSize]="'32px'"
        [btnTheme]="SeButtonTheme.TRUE_ONLY_TEXT"
        [ariaExpanded]="expanded"
        [ariaControls]="
          'se-header-info__info-items-container se-header-info__help-button'
        "
        (onClick)="expanded = !expanded"
      />
    </section>

    <section id="se-header-info__info-items-container" class="se-header-info__info-items-container d-lg-flex"
      [class.d-none]="!expanded"  *ngIf="hasInfoItems || hasTags">
      <div *ngFor="let infoItemRow of infoItemsList; index as i">
        <ng-container *ngTemplateOutlet="infoItemsListTemplate; context: { $implicit: infoItemRow }" />
        <div *ngIf="hasTags && i === 0" class="se-header-info__tags-container d-none d-lg-inline-flex">
          <!-- Por definición funcional los tags de la primera fila solo se muestran en tamaños de pantalla anchos. -->
          <ng-container *ngTemplateOutlet="tagList" />
        </div>
      </div>
      <div *ngIf="hasTags" class="se-header-info__tags-container d-lg-none">
        <!-- Por definición funcional los tags se muestran en la parte de
            abajo en anchos de pantalla reducidos. -->
        <ng-container *ngTemplateOutlet="tagList" />
      </div>
    </section>
  </section>

  <div class="se-header-info__actions-container d-lg-flex"
    [ngClass]="{'d-none': !expanded, 'mt-3': customButton || buttonDropdown || showHelp }">
    <se-button *ngIf="customButton && customButton.onButtonClick"
      [size]="customButton.size || 'default'"
      [disabled]="customButton.disabled || false"
      [btnTheme]="customButton.btnTheme || SeButtonTheme.PRIMARY"
      [icon]="customButton.icon"
      [iconSize]="customButton.iconSize"
      [iconPosition]="customButton.iconPosition"
      [type]="customButton.type || 'button'"
      (onClick)="customButton.onButtonClick($event)"
    >
      {{customButton.label}}
    </se-button>
    <se-button-dropdown *ngIf="buttonDropdown"
      [items]="buttonDropdown.items"
      [buttonOptions]="buttonDropdown.button"
    >
      {{ buttonDropdown.button?.label }}
    </se-button-dropdown>
    <se-button
      *ngIf="showHelp"
      id="se-header-info__help-button"
      class="d-lg-block"

      [btnTheme]="SeButtonTheme.SECONDARY"
      (onClick)="this.helpButtonClick.emit($event)"
    >
      {{ "UI_COMPONENTS.BUTTONS.HELP" | translate }}
    </se-button>
  </div>
</header>

<ng-template #tagList>
  <se-tag
    *ngFor="let tag of tags"
    [tagTheme]="tag?.tagTheme ?? 'primary'"
    [closable]="tag?.closable ?? false"
    [tooltipText]="tag?.tooltipText ?? ''"
  >
    {{ tag?.label ?? "" | translate }}
  </se-tag>
</ng-template>

<ng-template #infoItemsListTemplate let-items>
  <div *ngIf="items.length > 0" class="se-header-info__info-items-list">
    <ng-container *ngFor="let item of items">
      <span *ngIf="item.term" class="se-header-info__info-item-term">
        {{ item.term | translate }}
      </span>
      <span
        *ngIf="item.term && item.details"
        class="se-header-info__info-item-details"
        [attr.translate]="item.translateNoAttr ? 'no' : undefined"
      >
        {{ item.details | translate }}
      </span>

      <se-link
        *ngIf="item.link"
        class="se-header-info__info-item-link"
        [disabled]="item.link?.disabled ?? false"
        [href]="item.link?.href ?? ''"
        [iconName]="item.link?.iconName ?? ''"
        [size]="item.link?.size ?? 'regular'"
        [iconPosition]="item.link?.iconPosition ?? 'right'"
        [linkTheme]="item.link?.linkTheme ?? 'secondary'"
        [target]="item.link?.target ?? '_blank'"
        [ariaLabel]="item.link?.ariaLabel ?? ''"
        (onClick)="item.link?.onLinkClick($event) ?? {}"
      >
        <span class="text-2xs">{{ item.link?.label ?? "" | translate }}</span>
      </se-link>
    </ng-container>
  </div>
</ng-template>
