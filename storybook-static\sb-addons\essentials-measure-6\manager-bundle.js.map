{"version": 3, "sources": ["../../../node_modules/@storybook/addon-measure/dist/chunk-YIXVQKZ5.mjs", "global-externals:react", "global-externals:@storybook/manager-api", "global-externals:@storybook/components", "../../../node_modules/@storybook/addon-measure/dist/manager.mjs"], "sourcesContent": ["var ADDON_ID=\"storybook/measure-addon\",TOOL_ID=`${ADDON_ID}/tool`,PARAM_KEY=\"measureEnabled\";\n\nexport { ADDON_ID, PARAM_KEY, TOOL_ID };\n", "export default __REACT__;\nconst { Children, Component, Fragment, Profiler, PureComponent, StrictMode, Suspense, __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED, cloneElement, createContext, createElement, createFactory, createRef, forwardRef, isValidElement, lazy, memo, useCallback, useContext, useDebugValue, useEffect, useImperativeHandle, useLayoutEffect, useMemo, useReducer, useRef, useState, version } = __REACT__;\nexport { Children, Component, Fragment, Profiler, PureComponent, StrictMode, Suspense, __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED, cloneElement, createContext, createElement, createFactory, createRef, forwardRef, isValidElement, lazy, memo, useCallback, useContext, useDebugValue, useEffect, useImperativeHandle, useLayoutEffect, useMemo, useReducer, useRef, useState, version };", "export default __STORYBOOKAPI__;\nconst { ActiveTabs, Consumer, ManagerContext, Provider, addons, combineParameters, controlOrMetaKey, controlOrMetaSymbol, eventMatchesShortcut, eventToShortcut, isMacLike, isShortcutTaken, keyToSymbol, merge, mockChannel, optionOrAltSymbol, shortcutMatchesShortcut, shortcutToHumanString, types, useAddonState, useArgTypes, useArgs, useChannel, useGlobalTypes, useGlobals, useParameter, useSharedState, useStoryPrepared, useStorybookApi, useStorybookState } = __STORYBOOKAPI__;\nexport { ActiveTabs, Consumer, ManagerContext, Provider, addons, combineParameters, controlOrMetaKey, controlOrMetaSymbol, eventMatchesShortcut, eventToShortcut, isMacLike, isShortcutTaken, keyToSymbol, merge, mockChannel, optionOrAltSymbol, shortcutMatchesShortcut, shortcutToHumanString, types, useAddonState, useArgTypes, useArgs, useChannel, useGlobalTypes, useGlobals, useParameter, useSharedState, useStoryPrepared, useStorybookApi, useStorybookState };", "export default __STORYBOOKCOMPONENTS__;\nconst { A, ActionBar, AddonPanel, Badge, Bar, Blockquote, Button, Code, DL, Div, DocumentWrapper, ErrorFormatter, FlexBar, Form, H1, H2, H3, H4, H5, H6, HR, IconButton, IconButtonSkeleton, Icons, Img, LI, Link, ListItem, Loader, OL, P, Placeholder, Pre, ResetWrapper, ScrollArea, Separator, Spaced, Span, StorybookIcon, StorybookLogo, Symbols, SyntaxHighlighter, TT, TabBar, TabButton, TabWrapper, Table, Tabs, TabsState, TooltipLinkList, TooltipMessage, TooltipNote, UL, WithTooltip, WithTooltipPure, Zoom, codeCommon, components, createCopyToClipboardFunction, getStoryHref, icons, interleaveSeparators, nameSpaceClassNames, resetComponents, withReset } = __STORYBOOKCOMPONENTS__;\nexport { A, ActionBar, AddonPanel, Badge, Bar, Blockquote, Button, Code, DL, Div, DocumentWrapper, ErrorFormatter, FlexBar, Form, H1, H2, H3, H4, H5, H6, HR, IconButton, IconButtonSkeleton, Icons, Img, LI, Link, ListItem, Loader, OL, P, Placeholder, Pre, ResetWrapper, ScrollArea, Separator, Spaced, Span, StorybookIcon, StorybookLogo, Symbols, SyntaxHighlighter, TT, TabBar, TabButton, TabWrapper, Table, Tabs, TabsState, TooltipLinkList, TooltipMessage, TooltipNote, UL, WithTooltip, WithTooltipPure, Zoom, codeCommon, components, createCopyToClipboardFunction, getStoryHref, icons, interleaveSeparators, nameSpaceClassNames, resetComponents, withReset };", "import { ADDON_ID, TOOL_ID } from './chunk-YIXVQKZ5.mjs';\nimport React, { useCallback, useEffect } from 'react';\nimport { addons, types, useGlobals, useStorybookApi } from '@storybook/manager-api';\nimport { IconButton, Icons } from '@storybook/components';\n\nvar Tool=()=>{let[globals,updateGlobals]=useGlobals(),{measureEnabled}=globals,api=useStorybookApi(),toggleMeasure=useCallback(()=>updateGlobals({measureEnabled:!measureEnabled}),[updateGlobals,measureEnabled]);return useEffect(()=>{api.setAddonShortcut(ADDON_ID,{label:\"Toggle Measure [M]\",defaultShortcut:[\"M\"],actionName:\"measure\",showInMenu:!1,action:toggleMeasure});},[toggleMeasure,api]),React.createElement(IconButton,{key:TOOL_ID,active:measureEnabled,title:\"Enable measure\",onClick:toggleMeasure},React.createElement(Icons,{icon:\"ruler\"}))};addons.register(ADDON_ID,()=>{addons.add(TOOL_ID,{type:types.TOOL,id:\"measure\",title:\"Measure\",match:({viewMode})=>viewMode===\"story\",render:()=>React.createElement(Tool,null)});});\n"], "mappings": ";AAAA,IAAIA,EAAS,0BAA0BC,EAAQ,GAAGD,SCAlD,IAAOE,EAAQ,UACT,CAAE,SAAAC,EAAU,UAAAC,EAAW,SAAAC,EAAU,SAAAC,EAAU,cAAAC,EAAe,WAAAC,EAAY,SAAAC,EAAU,mDAAAC,EAAoD,aAAAC,EAAc,cAAAC,EAAe,cAAAC,EAAe,cAAAC,EAAe,UAAAC,EAAW,WAAAC,EAAY,eAAAC,EAAgB,KAAAC,EAAM,KAAAC,EAAM,YAAAC,EAAa,WAAAC,EAAY,cAAAC,EAAe,UAAAC,EAAW,oBAAAC,EAAqB,gBAAAC,EAAiB,QAAAC,EAAS,WAAAC,EAAY,OAAAC,EAAQ,SAAAC,GAAU,QAAAC,EAAQ,EAAI,UCDpY,IAAOC,GAAQ,iBACT,CAAE,WAAAC,GAAY,SAAAC,GAAU,eAAAC,GAAgB,SAAAC,GAAU,OAAAC,EAAQ,kBAAAC,GAAmB,iBAAAC,GAAkB,oBAAAC,GAAqB,qBAAAC,GAAsB,gBAAAC,GAAiB,UAAAC,GAAW,gBAAAC,GAAiB,YAAAC,GAAa,MAAAC,GAAO,YAAAC,GAAa,kBAAAC,GAAmB,wBAAAC,GAAyB,sBAAAC,GAAuB,MAAAC,EAAO,cAAAC,GAAe,YAAAC,GAAa,QAAAC,GAAS,WAAAC,GAAY,eAAAC,GAAgB,WAAAC,EAAY,aAAAC,GAAc,eAAAC,GAAgB,iBAAAC,GAAkB,gBAAAC,EAAiB,kBAAAC,EAAkB,EAAI,iBCD5c,IAAOC,GAAQ,wBACT,CAAE,EAAAC,GAAG,UAAAC,GAAW,WAAAC,GAAY,MAAAC,GAAO,IAAAC,GAAK,WAAAC,GAAY,OAAAC,GAAQ,KAAAC,GAAM,GAAAC,GAAI,IAAAC,GAAK,gBAAAC,GAAiB,eAAAC,GAAgB,QAAAC,GAAS,KAAAC,GAAM,GAAAC,GAAI,GAAAC,GAAI,GAAAC,GAAI,GAAAC,GAAI,GAAAC,GAAI,GAAAC,GAAI,GAAAC,GAAI,WAAAC,EAAY,mBAAAC,GAAoB,MAAAC,EAAO,IAAAC,GAAK,GAAAC,GAAI,KAAAC,GAAM,SAAAC,GAAU,OAAAC,GAAQ,GAAAC,GAAI,EAAAC,GAAG,YAAAC,GAAa,IAAAC,GAAK,aAAAC,GAAc,WAAAC,GAAY,UAAAC,GAAW,OAAAC,GAAQ,KAAAC,GAAM,cAAAC,GAAe,cAAAC,GAAe,QAAAC,GAAS,kBAAAC,GAAmB,GAAAC,GAAI,OAAAC,GAAQ,UAAAC,GAAW,WAAAC,GAAY,MAAAC,GAAO,KAAAC,GAAM,UAAAC,GAAW,gBAAAC,GAAiB,eAAAC,GAAgB,YAAAC,GAAa,GAAAC,GAAI,YAAAC,GAAa,gBAAAC,GAAiB,KAAAC,GAAM,WAAAC,GAAY,WAAAC,GAAY,8BAAAC,GAA+B,aAAAC,GAAc,MAAAC,GAAO,qBAAAC,GAAsB,oBAAAC,GAAqB,gBAAAC,GAAiB,UAAAC,EAAU,EAAI,wBCIlpB,IAAIC,EAAK,IAAI,CAAC,GAAG,CAACC,EAAQC,CAAa,EAAEC,EAAW,EAAE,CAAC,eAAAC,CAAc,EAAEH,EAAQI,EAAIC,EAAgB,EAAEC,EAAcC,EAAY,IAAIN,EAAc,CAAC,eAAe,CAACE,CAAc,CAAC,EAAE,CAACF,EAAcE,CAAc,CAAC,EAAE,OAAOK,EAAU,IAAI,CAACJ,EAAI,iBAAiBK,EAAS,CAAC,MAAM,qBAAqB,gBAAgB,CAAC,GAAG,EAAE,WAAW,UAAU,WAAW,GAAG,OAAOH,CAAa,CAAC,CAAE,EAAE,CAACA,EAAcF,CAAG,CAAC,EAAEM,EAAM,cAAcC,EAAW,CAAC,IAAIC,EAAQ,OAAOT,EAAe,MAAM,iBAAiB,QAAQG,CAAa,EAAEI,EAAM,cAAcG,EAAM,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EAAEC,EAAO,SAASL,EAAS,IAAI,CAACK,EAAO,IAAIF,EAAQ,CAAC,KAAKG,EAAM,KAAK,GAAG,UAAU,MAAM,UAAU,MAAM,CAAC,CAAC,SAAAC,CAAQ,IAAIA,IAAW,QAAQ,OAAO,IAAIN,EAAM,cAAcX,EAAK,IAAI,CAAC,CAAC,CAAE,CAAC", "names": ["ADDON_ID", "TOOL_ID", "react_default", "Children", "Component", "Fragment", "Profiler", "PureComponent", "StrictMode", "Suspense", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "cloneElement", "createContext", "createElement", "createFactory", "createRef", "forwardRef", "isValidElement", "lazy", "memo", "useCallback", "useContext", "useDebugValue", "useEffect", "useImperativeHandle", "useLayoutEffect", "useMemo", "useReducer", "useRef", "useState", "version", "manager_api_default", "ActiveTabs", "Consumer", "ManagerContext", "Provider", "addons", "combineParameters", "controlOrMetaKey", "controlOrMetaSymbol", "eventMatchesShortcut", "eventToShortcut", "isMacLike", "isShortcutTaken", "keyToSymbol", "merge", "mockChannel", "optionOrAltSymbol", "shortcutMatchesShortcut", "shortcutToHumanString", "types", "useAddonState", "useArgTypes", "useArgs", "useChannel", "useGlobalTypes", "useGlobals", "useParameter", "useSharedState", "useStoryPrepared", "useStorybookApi", "useStorybookState", "components_default", "A", "ActionBar", "AddonPanel", "Badge", "Bar", "Blockquote", "<PERSON><PERSON>", "Code", "DL", "Div", "DocumentWrapper", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FlexBar", "Form", "H1", "H2", "H3", "H4", "H5", "H6", "HR", "IconButton", "IconButtonSkeleton", "Icons", "Img", "LI", "Link", "ListItem", "Loader", "OL", "P", "Placeholder", "Pre", "ResetWrapper", "ScrollArea", "Separator", "Spaced", "Span", "StorybookIcon", "StorybookLogo", "Symbols", "Syntax<PERSON><PERSON><PERSON><PERSON>", "TT", "TabBar", "TabButton", "TabWrapper", "Table", "Tabs", "TabsState", "TooltipLinkList", "TooltipMessage", "TooltipNote", "UL", "WithTooltip", "WithTooltipPure", "Zoom", "codeCommon", "components", "createCopyToClipboardFunction", "getStoryHref", "icons", "interleaveSeparators", "nameSpaceClassNames", "resetComponents", "with<PERSON><PERSON><PERSON>", "Tool", "globals", "updateGlobals", "useGlobals", "measureEnabled", "api", "useStorybookApi", "toggleMeasure", "useCallback", "useEffect", "ADDON_ID", "react_default", "IconButton", "TOOL_ID", "Icons", "addons", "types", "viewMode"]}