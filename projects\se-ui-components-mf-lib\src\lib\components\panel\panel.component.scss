@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

::ng-deep se-panel {
  //PRIME NG
  .p-panel {
    font-family: var(--font-primary);

    .p-panel-header {
      border-top-right-radius: 0px;
      border-top-left-radius: 0px;
      font-family: var(--font-primary);
      font-weight: var(--font-regular);
      font-size: var(--text-lg);
      padding: 0 1.5rem;
      min-height: 54px;
      border: 1px solid var(--color-gray-300);
      color: var(--color-gray-700);

      .p-panel-header-icon {
        color: var(--color-gray-700);
      }
    }

    .p-panel-content {
      border-bottom-right-radius: 0px;
      border-bottom-left-radius: 0px;
      border: 1px solid var(--color-gray-300);
      border-top: 0 none;
      padding: 1.5rem;
      color: var(--color-gray-700);
    }

    @include media-breakpoint-down(sm) {
      .p-panel-header {
        padding: 0 1rem;
      }
      .p-panel-content {
        padding: 1rem;
      }
    }
  }

  //CUSTOM
  .se-panel {
    &.default .p-panel-header {
      background-color: var(--color-gray-200);
    }

    &.secondary .p-panel-header {
      background-color: var(--color-blue-200);
    }

    &.primary {
      > .p-panel-header {
        background-color: var(--color-white);

        &:hover {
          background-color: var(--color-blue-200);
        }

        .p-panel-icons button.p-panel-toggler {
          color: var(--color-primary-action);
          border-radius: 50%;
          border: 1px solid var(--color-primary-action);

          &:hover {
            background-color: var(--color-blue-200);
          }

          &[aria-expanded="true"] {
            background-color: var(--color-blue-300);
            &:hover {
              background-color: var(--color-blue-300);
            }
          }
        }
      }

      &.p-panel-expanded {
        > .p-panel-header {
          background-color: var(--color-blue-200);
        }
      }
    }

    &__header {
      width: 100%;
      display: flex;
      justify-content: space-between;

      &-title {
        display: inline-flex;
        flex-direction: column;
        padding: var(--padding-sm) 0;
      }

      .se-panel-header-buttons {
        display: inline-flex;
        align-items: center;
        gap: 8px;
      }
      .panel-toggler-separation {
        margin-right: 16px;
      }

      // @media (max-width: 575px) {
      @include media-breakpoint-down(sm) {
        flex-direction: column;

        .se-panel-header-buttons {
          flex-direction: column;
          padding-bottom: var(--padding-sm);

          se-button {
            width: 100%;
          }
        }
      }
    }

    &.empty .p-panel-content {
      padding: 0;
      border: none;
    }
  }

  &.panel-0-padding {
    &:not(:last-child) .p-panel {
      margin-bottom: 1.5rem;
    }

    .p-panel .p-panel-content {
      padding: 0;

      se-table .se-table-component {
        .se-table-container {
          .se-table__header-cell {
            border-top: 1px solid var(--color-gray-300);
          }

          tr:last-child td.se-table__cell {
            border-bottom: none;
          }

          // @media (max-width: 768px) {
            @include media-breakpoint-down(sm) {
            .se-table__body {
              ::ng-deep {
                tr[default-row] {
                  border-right: none;
                  border-left: none;

                  &:last-child {
                    border-bottom: none;
                  }
                }
              }
            }
          }
        }
      }

      .se-table-empty-state {
        border-top: 1px solid var(--color-gray-300);
      }
    }
  }
}
