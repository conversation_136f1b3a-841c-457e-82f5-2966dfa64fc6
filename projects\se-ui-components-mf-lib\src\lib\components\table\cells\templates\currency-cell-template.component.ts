import {
  ChangeDetectionStrategy,
  Component,
  Inject,
  Input,
  LOCALE_ID,
} from '@angular/core';

import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import type {
  CellComponent,
  CurrencyCellConfig,
  FlattenedCell,
} from '../cells.model';

@Component({
  selector: 'se-currency-cell',
  template: `
    <span
      [ngStyle]="cellConfig['ngStyle']"
      [attr.translate]="cellConfig['translateNoAttr'] ? 'no' : undefined"
    >
      {{
        value
          | currency
            : cellConfig.currencyCode ?? CURRENCY_CODE_DEFAULT
            : cellConfig.display ?? DISPLAY_DEFAULT
            : cellConfig.digitsInfo ?? DIGITS_INFO_DEFAULT
            : cellConfig.locale ?? LOCALE_DEFAULT
      }}
    </span>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CurrencyCellComponent implements CellComponent {
  protected readonly CURRENCY_CODE_DEFAULT = 'EUR';
  protected readonly DISPLAY_DEFAULT = 'symbol';
  protected readonly DIGITS_INFO_DEFAULT = '1.2-2';
  protected readonly LOCALE_DEFAULT = this.localeDefault ?? 'ca-ES';

  constructor(@Inject(LOCALE_ID) private readonly localeDefault: string) {
    // Intencionadamente vacío
  }

  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CurrencyCellConfig;
}
