import { Component, EventEmitter, Input, Output, TemplateRef } from '@angular/core';

import { SeTagThemeEnum, type SeTagTheme } from './models';
import { SeTagTooltipPosition } from './models/tag.model';

@Component({
  selector: 'se-tag',
  template: `
    <div
      class="tag text-2xs"
      [ngClass]="[tagTheme]"
      [tooltipPosition]="tooltipPosition"
      [pTooltipAccessible]="showTooltipIcon ? undefined : tooltipText"
      [tooltipStyleClass]="tooltipStyleClass" 
    >
      <span>
        <ng-content />
      </span>
      <ng-icon [pTooltipAccessible]="tooltipText" [tooltipStyleClass]="tooltipStyleClass" [tooltipPosition]="tooltipPosition" class="info-icon" *ngIf="showTooltipIcon && tooltipText" name="matInfoOutline"></ng-icon>
      <button
        *ngIf="closable"
        class="close-button"
        (click)="onClose.emit()"
        [attr.aria-label]="'UI_COMPONENTS.BUTTONS.CLOSE' | translate"
      >
        <ng-icon class="text-base" name="matCloseOutline" />
      </button>
    </div>
  `,
  styleUrls: ['./tag.component.scss'],
})
export class TagComponent {
  @Input() tagTheme: SeTagTheme = SeTagThemeEnum.PRIMARY;
  @Input() tooltipText: string | TemplateRef<HTMLElement> | undefined;
  @Input() closable: boolean = true;
  @Input() showTooltipIcon: boolean = false;
  @Input() tooltipStyleClass: string = '';
  @Input() tooltipPosition: SeTagTooltipPosition = SeTagTooltipPosition.TOP;

  @Output() onClose: EventEmitter<void> = new EventEmitter();
}
