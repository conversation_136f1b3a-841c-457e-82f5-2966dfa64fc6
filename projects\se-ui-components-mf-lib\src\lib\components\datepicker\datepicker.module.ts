import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DatepickerComponent } from './datepicker.component';
import { CalendarModule } from 'primeng/calendar';
import { SeSharedModule } from '../../shared/shared.module';
import { SeFormControlErrorModule } from '../form-control-error/form-control-error.module';
import { CalendarIcon } from 'primeng/icons/calendar';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,  
    CalendarModule, 
    ReactiveFormsModule, 
    SeSharedModule, 
    CalendarIcon,
    SeFormControlErrorModule,
    TranslateModule
  ],
  declarations: [DatepickerComponent],
  exports: [DatepickerComponent],
})
export class SeDatepickerModule {}
