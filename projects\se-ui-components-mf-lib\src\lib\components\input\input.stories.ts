import '@angular/common/locales/global/ca';
import { LOCALE_ID } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';

import { SeInputUpperCaseModule } from '../../directives';
import { SeValidations } from '../../services';
import { SeButtonModule } from '../button/button.module';
import { InputComponent } from '../input/input.component';
import { SeInputModule } from '../input/input.module';

const meta: Meta<InputComponent> = {
  title: 'Components/Input',
  component: InputComponent,
  decorators: [
    moduleMetadata({
      imports: [
        SeInputModule,
        ReactiveFormsModule,
        SeButtonModule,
        SeInputUpperCaseModule
      ],
      providers: [{ provide: LOCALE_ID, useValue: 'ca-ES' }]
    }),
  ],
  args: {
    label: 'Input label',
    disabled: false,
    placeholder: 'Text',
    type: 'text',
    id: 'input-id',
    readonly: false,
    inline: false,
    currencyMode: false,
    currencySymbol: "€",
    decimals: 2,
  },
  argTypes: {
    label: {
      description: 'Label for the input field.',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'Input label' } },
    },
    disabled: {
      description: 'Determines if the input is disabled or not.',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    readonly: {
      description: 'Determines if the input is readonly or not.',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    inline: {
      description: 'Determines if the input is inline flex or not.',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    placeholder: {
      description: 'Placeholder text for the input field.',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'Text' } },
    },
    type: {
      description: 'The type of the input.',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'text' } },
    },
    id: {
      description: 'Input id',
      control: { type: 'text' },
    },
    min: {
      description: 'For number type, determinates min value',
      control: { type: 'text' },
    },
    max: {
      description: 'For number type, determinates max value',
      control: { type: 'text' },
    },
    maxLength: {
      description: 'For number type, determinates maxLenght input value',
      control: { type: 'text' },
    },
    icon: {
      description: 'Icon',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'text' } }
    },
    currencyMode: {
      description: 'Determines if the input has currency format or not.',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    currencySymbol: {
      description: 'Currency symbol to be added at the end of input value',
      control: { type: 'text' },
      table: { defaultValue: { summary: '€' } }
    },
    decimals: {
      description: 'Determinates de decimals of input',
      control: { type: 'number' },
      table: { defaultValue: { summary: 2 } }
    },
    tooltip: {
      description: 'tooltip options',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    tooltipText: {
      description: 'tooltip message',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
    ariaLabel: {
      description: 'Aria label',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
  },
  tags: ['autodocs'],
  render: (args) => ({
    template: `
      <div style="max-width: 350px">
        <form [formGroup]="form" (ngSubmit)="onSubmit()">
          <se-input
            formControlName="value"
            [label]="label"
            [placeholder]="placeholder"
            [readonly]="readonly"
            [disabled]="disabled"
            [inline]="inline"
            [type]="type"
            [id]="id"
            [min]="min"
            [max]="max"
            [maxLength]="maxLength"
            [icon]="icon"
            [tooltip]="tooltip"
            [tooltipText]="tooltipText"
            [ariaLabel]="ariaLabel"
            [currencyMode]="currencyMode"
            [currencySymbol]="currencySymbol"
            [decimals]="decimals"
            [labelAlign]="labelAlign"
            (onBlur)="onBlur($event)">
          </se-input>
        </form>
      </div>
    `,
    props: {
      ...args,
      form: new FormGroup({
        value: new FormControl('',
          SeValidations.listValidations([
            {validator: Validators.required},
            {
              validator: Validators.minLength(3),
              translation: 'UI_COMPONENTS.VALIDATIONS_ERRORS.minlength',
              translateParams:{ minlength: 3 }
            },
            // {
            //   validator: SeValidations.iban, 
            //   translationByKey:{
            //     ibanValue: 'ibanValue',
            //     ibanPattern: 'ibanPattern'
            //   }
            // },
          ])
        ),
      }),
      onBlur: ($event: any) => console.log("onBlur->",$event)
    },
  }),
};

export default meta;
type Story = StoryObj<InputComponent>;

export const DefaultInput: Story = {};
export const labelAlignRight: Story = { args: { labelAlign: "right", value: "Alineado a la derecha"} };
export const labelAlignCenter: Story = { args: { labelAlign: "center", value: "Alineado al centro"} };
export const withValue: Story = { args: { value: "Esto es un input con valor iniciado"} };

export const withMaxLength: Story = { args: { maxLength: 2} };
export const WithTooltip: Story = { args: { tooltip: true, tooltipText: "Esto es un tooltip" } };
export const DisabledInput: Story = { args: { disabled: true } };
export const ReadonlyInput: Story = { args: { readonly: true } };
export const InlineInput: Story = { args: { inline: true } };
export const WithPlaceholder: Story = {
  args: { placeholder: 'Custom Placeholder' },
};
export const WithIcon: Story = { args: { icon: 'matFileDownloadOutline' } };
export const TypePassword: Story = { args: { type: 'password', placeholder: 'Password' } };
export const TypeNumber: Story = { args: { type: 'number', min: 0, max: 100, placeholder: 'Number' } };
export const TypeNumberOnlyInteger: Story = {
  args: {
    type: 'number',
    decimals: 0,
    min: 0,
    max: 100,
    placeholder: 'Number',
  },
};
export const CurrencyMode: Story = { 
  render: (args) => ({
    template: `
      <div style="max-width: 350px">
        <form [formGroup]="form">
          <se-input
            formControlName="value"
            [label]="label"
            [placeholder]="placeholder"
            [readonly]="readonly"
            [inline]="inline"
            [type]="type"
            [id]="id"
            [min]="min"
            [max]="max"
            [icon]="icon"
            [currencyMode]="currencyMode"
            [currencySymbol]="currencySymbol"
            [decimals]="decimals"
            (input)="log($event)">
          </se-input>
        </form>
      </div>
    `,
    props: {
      ...args,
      currencyMode: true,
      form: new FormGroup({
        value: new FormControl(25.5,
          SeValidations.listValidations([
            {validator: Validators.required},
            {
              validator: Validators.minLength(3),
              translation: 'UI_COMPONENTS.VALIDATIONS_ERRORS.minlength',
              translateParams:{ minlength: 3 }
            }
          ])
        ),
      }),
      log: (event: Event) => {
        action(event.type)(event);
      }
    },
  })
};
export const CurrencyModeNoDecimals: Story = { args: { value: 34, currencyMode: true, decimals: 0} };
export const Uppercase: Story = {
  render: (args) => ({
    template: `
      <div style="max-width: 350px">
        <form [formGroup]="form">
          <se-input
            seInputUpperCase
            formControlName="value"
            [label]="label"
            [placeholder]="placeholder"
            [readonly]="readonly"
            [disabled]="disabled"
            [inline]="inline"
            [type]="type"
            [id]="id"
            [min]="min"
            [max]="max"
            [maxLength]="maxLength"
            [icon]="icon"
            [tooltip]="tooltip"
            [tooltipText]="tooltipText"
            [currencyMode]="currencyMode"
            [currencySymbol]="currencySymbol"
            [decimals]="decimals"
            [labelAlign]="labelAlign"
            (input)="handleInput($event)"
          />
        </form>
      </div>
    `,
    props: {
      ...args,
      form: new FormGroup({ value: new FormControl('') }),
      handleInput: (event: InputEvent) => {
        action('InputEvent')({
          'target.value': (event.target as HTMLInputElement).value,
        });
      },
    },
  }),
};
