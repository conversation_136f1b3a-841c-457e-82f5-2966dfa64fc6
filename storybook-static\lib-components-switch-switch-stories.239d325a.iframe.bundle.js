(self.webpackChunkse_ui_components_mf_lib=self.webpackChunkse_ui_components_mf_lib||[]).push([[417],{"./projects/se-ui-components-mf-lib/src/lib/components/switch/switch.stories.ts":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Switch:()=>Switch,default:()=>switch_stories});var _class,SwitchComponent_1,fesm2022_forms=__webpack_require__("./node_modules/@angular/forms/fesm2022/forms.mjs"),dist=__webpack_require__("./node_modules/@storybook/angular/dist/index.mjs"),tslib_es6=__webpack_require__("./node_modules/tslib/tslib.es6.mjs"),switch_component=__webpack_require__("./projects/se-ui-components-mf-lib/src/lib/components/switch/switch.component.ts.css?ngResource!=!./node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=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%3D%3D!./projects/se-ui-components-mf-lib/src/lib/components/switch/switch.component.ts"),switch_component_default=__webpack_require__.n(switch_component),core=__webpack_require__("./node_modules/@angular/core/fesm2022/core.mjs");let SwitchComponent=(SwitchComponent_1=_class=class SwitchComponent{constructor(){this.label="",this.labelPosition="right",this.disabled=!1,this.value=!1,this.focus=!1,this.hover=!1,this.onChange=()=>{},this.onTouched=()=>{}}toggle(){this.disabled||(this.value=!this.value,this.onChange(this.value),this.onTouched())}writeValue(value){this.value=value}registerOnChange(fn){this.onChange=fn}registerOnTouched(fn){this.onTouched=fn}setDisabledState(isDisabled){this.disabled=isDisabled}},_class.propDecorators={label:[{type:core.Input}],labelPosition:[{type:core.Input}],disabled:[{type:core.Input}],id:[{type:core.Input}]},_class);SwitchComponent=SwitchComponent_1=(0,tslib_es6.gn)([(0,core.Component)({selector:"se-switch",template:'\n    <div\n      class="switch-container"\n      [class.left]="labelPosition === \'left\'"\n      [class.right]="labelPosition === \'right\'"\n    >\n      <label\n        (click)="toggle()"\n        [for]="id"\n        [class.label-right]="labelPosition === \'right\'"\n        >{{ label }}</label\n      >\n      <div\n        class="switch"\n        (mouseenter)="hover = true"\n        (mouseleave)="hover = false"\n      >\n        <input\n          type="checkbox"\n          [attr.id]="id"\n          style="display: none;"\n          [checked]="value"\n        />\n        <div\n          class="slider"\n          (click)="toggle()"\n          [class.checked]="value"\n          [class.focus]="focus && !value && !disabled"\n          [class.hover]="hover && !disabled"\n          [class.disabled]="disabled"\n          (focus)="focus = true"\n          (blur)="focus = false"\n          tabindex="0"\n        >\n          <div class="toggle-ball" [class.checked]="value">\n            <ng-container *ngIf="value"><ng-content></ng-content></ng-container>\n          </div>\n        </div>\n      </div>\n    </div>\n  ',providers:[{provide:fesm2022_forms.JU,useExisting:(0,core.forwardRef)((()=>SwitchComponent_1)),multi:!0}],styles:[switch_component_default()]})],SwitchComponent);var common=__webpack_require__("./node_modules/@angular/common/fesm2022/common.mjs");let SeSwitchModule=class SeSwitchModule{};SeSwitchModule=(0,tslib_es6.gn)([(0,core.NgModule)({imports:[common.CommonModule,fesm2022_forms.UX,fesm2022_forms.u5],declarations:[SwitchComponent],exports:[SwitchComponent]})],SeSwitchModule);const switch_stories={title:"Components/Switch",component:SwitchComponent,decorators:[(0,dist.moduleMetadata)({imports:[SeSwitchModule,fesm2022_forms.UX]})],args:{label:"Switch label",disabled:!1,labelPosition:"right"},argTypes:{disabled:{description:"Determines if the switch is disabled or not.",control:{type:"boolean"},table:{defaultValue:{summary:!1}}},labelPosition:{description:"Determines the position of the label, left or right.",options:["left","right"],control:{type:"radio"},table:{defaultValue:{summary:"right"}}}},tags:["autodocs"],render:args=>({template:'\n      <form [formGroup]="form">\n        <se-switch\n          [label]="label"\n          [labelPosition]="labelPosition"\n          [formControl]="form.controls[\'value\']">\n          \x3c!-- You can insert an icon or other content here --\x3e\n        </se-switch>\n      </form>\n    ',props:{...args,form:new fesm2022_forms.cw({value:new fesm2022_forms.NI({value:!1,disabled:args.disabled})})}})},Switch={}},"./node_modules/css-loader/dist/runtime/api.js":module=>{"use strict";module.exports=function(cssWithMappingToString){var list=[];return list.toString=function toString(){return this.map((function(item){var content="",needLayer=void 0!==item[5];return item[4]&&(content+="@supports (".concat(item[4],") {")),item[2]&&(content+="@media ".concat(item[2]," {")),needLayer&&(content+="@layer".concat(item[5].length>0?" ".concat(item[5]):""," {")),content+=cssWithMappingToString(item),needLayer&&(content+="}"),item[2]&&(content+="}"),item[4]&&(content+="}"),content})).join("")},list.i=function i(modules,media,dedupe,supports,layer){"string"==typeof modules&&(modules=[[null,modules,void 0]]);var alreadyImportedModules={};if(dedupe)for(var k=0;k<this.length;k++){var id=this[k][0];null!=id&&(alreadyImportedModules[id]=!0)}for(var _k=0;_k<modules.length;_k++){var item=[].concat(modules[_k]);dedupe&&alreadyImportedModules[item[0]]||(void 0!==layer&&(void 0===item[5]||(item[1]="@layer".concat(item[5].length>0?" ".concat(item[5]):""," {").concat(item[1],"}")),item[5]=layer),media&&(item[2]?(item[1]="@media ".concat(item[2]," {").concat(item[1],"}"),item[2]=media):item[2]=media),supports&&(item[4]?(item[1]="@supports (".concat(item[4],") {").concat(item[1],"}"),item[4]=supports):item[4]="".concat(supports)),list.push(item))}},list}},"./node_modules/css-loader/dist/runtime/noSourceMaps.js":module=>{"use strict";module.exports=function(i){return i[1]}},"./projects/se-ui-components-mf-lib/src/lib/components/switch/switch.component.ts.css?ngResource!=!./node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=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%3D%3D!./projects/se-ui-components-mf-lib/src/lib/components/switch/switch.component.ts":(module,__unused_webpack_exports,__webpack_require__)=>{var ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___=__webpack_require__("./node_modules/css-loader/dist/runtime/noSourceMaps.js"),___CSS_LOADER_EXPORT___=__webpack_require__("./node_modules/css-loader/dist/runtime/api.js")(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);___CSS_LOADER_EXPORT___.push([module.id,"\n      .switch-container {\n        display: flex;\n        align-items: center;\n        font-family: Open Sans;\n        font-size: 14px;\n        font-style: normal;\n        font-weight: 400;\n        line-height: 20px;\n      }\n\n      .switch-container.left label {\n        order: 0;\n        margin-right: 8px;\n      }\n\n      .switch-container.right label.label-right {\n        order: 2;\n        margin-left: 8px;\n      }\n\n      .switch {\n        position: relative;\n        display: inline-block;\n        width: 32px;\n        height: 18px;\n        border-radius: 9px;\n      }\n\n      .slider {\n        position: absolute;\n        cursor: pointer;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: var(--icono-azul, #106bc4);\n        transition: background 0.3s;\n        border-radius: 9px;\n        outline: none;\n      }\n\n      .slider.disabled {\n        cursor: not-allowed;\n        background: var(--icono-disable, #b6b6b6);\n      }\n\n      .slider.checked {\n        background: var(--icono-azul, #106bc4);\n      }\n\n      .slider.hover {\n        background: var(--degradados-azul-600, #004e9b);\n      }\n\n      .slider.focus {\n        background: var(--degradados-azul-700, #002c57);\n      }\n\n      .toggle-ball {\n        position: absolute;\n        width: 14px;\n        height: 14px;\n        left: 2px;\n        top: 2px;\n        background: white;\n        border-radius: 7px;\n        transition: transform 0.3s;\n      }\n\n      .toggle-ball.checked {\n        transform: translateX(14px);\n      }\n    ",""]),module.exports=___CSS_LOADER_EXPORT___.toString()}}]);