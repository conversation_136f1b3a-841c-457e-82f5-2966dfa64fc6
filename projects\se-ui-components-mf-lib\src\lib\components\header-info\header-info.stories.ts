import { Meta, StoryObj, applicationConfig, moduleMetadata } from '@storybook/angular';

import { HeaderInfoComponent } from './header-info.component';
import { SeHeaderInfoModule } from './header-info.module';
import { SeTagThemeEnum } from '../tag';
import { provideAnimations } from '@angular/platform-browser/animations';

/* More on how to set up stories at:
https://storybook.js.org/docs/angular/writing-stories/introduction */
const meta: Meta<HeaderInfoComponent> = {
  title: 'Components/Header Info',
  component: HeaderInfoComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [SeHeaderInfoModule],
    }),
    applicationConfig({
      providers: [provideAnimations()],
    }),
  ],
  args: {
    title: 'Lorem nulla amet labore est quis eiusmod consequat in',
    showHelp: true,
    expanded: false,
    infoItems: [
      { term: 'Declarant', details: 'Deserunt est aliqua' },
      null,
      null,
      { term: 'Link', link: {label: 'href',href: 'https://www.google.com'}},
      { term: 'Link 2',details: 'Link details', link: {label: 'link_click',onLinkClick: ()  => {alert('link_click')}}},
      { term: 'Autoliquidació', details: 'anual 2022' },
      { term: 'Model', details: '550' },
      { term: 'Complementaria de NNNNNNNNNNNNN' },
    ],

    tags: [
      {
        tagTheme: SeTagThemeEnum.SUCCESS,
        label: 'Presentat: 31/01/2022',
      },
      {
        tagTheme: SeTagThemeEnum.WARNING,
        label: 'Pendent de pagament',
      },
    ],
  },
  argTypes: {
    title: {
      description: 'Title',
      control: { type: 'text' },
      table: {
        defaultValue: { summary: '' },
      },
    },
    showHelp: {
      description: 'Show button help',
      control: { type: 'boolean' },
      type: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
    expanded: {
      description: 'Expand header metadata',
      control: 'boolean',
      type: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
    infoItems: {
      description:
        'List of info elements to show. First three items will be placed' +
        ' into the first row. The rest of elements will be placed in the' +
        ' second row.',
      control: 'object',
    },
    tags: {
      description:
        'List of tags to show. Can be customized passing any' +
        ' TagComponent property',
      control: 'object',
    },
  },
};

export default meta;
type Story = StoryObj<HeaderInfoComponent>;

export const Default: Story = {};

export const WithDropdown: Story = {
  args: {
    buttonDropdown: {
      button: {
        label: 'Seleccionar usuario',
        icon: 'matPermIdentityOutline',
        iconPosition: 'right',
      },
      items: [
        {
          label: 'Item 1',
          action: () => console.log('Item 1 clicked'),
        },
        {
          label: 'Item 2',
          action: () => console.log('Item 2 clicked'),
        },
      ],
    },
  },
};

export const ThreeItemsInTheFirstRow = {
  args: {
    infoItems: [
      { term: 'Declarant', details: 'Deserunt est aliqua' },
      { term: 'Protocol', details: '123456' },
      { term: 'Data', details: '03/05/2024' },
      { term: 'Autoliquidació', details: 'anual 2022' },
      { term: 'Model', details: '550' },
      { term: 'Complementaria de NNNNNNNNNNNNN' },
    ],
  },
};
export const InfoItemsArrayArray = {
  args: {
    infoItems: [
      [
        {
          "term": "Notari/ària",
          "details": "Deserunt est aliqua"
        },
        {
          "term": "Protocol/Bis",
          "details": "222"
        },
        {
          "term": "Data",
          "details": "22/05/2024"
        },
      ],
      [
        {
          "term": "Compravenda de béns mobles o  transmissió de drets reals sobre béns mobles, excepte vehicles i drets reals de garantia"
        },
      ],
      [
        {
          "term": "Data de meritació",
          "details": "22/05/2024"
        },
        {
          "term": "Data fi del termini presentació",
          "details": "22/06/2024"
        }
      ]
    ]
  },
};

export const WithoutTags: Story = {
  args: {
    tags: [],
  },
};

export const OnlyTags: Story = {
  args: {
    infoItems: [],
  },
};

export const OnlyTitle: Story = {
  args: {
    infoItems: [],
    tags: [],
  },
};
