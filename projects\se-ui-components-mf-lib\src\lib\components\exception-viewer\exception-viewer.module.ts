import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SeExceptionViewerComponent } from './exception-viewer.component';
import { SeAlertModule } from '../alert/alert.module';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  declarations: [SeExceptionViewerComponent],
  imports: [CommonModule, SeAlertModule, TranslateModule.forChild()],
  exports: [SeExceptionViewerComponent]
})
export class SeExceptionViewerModule {}
