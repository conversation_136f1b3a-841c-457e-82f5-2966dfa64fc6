import { Meta } from "@storybook/blocks";

<Meta title="Core/ListValidations" />

# ListValidations

'SeValidations.listValidations' is a validation that collects a list of validations and adds custom translations to the resulting errors.
This validation supports an array of objects with the following form:

```js
{
  validator: Validators.minLength(3),
  translation: 'UI_COMPONENTS.VALIDATIONS_ERRORS.minlength',
  translateParams:{minlength: 3}
}
```

## How to use

Include the validation within the 'FormControl' as follows:
```js
form: new FormGroup({
  value: new FormControl({ value: '', disabled: args.disabled },
    SeValidations.listValidations([
      {
        validator: Validators.minLength(3),
        translation: 'UI_COMPONENTS.VALIDATIONS_ERRORS.minlength',
        translateParams:{minlength: 3}
      },
    ])
  ),
})
```
In case there is more than one validation, they will be seen in order of writing

```js
form: new FormGroup({
  value: new FormControl({ value: '', disabled: args.disabled },
    SeValidations.listValidations([
      {
        validator: Validators.minLength(3),
        translation: 'UI_COMPONENTS.VALIDATIONS_ERRORS.minlength',
        translateParams:{minlength: 3}
      },
      {
        validator: Validators.maxLength(4),
        translation: 'UI_COMPONENTS.VALIDATIONS_ERRORS.maxlength',
        translateParams:{minlength: 4}
      },
    ])
  ),
})
```

If no translations are added, default ones are used.

```js
form: new FormGroup({
  value: new FormControl({ value: '', disabled: args.disabled },
    SeValidations.listValidations([
      {
        validator: Validators.required
      },
    ])
  ),
})
```

In case there are validations with different errors, it can be used the parameters `translationByKey` and `translationByKeyParams`

```js
form: new FormGroup({
  value: new FormControl({ value: '', disabled: args.disabled },
    SeValidations.listValidations([
      {
        validator: SeValidations.iban, 
        translationByKey:{
          ibanValue: 'ibanValue',
          ibanPattern: 'ibanPattern'
        },
        translationByKeyParams:{
          ibanValue: {ibanValue: 'ibanValue'},
          ibanPattern: {ibanPattern: 'ibanValue'}
        },
      }
    ])
  ),
})
```
