import { InjectionToken } from '@angular/core';
import { SeHttpResponse } from '../http/http-service.model';

export interface SeLoginResponse extends SeHttpResponse {
  content: {
    usuario: SeUser;
    tokenJwt: string;
    tokenExpiration: string;
  };
}

export interface SeLoginSimulatRequest extends SeHttpResponse {
  nifUser: string;
  nifCompany: string;
  method: LoginSimulatMethods;
  certificateType: LoginCertificates;
}

export interface SeLogoutResponse extends SeHttpResponse {
  content: {
    url: string;
  };
}

export interface SeGetEnvironmentResponse extends SeHttpResponse {
  content: SeEnvironment;
}

export enum SeEnvironment {
  DEV = 'dev',
  INT = 'int',
  PRE = 'pre',
  PRO = 'pro',
}
export enum LoginSimulatMethods {
  CERTIFICAT = 'CERTIFICAT',
  IDCATMOBIL = 'IDCATMOBIL',
  CLAVE = 'CLAVE',
}
export type LoginSimulatMethodsT = keyof typeof LoginSimulatMethods;
export type LoginSimulatMethodsValues<LoginSimulatMethods> =
  LoginSimulatMethods[keyof LoginSimulatMethods];

export enum LoginCertificates {
  FIS_PERSON = '0',
  JUR_PERSON = '1',
  SSL_COMP = '2',
  ELECTRONIC_SEU = '3',
  ELECTRONIC_SEG = '4',
  PUBLIC_EMP = '5',
  ENT_JUR_PERSON = '6',
  PUBLIC_EMP_PSEUDONIM = '7',
  QUAL_SEG = '8',
  QUAL_AUTH_WEB = '9',
  SEG_CERTIFICATE = '10',
  REPRE_JUR = '11',
  REPRE_ENT_JUR = '12',
}
export type LoginCertificatesT = keyof typeof LoginCertificates;
export type LoginCertificatesValues<LoginCertificates> =
  LoginCertificates[keyof LoginCertificates];

export interface SeUser {
  accessMode?: string;
  accessToken?: string;
  apellido1: string;
  apellido1Presentador?: string;
  apellido2: string;
  apellido2Presentador?: string;
  assuranceLevel?: string;
  certificateType: LoginCertificatesValues<typeof LoginCertificates>;
  codiTipusPersona?: string;
  companyId?: string;
  companyName?: string;
  email: string;
  environment: string;
  es012?: boolean;
  esAtesa?: boolean;
  esCoordinador?: boolean;
  esLoginGicar?: boolean;
  esEmpleadoPublico?: boolean;
  esJuridico?: boolean;
  esConveniat?: boolean;
  esRepresentant?: boolean;
  esLoginSimple?: boolean;
  esLoginAOC?: boolean;
  fechaCreacion: string; // Date
  fechaUltimaConexion: string; // Date
  fechaLogin: string; // Date
  idCompanyCens: string;
  idPersCens: string;
  idPersCensPresentador: string;
  idSession?: string;
  identifierType?: string;
  indActivo: boolean;
  indUsuarioSimulado?: boolean;
  method: string;
  nif: string;
  nifReal?: string;
  nifPresentador: string;
  nombre: string;
  nombreCompleto?: string;
  nombreCompletoReal?: string;
  nombrePresentador: string;
  nombreSimplePresentador?: string;
  rol: string;
  origin_id: 'GICAR' | 'AOC';
  token?: string;
  tokenExpiration?: string;
  idPersCensNomPropi: string;
  nifNomPropi: string;
  nomNomPropi: string;
  nombreSimpleNomPropi: string;
  apellido1NomPropi: string;
  apellido2NomPropi: string;
}

export const NAME_USER_STORAGE = new InjectionToken<string>('nameUserStorage');
