(self.webpackChunkse_ui_components_mf_lib=self.webpackChunkse_ui_components_mf_lib||[]).push([[312],{"./projects/se-ui-components-mf-lib/src/lib/components/checkbox/checkbox.stories.ts":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Checkbox:()=>Checkbox,default:()=>checkbox_stories});var _class,CheckboxComponent_1,fesm2022_forms=__webpack_require__("./node_modules/@angular/forms/fesm2022/forms.mjs"),dist=__webpack_require__("./node_modules/@storybook/angular/dist/index.mjs"),tslib_es6=__webpack_require__("./node_modules/tslib/tslib.es6.mjs"),checkbox_component=__webpack_require__("./projects/se-ui-components-mf-lib/src/lib/components/checkbox/checkbox.component.ts.css?ngResource!=!./node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=CiAgICAgIC5jb250YWluZXIgewogICAgICAgIGRpc3BsYXk6IGlubGluZS1mbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgICAgIHBhZGRpbmctbGVmdDogMjRweDsKICAgICAgICBmb250LWZhbWlseTogT3BlbiBTYW5zOwogICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0b3MtZGFyaywgIzMzMyk7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIGZvbnQtc3R5bGU6IG5vcm1hbDsKICAgICAgICBmb250LXdlaWdodDogNDAwOwogICAgICAgIGxpbmUtaGVpZ2h0OiAyMHB4OwogICAgICB9CgogICAgICAuY29udGFpbmVyLmRpc2FibGVkIHsKICAgICAgICBjb2xvcjogI2I2YjZiNjsKICAgICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkOwoKICAgICAgICAmIC5jaGVja2JveC1pbnB1dCB7CiAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC5jb250YWluZXIuZGlzYWJsZWQgaW5wdXQgfiAuY2hlY2ttYXJrIHsKICAgICAgICBib3JkZXItY29sb3I6ICNiNmI2YjY7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsKICAgICAgfQoKICAgICAgLmNoZWNrYm94LWlucHV0IHsKICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgICAgb3BhY2l0eTogMDsKICAgICAgICBoZWlnaHQ6IDEwMCU7CiAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgbGVmdDogMDsKICAgICAgICB0b3A6IDA7CiAgICAgICAgei1pbmRleDogMTsKICAgICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgIH0KCiAgICAgIC5jaGVja21hcmsgewogICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgICB0b3A6IDUwJTsKICAgICAgICBsZWZ0OiAwOwogICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNTAlKTsKICAgICAgICBoZWlnaHQ6IDE2cHg7CiAgICAgICAgd2lkdGg6IDE2cHg7CiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjY2M7CiAgICAgICAgY29sb3I6ICNmZmY7CiAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgICB9CgogICAgICAuY29udGFpbmVyOmhvdmVyIGlucHV0Om5vdCg6ZGlzYWJsZWQpOm5vdCg6Y2hlY2tlZCkgfiAuY2hlY2ttYXJrIHsKICAgICAgICBib3JkZXItY29sb3I6ICMxMDZCQzQ7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKICAgICAgfQoKICAgICAgLmNvbnRhaW5lcjpob3ZlciBpbnB1dDpjaGVja2VkIH4gLmNoZWNrbWFyayB7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzAwNGU5YjsKICAgICAgfQoKICAgICAgLmNvbnRhaW5lciBpbnB1dDpjaGVja2VkIH4gLmNoZWNrbWFyayB7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzIxOTZmMzsKICAgICAgfQogICAg!./projects/se-ui-components-mf-lib/src/lib/components/checkbox/checkbox.component.ts"),checkbox_component_default=__webpack_require__.n(checkbox_component),core=__webpack_require__("./node_modules/@angular/core/fesm2022/core.mjs");let CheckboxComponent=(CheckboxComponent_1=_class=class CheckboxComponent{constructor(){this.label="",this.disabled=!1,this.value=!1,this.onChange=()=>{},this.onTouched=()=>{}}onCheckboxChange(event){const target=event.target;this.value=target.checked,this.onChange(this.value),this.onTouched()}writeValue(value){this.value=value}registerOnChange(fn){this.onChange=fn}registerOnTouched(fn){this.onTouched=fn}setDisabledState(isDisabled){this.disabled=isDisabled}},_class.propDecorators={label:[{type:core.Input}],disabled:[{type:core.Input}],id:[{type:core.Input}]},_class);CheckboxComponent=CheckboxComponent_1=(0,tslib_es6.gn)([(0,core.Component)({selector:"se-checkbox",template:'\n    <label class="container" [ngClass]="{ disabled: disabled }" [for]="id">\n      <input\n        type="checkbox"\n        [id]="id"\n        [checked]="value"\n        [disabled]="disabled"\n        (change)="onCheckboxChange($event)"\n        class="checkbox-input"\n      />\n      <span class="checkmark">\n        <svg\n          *ngIf="value"\n          xmlns="http://www.w3.org/2000/svg"\n          fill="none"\n          viewBox="0 0 24 24"\n          stroke-width="2"\n          stroke="currentColor"\n          class="w-6 h-6"\n        >\n          <path\n            stroke-linecap="round"\n            stroke-linejoin="round"\n            d="M4.5 12.75l6 6 9-13.5"\n          />\n        </svg>\n      </span>\n      {{ label }}\n    </label>\n  ',providers:[{provide:fesm2022_forms.JU,useExisting:(0,core.forwardRef)((()=>CheckboxComponent_1)),multi:!0}],styles:[checkbox_component_default()]})],CheckboxComponent);var common=__webpack_require__("./node_modules/@angular/common/fesm2022/common.mjs");let SeCheckboxModule=class SeCheckboxModule{};SeCheckboxModule=(0,tslib_es6.gn)([(0,core.NgModule)({imports:[common.CommonModule,fesm2022_forms.UX,fesm2022_forms.u5],declarations:[CheckboxComponent],exports:[CheckboxComponent]})],SeCheckboxModule);const checkbox_stories={title:"Components/Checkbox",component:CheckboxComponent,decorators:[(0,dist.moduleMetadata)({imports:[SeCheckboxModule,fesm2022_forms.UX]})],args:{label:"Checkbox label",disabled:!1,id:"checkbox-id"},argTypes:{disabled:{description:"Determines if the checkbox is disabled or not.",control:{type:"boolean"},table:{defaultValue:{summary:!1}}}},tags:["autodocs"],render:args=>({template:'\n      <form [formGroup]="form">\n        <se-checkbox\n          [label]="label"\n          [id]="id"\n          [formControl]="form.controls[\'value\']">\n        </se-checkbox>\n      </form>\n    ',props:{...args,form:new fesm2022_forms.cw({value:new fesm2022_forms.NI({value:!1,disabled:args.disabled})})}})},Checkbox={}},"./node_modules/css-loader/dist/runtime/api.js":module=>{"use strict";module.exports=function(cssWithMappingToString){var list=[];return list.toString=function toString(){return this.map((function(item){var content="",needLayer=void 0!==item[5];return item[4]&&(content+="@supports (".concat(item[4],") {")),item[2]&&(content+="@media ".concat(item[2]," {")),needLayer&&(content+="@layer".concat(item[5].length>0?" ".concat(item[5]):""," {")),content+=cssWithMappingToString(item),needLayer&&(content+="}"),item[2]&&(content+="}"),item[4]&&(content+="}"),content})).join("")},list.i=function i(modules,media,dedupe,supports,layer){"string"==typeof modules&&(modules=[[null,modules,void 0]]);var alreadyImportedModules={};if(dedupe)for(var k=0;k<this.length;k++){var id=this[k][0];null!=id&&(alreadyImportedModules[id]=!0)}for(var _k=0;_k<modules.length;_k++){var item=[].concat(modules[_k]);dedupe&&alreadyImportedModules[item[0]]||(void 0!==layer&&(void 0===item[5]||(item[1]="@layer".concat(item[5].length>0?" ".concat(item[5]):""," {").concat(item[1],"}")),item[5]=layer),media&&(item[2]?(item[1]="@media ".concat(item[2]," {").concat(item[1],"}"),item[2]=media):item[2]=media),supports&&(item[4]?(item[1]="@supports (".concat(item[4],") {").concat(item[1],"}"),item[4]=supports):item[4]="".concat(supports)),list.push(item))}},list}},"./node_modules/css-loader/dist/runtime/noSourceMaps.js":module=>{"use strict";module.exports=function(i){return i[1]}},"./projects/se-ui-components-mf-lib/src/lib/components/checkbox/checkbox.component.ts.css?ngResource!=!./node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=CiAgICAgIC5jb250YWluZXIgewogICAgICAgIGRpc3BsYXk6IGlubGluZS1mbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgICAgIHBhZGRpbmctbGVmdDogMjRweDsKICAgICAgICBmb250LWZhbWlseTogT3BlbiBTYW5zOwogICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0b3MtZGFyaywgIzMzMyk7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIGZvbnQtc3R5bGU6IG5vcm1hbDsKICAgICAgICBmb250LXdlaWdodDogNDAwOwogICAgICAgIGxpbmUtaGVpZ2h0OiAyMHB4OwogICAgICB9CgogICAgICAuY29udGFpbmVyLmRpc2FibGVkIHsKICAgICAgICBjb2xvcjogI2I2YjZiNjsKICAgICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkOwoKICAgICAgICAmIC5jaGVja2JveC1pbnB1dCB7CiAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC5jb250YWluZXIuZGlzYWJsZWQgaW5wdXQgfiAuY2hlY2ttYXJrIHsKICAgICAgICBib3JkZXItY29sb3I6ICNiNmI2YjY7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsKICAgICAgfQoKICAgICAgLmNoZWNrYm94LWlucHV0IHsKICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgICAgb3BhY2l0eTogMDsKICAgICAgICBoZWlnaHQ6IDEwMCU7CiAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgbGVmdDogMDsKICAgICAgICB0b3A6IDA7CiAgICAgICAgei1pbmRleDogMTsKICAgICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgIH0KCiAgICAgIC5jaGVja21hcmsgewogICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgICB0b3A6IDUwJTsKICAgICAgICBsZWZ0OiAwOwogICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNTAlKTsKICAgICAgICBoZWlnaHQ6IDE2cHg7CiAgICAgICAgd2lkdGg6IDE2cHg7CiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjY2M7CiAgICAgICAgY29sb3I6ICNmZmY7CiAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgICB9CgogICAgICAuY29udGFpbmVyOmhvdmVyIGlucHV0Om5vdCg6ZGlzYWJsZWQpOm5vdCg6Y2hlY2tlZCkgfiAuY2hlY2ttYXJrIHsKICAgICAgICBib3JkZXItY29sb3I6ICMxMDZCQzQ7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKICAgICAgfQoKICAgICAgLmNvbnRhaW5lcjpob3ZlciBpbnB1dDpjaGVja2VkIH4gLmNoZWNrbWFyayB7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzAwNGU5YjsKICAgICAgfQoKICAgICAgLmNvbnRhaW5lciBpbnB1dDpjaGVja2VkIH4gLmNoZWNrbWFyayB7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzIxOTZmMzsKICAgICAgfQogICAg!./projects/se-ui-components-mf-lib/src/lib/components/checkbox/checkbox.component.ts":(module,__unused_webpack_exports,__webpack_require__)=>{var ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___=__webpack_require__("./node_modules/css-loader/dist/runtime/noSourceMaps.js"),___CSS_LOADER_EXPORT___=__webpack_require__("./node_modules/css-loader/dist/runtime/api.js")(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);___CSS_LOADER_EXPORT___.push([module.id,"\n      .container {\n        display: inline-flex;\n        align-items: center;\n        position: relative;\n        padding-left: 24px;\n        font-family: Open Sans;\n        color: var(--textos-dark, #333);\n        font-size: 14px;\n        font-style: normal;\n        font-weight: 400;\n        line-height: 20px;\n      }\n\n      .container.disabled {\n        color: #b6b6b6;\n        cursor: not-allowed;\n\n        & .checkbox-input {\n        cursor: not-allowed;\n        }\n      }\n\n      .container.disabled input ~ .checkmark {\n        border-color: #b6b6b6;\n        background-color: #f5f5f5;\n      }\n\n      .checkbox-input {\n        position: absolute;\n        opacity: 0;\n        height: 100%;\n        width: 100%;\n        left: 0;\n        top: 0;\n        z-index: 1;\n        cursor: pointer;\n      }\n\n      .checkmark {\n        position: absolute;\n        top: 50%;\n        left: 0;\n        transform: translateY(-50%);\n        height: 16px;\n        width: 16px;\n        border-radius: 4px;\n        border: 1px solid #ccc;\n        color: #fff;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n\n      .container:hover input:not(:disabled):not(:checked) ~ .checkmark {\n        border-color: #106BC4;\n        background-color: #fff;\n      }\n\n      .container:hover input:checked ~ .checkmark {\n        background-color: #004e9b;\n      }\n\n      .container input:checked ~ .checkmark {\n        background-color: #2196f3;\n      }\n    ",""]),module.exports=___CSS_LOADER_EXPORT___.toString()}}]);