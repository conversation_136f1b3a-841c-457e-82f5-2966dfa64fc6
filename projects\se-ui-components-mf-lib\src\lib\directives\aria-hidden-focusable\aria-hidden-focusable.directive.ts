import { Directive, ElementRef, Renderer2, On<PERSON><PERSON>roy } from '@angular/core';
/**
 * Directiva para corregir problemas de accesibilidad en elementos generados dinámicamente
 * por PrimeNG (como `.p-hidden-accessible`).
 *
 * Problema:
 * Algunos elementos ocultos con la clase `.p-hidden-accessible.p-hidden-focusable` y 
 * atributo `aria-hidden="true"` permanecen accesibles al teclado (focusable), lo que 
 * viola la especificación WCAG 4.1.2.
 *
 */
@Directive({
  selector: '[fixAriaHiddenFocusable]',
  standalone: true,
})
export class SeAriaHiddenFocusableDirective implements OnDestroy {
  private observer: MutationObserver;

  constructor(private el: ElementRef, private renderer: Renderer2) {
    /*
    monitoriza cambios en el DOM para evitar ejecutar cambios innecesarios y 
    ajustar dinámicamente los atributos `tabindex` de los elementos que coincidan con 
    el selector problemático*/    
    this.observer = new MutationObserver(() => {
      this.fixFocusableElements();
    });

    this.observer.observe(this.el.nativeElement, {
      childList: true,
      subtree: true,
    });
  }

  private fixFocusableElements() {
    const hiddenAccessibleElements = this.el.nativeElement.querySelectorAll(
      '.p-hidden-accessible.p-hidden-focusable[aria-hidden="true"]'
    );

    hiddenAccessibleElements.forEach((element: HTMLElement) => {
      this.renderer.setAttribute(element, 'tabindex', '-1');
    });
  }

  ngOnDestroy() {
    this.observer.disconnect();
  }
}
