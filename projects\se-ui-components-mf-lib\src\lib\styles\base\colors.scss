@use 'sass:map';

$colors: (
  // BLACK & WHITE
  'white': #ffffff,
  'black': #000000,

  // PRIMARY
  'primary-action': #106bc4,
  'primary-link': #a81b8d,

  // BLUE
  'blue-800': #003366,
  'blue-700': #002c57,
  'blue-600': #004e9b,
  'blue-500': #106bc4,
  'blue-400': #77a8cc,
  'blue-300': #cbe3f4,
  'blue-200': #ebf6ff,
  'blue-100': #f6fafe,

  // PINK
  'pink-700': #550b47,
  'pink-600': #80146c,
  'pink-500': #a81b8d,
  'pink-400': #b955a6,
  'pink-300': #ffa0ed,
  'pink-200': #fbdaf5,
  'pink-100': #fcedf9,

  // GRAY
  'gray-700': #333333,
  'gray-650': #454545,
  'gray-600': #666666,
  'gray-550': #757575,
  'gray-500': #999999,
  'gray-400': #b6b6b6,
  'gray-300': #dddddd,
  'gray-200': #f7f7f7,
  'gray-100': #f8f8f8,

  // RED
  'red-500': #a30014,
  'red-400': #d0021b,
  'red-300': #db3348,
  'red-200': #f0b2ba,
  'red-100': #ffd1d7,
  'red-50': #ffeff1,

  // ORANGE
  'orange-500': #7a4b00,
  'orange-400': #916630,
  'orange-300': #ff8d00,
  'orange-200': #ffc57a,
  'orange-100': #ffebcc,
  'orange-50': #fff5ef,

  // GREEN
  'green-400': #006125,
  'green-300': #018935,
  'green-200': #a1d3b6,
  'green-100': #dcefe3,
  'green-50': #f1ffe8,

  // PINK
  'purple-500': #32248e,
  'purple-400': #412fb7,
  'purple-300': #6354c4,
  'purple-200': #c6c4f3,
  'purple-100': #eeebff
);

:root {
  @each $name, $color in $colors {
    --color-#{$name}: #{$color};
  }
}

/* Text color utility classes */
@each $name in map.keys($colors) {
  .text-#{$name} {
    color: var(--color-#{$name}) !important;
  }
}

/* Background color utility classes */
@each $name in map.keys($colors) {
  .bg-#{$name} {
    background-color: var(--color-#{$name}) !important;
  }
}
