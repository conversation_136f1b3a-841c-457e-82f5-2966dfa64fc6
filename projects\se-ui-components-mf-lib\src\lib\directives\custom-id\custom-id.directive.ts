import { AfterViewInit, Directive, ElementRef, Input } from '@angular/core';
import { CustomIdInput } from './custom-id.model';

@Directive({
  selector: '[customId]',
})
export class SeCustomIdDirective implements AfterViewInit {
  
  @Input('customId') customIdInput!: CustomIdInput;
  
  constructor(
    private el: ElementRef
  ) {}

  ngAfterViewInit(): void {
    setTimeout(() => {
      const htmlElement = this.el.nativeElement.querySelector(
        this.customIdInput.querySelector
      ) as HTMLButtonElement;
      if (htmlElement) {
        htmlElement.id = this.customIdInput.id;
      }
    },0)
  }
}
