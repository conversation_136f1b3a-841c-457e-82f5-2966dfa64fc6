import {
  Component,
  EventEmitter,
  Input,
  Output,
  type OnDestroy,
} from '@angular/core';
import { Subject } from 'rxjs';

import type { Nullable } from '../../models';
import type { SeHeaderInfoItem, TagProps } from './models';
import { SeButtonDropdown } from '../button-dropdown';
import { SeHeaderInfoCustomButton } from './models/custom-button-props.model';
import { SeButtonThemeEnum } from '../button';

@Component({
  selector: 'se-header-info',
  templateUrl: './header-info.component.html',
  styleUrls: ['./header-info.component.scss'],
})
export class HeaderInfoComponent implements OnDestroy {

  infoItemsList: SeHeaderInfoItem[][] = [];
  SeButtonTheme = SeButtonThemeEnum;

  @Input() title: Nullable<string> = '';
  @Input() showHelp: Nullable<boolean> = false;
  @Input() buttonDropdown: SeButtonDropdown | undefined;
  @Input() customButton: SeHeaderInfoCustomButton | undefined;
  @Input() expanded: Nullable<boolean> = false;
  @Input() set infoItems(newInfoItems: Nullable<Nullable<SeHeaderInfoItem>[]> | Nullable<SeHeaderInfoItem[][]>) {
    this.infoItemsList = [[]]
    if (newInfoItems?.length) {
      if (newInfoItems[0] instanceof Array) {
        this.infoItemsList = newInfoItems as SeHeaderInfoItem[][]
      } else {
        this.infoItemsList = [[], []]
        this.infoItemsList[0] = [...(newInfoItems ?? [])]
          .slice(0, 3)
          .filter(Boolean) as SeHeaderInfoItem[];
        this.infoItemsList[1] = [...(newInfoItems ?? [])]
          .slice(3)
          .filter(Boolean) as SeHeaderInfoItem[];
      }
    }
  }

  @Input() set tags(newTags: Nullable<Nullable<TagProps>[]>) {
    this._tags = (newTags ?? []).filter(Boolean);
  }

  get tags(): Nullable<TagProps>[] {
    return this._tags;
  }

  @Output() helpButtonClick = new EventEmitter<MouseEvent>();

  protected infoItemsFirstRow: SeHeaderInfoItem[] = [];
  protected infoItemsSecondRow: SeHeaderInfoItem[] = [];
  protected _tags: Nullable<TagProps>[] = [];

  private destroyed$ = new Subject<void>();

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.unsubscribe();
  }

  protected get disclosureIconName(): string {
    return this.expanded
      ? 'matKeyboardArrowUpOutline'
      : 'matKeyboardArrowDownOutline';
  }

  protected get hasInfoItems(): boolean {
    return this.infoItemsList.length > 0 && this.infoItemsList.some((item: SeHeaderInfoItem[]) => item && item.length > 0);
  }

  protected get hasTags(): boolean {
    return (this.tags ?? []).length > 0;
  }
}
