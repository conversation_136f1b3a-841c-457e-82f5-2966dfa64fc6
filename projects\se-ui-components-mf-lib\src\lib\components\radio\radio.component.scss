.radio-container {
  display: inline-flex;
  flex-direction: column;

  label {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    font-family: var(--font-primary);

    &.disabled {
      cursor: not-allowed;

      .radio-label {
        color: var(--color-gray-550);
      }

      .radio-icon-container .radio-icon {
        color: var(--color-gray-400);
      }
    }

    &:not(.disabled) {
      &:hover .radio-icon-container .radio-icon {
        color: var(--color-blue-600);
      }      
    }

    .radio-icon-container {
      width:  var(--line-sm);
      height: var(--line-sm);
      height: var(--line-sm);
      line-height: var(--line-sm);

      .radio-icon {
        color: var(--color-gray-550);
        background: white;
        border-radius: 100%;
      }

      .radio-icon-checked {
        color: var(--color-primary-action);
      }
    }

    .radio-label {
      color: var(--color-gray-700);
      margin-left: 4px;
      pointer-events: none; 
    }
  }

  .radio-subtitle {
    margin-left: 24px;
    margin-top: 4px;
    color: var(--color-gray-600);

    &.disabled {
      cursor: not-allowed;
      opacity: 0.6;
      color: var(--color-gray-550)
    }
  }
}

//INLINE
::ng-deep se-radio + se-radio > .radio-container {
  margin-left: 1rem;
}