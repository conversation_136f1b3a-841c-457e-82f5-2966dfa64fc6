import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { SeExceptionViewerModule } from './exception-viewer.module';
import { SeExceptionViewerComponent } from './exception-viewer.component';
import { 
  SeExceptionViewerAction, 
  SeExceptionViewerEvent, 
  SeMessageI, 
  SeMessageService
} from 'projects/se-ui-components-mf-lib/src/public-api';
import { Component } from '@angular/core';

@Component({
  selector:'se-exception-viewer-button-example',
  template: `
    <button (click)="addException(0)">Add exception 1</button>
    <button (click)="addException(1)">Add exception 2</button>
  `
})
class ExceptionExampleButtonComponent {

  constructor(private messageService: SeMessageService) { }

  addException = (index: number) => {
    const exceptions: SeMessageI[] = [
      {
        severity: 'error',
        title: 'Error',
        subtitle: 'COMMONS.DALI.EN003_ERROR',
        subtitleParams: { codiError: 'EN003' },
        closable: true,
      },
      {
        severity: 'error',
        title: 'Error',
        subtitle: 'COMMONS.DALI.EN004_ERROR',
        subtitleParams: { codiError: 'EN004' },
        closable: true,
      },
    ];
    const eventDetail: SeExceptionViewerEvent = {
      action: SeExceptionViewerAction.ADD,
      messages: exceptions,
    };

    this.messageService.addMessages([exceptions[index]])

    // document.dispatchEvent(
    //   new CustomEvent('exceptionsEvent', { detail: eventDetail })
    // );
  };
}

const meta: Meta<SeExceptionViewerComponent> = {
  title: 'Components/Exception Viewer',
  component: SeExceptionViewerComponent,
  decorators: [
    moduleMetadata({
      declarations: [ExceptionExampleButtonComponent],
      imports: [SeExceptionViewerModule],
    }),
  ],
  render: (args) => ({
    template: `
    <div style="max-width: 850px">
      <se-exception-viewer></se-exception-viewer>
    </div>    

    <se-exception-viewer-button-example></se-exception-viewer-button-example>

    `,//<button (click)="addException()">Add exception</button>
    // props: {
    //   addException: () => {
    //     const exceptions: SeMessageI[] = [
    //       {
    //         severity: 'error',
    //         title: 'Error',
    //         subtitle: 'COMMONS.DALI.EN003_ERROR',
    //         subtitleParams: { codiError: 'EN003' },
    //         closable: true,
    //       },
    //       {
    //         severity: 'error',
    //         title: 'Error',
    //         subtitle: 'COMMONS.DALI.EN004_ERROR',
    //         subtitleParams: { codiError: 'EN004' },
    //         closable: true,
    //       },
    //     ];
    //     const eventDetail: SeExceptionViewerEvent = {
    //       action: SeExceptionViewerAction.ADD,
    //       messages: exceptions,
    //     };

    //     document.dispatchEvent(
    //       new CustomEvent('exceptionsEvent', { detail: eventDetail })
    //     );
    //   },
    // },
  }),

  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<SeExceptionViewerComponent>;

export const DefaultInput: Story = {};
