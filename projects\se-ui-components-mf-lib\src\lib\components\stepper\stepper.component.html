<div class="se-stepper">
  <!-- MOBILE -->
  <div class="se-stepper__mobile" *ngIf="currentStepMobileIndex > -1">
    <span class="align-self-center">
      <span class="se-stepper__mobile-on">{{currentStepMobileIndex + 1}}</span>/{{steps.length}}
    </span>
    <div class="se-stepper__mobile__label">
      <span>{{steps[currentStepMobileIndex].label}}</span>
      <span *ngIf="nextStepMobileIndex > -1"
        class="se-stepper__mobile__label-next">
        {{steps[nextStepMobileIndex].label}}
      </span>
    </div>
    <button (click)="toggleMobileCard()" class="se-stepper__mobile__button">
      <ng-icon class="_icon" [name]="accordionArrowIcon"></ng-icon>
    </button>
  </div>
  <!-- DESKTOP -->
  <div class="se-stepper__desktop">
    <ng-container *ngTemplateOutlet="longStepper"></ng-container>
  </div>
  <p-progressBar [ariaLabel]="progressBarAriaLabel" class="se-stepper__progress" [value]="progressValue()">
  </p-progressBar>
  <ng-container *ngIf="openCardMobile">
    <p-card class="se-stepper__mobile__card">
      <ng-container *ngTemplateOutlet="longStepper"></ng-container>
    </p-card>
  </ng-container>
</div>

<ng-template #longStepper>
  <div *ngFor="let step of steps; let i = index" class="se-step">
    <div class="se-step__container" [ngClass]="{'se-step__pointer': step.status === SeStepStatus.PAST && canNavigate}" (click)="onClick($event, step)">
      <ng-container *ngIf="step.status !== SeStepStatus.PAST; else checkIcon">
        <div class="se-step__circle" [ngClass]="'se-step__circle-'+statusClass[step.status!]"></div>
      </ng-container>
      <ng-template #checkIcon>
        <ng-icon class="past-icon" name="matCheckOutline"></ng-icon>
      </ng-template>
      <p class="se-step__label text-sm" [ngClass]="'se-step__label-'+statusClass[step.status!]"
        [pTooltip]="step.tooltipText" tooltipPosition="top">
        {{ step.label }}
      </p>
    </div>
    <div class="se-step__line" [ngClass]="'se-step__line-'+statusClass[step.status!]"></div>
  </div>
</ng-template>