import {
  AfterContentInit,
  AfterViewInit,
  ContentChildren,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  QueryList,
  ViewChild,
} from '@angular/core';
import { Component } from '@angular/core';
import { TabItemComponent } from '../tab-item/tab-item.component';

@Component({
  selector: 'se-tab-view',
  template: `
    <div class="se-tab-view">
      <div class="se-tab-view--header">
        <!-- boton para desplegar a la izquierda -->
        <button
          class="se-tab-view--header--button left-paddle"
          *ngIf="showLeftPaddle"
          (click)="scrollTab(-1)"
        >
          <ng-icon name="matArrowBackIosNewOutline" size="24px"></ng-icon>
        </button>

        <!-- tabs normal -->
        <div
          [ngClass]="{
            'left-paddle': showLeftPaddle,
            'right-paddle': showRightPaddle
          }"
          class="tab-header"
          #tabHeader
          role="tablist"
        >
          <se-tab-header
            *ngFor="let tab of tabItems; let i = index"
            [label]="tab.label"
            [showBadge]="tab.showBadge"
            [badgeColor]="tab.badgeColor"
            [badgeTextColor]="tab.badgeTextColor"
            [badgeTheme]="tab.badgeTheme"
            [badgeTextInsideCircle]="tab.badgeTextInsideCircle"
            [isActive]="isVisible(tab, i)"
            [disabled]="tab.disabled"
            (onChange)="setActiveTab(tab, i)"
          >
          </se-tab-header>
        </div>

        <!-- boton para desplegar a la derecha -->
        <button
          class="se-tab-view--header--button right-paddle"
          *ngIf="showRightPaddle && isOverflowing()"
          (click)="scrollTab(1)"
        >
          <ng-icon
            class="rotate-180"
            name="matArrowBackIosNewOutline"
            size="24px"
          ></ng-icon>
        </button>
      </div>

      <div class="tab-content">
        <ng-container *ngFor="let tab of tabItems; let i = index">
          <div *ngIf="!tab.disabled" [class.d-none]="!isVisible(tab, i)">
            <ng-container *ngTemplateOutlet="tab.content"></ng-container>
          </div>
        </ng-container>
      </div>
    </div>
  `,
  styleUrls: ['./tab-view.component.scss'],
})
export class TabViewComponent implements AfterContentInit, AfterViewInit {
  @ContentChildren(TabItemComponent) tabItems!: QueryList<TabItemComponent>;

  @ViewChild('tabHeader') tabHeader: ElementRef | undefined;

  @Input() activeTabIndex: number = 0;

  @Output() onChange = new EventEmitter<number>();

  showLeftPaddle = false;
  showRightPaddle = true;

  get tabHeaderContent(): any | undefined {
    return this.tabHeader?.nativeElement;
  }

  ngAfterContentInit(): void {
    if (this.tabItems && this.tabItems.first) {
      if (this.activeTabIndex == undefined) {
        this.activeTabIndex = 0;
      }
    }
  }

  ngAfterViewInit(): void {
    if (this.tabHeaderContent) this.setPositionHeader(this.activeTabIndex);
  }

  private setPositionHeader(index: number): void {
    // scrollWidth, tamaño del scroll que tiene los tabs
    // clientWidth, tamaño del component tabHeader sin hacer scroll en caso se necesitara
    // scrollLeft, posicion actual del scroll

    let scroll = 0;
    // tiene que tomar el tamaño final del tab previo al seleccionado.
    for (let i = 0; i < index; i++) {
      scroll += this.tabHeaderContent.children[i].clientWidth;
      // clientWidth, tamaño del tab seleccionado
    }
    this.tabHeaderContent.scrollLeft = scroll;

    const isScrollWidthEqualClientWidth =
      this.tabHeaderContent.scrollWidth === this.tabHeaderContent.clientWidth;

    this.showLeftPaddle = !isScrollWidthEqualClientWidth && scroll > 0;

    // *si es el ultimo tab, que no se muestre
    this.showRightPaddle =
      index === this.tabItems.length - 1
        ? false
        : scroll < this.tabHeaderContent.scrollWidth;
  }

  protected setActiveTab(tab: TabItemComponent, index: number): void {
    this.activeTabIndex = this.getIndexTab(tab, index);
    this.setPositionHeader(this.activeTabIndex);
    this.onChange.emit(this.activeTabIndex);
  }

  protected scrollTab(index: number): void {
    const scroll = index * 200;
    if (this.tabHeaderContent) {
      this.tabHeaderContent.scrollLeft += scroll;
      this.showLeftPaddle = this.tabHeaderContent.scrollLeft + scroll > 0;
      this.showRightPaddle =
        this.tabHeaderContent.scrollLeft +
          scroll +
          40 +
          this.tabHeaderContent.clientWidth <
        this.tabHeaderContent.scrollWidth;
    }
  }

  protected isOverflowing(): boolean {
    return (
      this.tabHeaderContent?.clientWidth < this.tabHeaderContent?.scrollWidth
    );
  }

  protected isVisible(tab: TabItemComponent, index: number): boolean {
    return this.getIndexTab(tab, index) === this.activeTabIndex;
  }

  protected getIndexTab(tab: TabItemComponent, index: number): number {
    return tab.index ?? index;
  }
}
