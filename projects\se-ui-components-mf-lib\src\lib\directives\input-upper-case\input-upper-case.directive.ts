import {
  Directive,
  HostListener,
} from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
  selector: '[seInputUpperCase]',
})
export class SeInputUpperCaseDirective {
  constructor(
    private readonly control: NgControl
  ) { }

  @HostListener('input', ['$event.target'])
  public onInput(input: HTMLInputElement): void {
    const cursorPos = input.selectionStart;
    this.control?.control?.setValue(input.value.toUpperCase(), { emitEvent: false });
    setTimeout(() => {
      input.setSelectionRange(cursorPos, cursorPos);
    }, 0)
  }
}
