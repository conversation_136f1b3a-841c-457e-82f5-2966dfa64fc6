export interface SeModal {
  severity?: 'info' | 'warning' | 'error' | 'success';
  title?: string;
  subtitle?: string;
  subtitleStyles?: { [key: string]: string };
  backdrop?: boolean | 'static';
  keyboard?: boolean;
  centered?: boolean;
  closable?: boolean;
  closableLabel?: string;
  closableDisabled?: boolean;
  secondaryButton?: boolean;
  secondaryButtonLabel?: string;
  titleParams?: {};
  subtitleParams?: {};
  titleTextWeight?: 'regular' | 'semi-bold' | 'bold';
  size?: 'sm' | 'lg' | 'xl' | 'fullscreen' | 'sidebar';
  component?: any,
  windowClass?: string;
  hideIcon?: boolean;
}

export interface SeModalSeverityClass {
  class: string;
  icon: string;
}

export enum SeModalOutputEvents {
  MAIN_ACTION = 'mainAction',
  CLOSE = 'closeButtonAction',
  SECONDARY_ACTION = 'secondaryAction'
}