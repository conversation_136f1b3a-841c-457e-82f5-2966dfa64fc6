import { Component, HostListener } from '@angular/core';
import {
  SeExceptionViewerAction,
  SeExceptionViewerEvent,
  SeMessage,
  SeMessageI,
  SeMessageService,
} from '../../services';

@Component({
  selector: 'se-exception-viewer',
  templateUrl: './exception-viewer.component.html',
  styleUrls: ['./exception-viewer.component.scss'],
})
export class SeExceptionViewerComponent {

  exceptions: SeMessage[] = [];

  constructor(
    private seMessageService: SeMessageService
  ) { }

  closeError = (item: SeMessage): void => {
    if(item.closableFn){
      item.closableFn(item);
    } else {
      this.seMessageService.deleteMessage(item);
    }
  }

  protected addMessages = (exceptions: SeMessageI[]): SeMessage[] => {
    // Add new messages
    const newMessages: SeMessage[] = exceptions || [];
    return this.exceptions.concat(
      newMessages.filter((newMessage) => {
        newMessage.closableFn = this.seMessageService.deleteMessage.bind(this);
        return !this.seMessageService.isDuplicated(this.exceptions, newMessage);
      })
    );
  };

  // Delete one message
  protected deleteMessage = (item: SeMessage): SeMessage[] => {
    return this.exceptions.filter(
        (exception: SeMessage) => !this.seMessageService.compareMsg(exception, item)
      );
  }

  // Recieve message with the event: exceptionsEvent
  @HostListener('document:exceptionsEvent', ['$event'])
  exceptionsEventFn(ev: CustomEvent) {
    const eventDetail: SeExceptionViewerEvent = ev?.detail;
    const exceptions = eventDetail?.messages || [];

    switch(eventDetail.action) {
      case SeExceptionViewerAction.ADD:
        this.exceptions = this.addMessages(exceptions);
        break;
      
      case SeExceptionViewerAction.DELETE:
        this.exceptions = this.deleteMessage(exceptions[0]);
        break;

      case SeExceptionViewerAction.RESET:
        this.exceptions = [];
        break;
    }
  }
}
