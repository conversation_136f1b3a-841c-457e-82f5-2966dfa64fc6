(self.webpackChunkse_ui_components_mf_lib=self.webpackChunkse_ui_components_mf_lib||[]).push([[599],{"./projects/se-ui-components-mf-lib/src/lib/components/textarea/textarea.stories.ts":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Textarea:()=>Textarea,default:()=>textarea_stories});var _class,TextareaComponent_1,fesm2022_forms=__webpack_require__("./node_modules/@angular/forms/fesm2022/forms.mjs"),dist=__webpack_require__("./node_modules/@storybook/angular/dist/index.mjs"),tslib_es6=__webpack_require__("./node_modules/tslib/tslib.es6.mjs"),textarea_component=__webpack_require__("./projects/se-ui-components-mf-lib/src/lib/components/textarea/textarea.component.ts.css?ngResource!=!./node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=CiAgICAgIC50ZXh0YXJlYS1jb250YWluZXIgewogICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAgICAgfQoKICAgICAgbGFiZWwgewogICAgICAgIGZvbnQtZmFtaWx5OiBPcGVuIFNhbnM7CiAgICAgICAgZm9udC1zaXplOiAxM3B4OwogICAgICAgIGZvbnQtc3R5bGU6IG5vcm1hbDsKICAgICAgICBmb250LXdlaWdodDogNDAwOwogICAgICAgIGxpbmUtaGVpZ2h0OiAyMHB4OwogICAgICAgIG1hcmdpbi10b3A6IDRweDsKICAgICAgfQoKICAgICAgLnJlYWRvbmx5LXRleHQgewogICAgICAgIGZvbnQtZmFtaWx5OiBPcGVuIFNhbnM7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIGZvbnQtc3R5bGU6IG5vcm1hbDsKICAgICAgICBmb250LXdlaWdodDogNDAwOwogICAgICAgIGxpbmUtaGVpZ2h0OiAyMHB4OwogICAgICB9CgogICAgICAudGV4dGFyZWEtd3JhcHBlciB7CiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgICB9CgogICAgICAudGV4dGFyZWEtd3JhcHBlciB0ZXh0YXJlYSB7CiAgICAgICAgcGFkZGluZzogMTBweDsKICAgICAgICBwYWRkaW5nLXJpZ2h0OiAzMHB4OwogICAgICAgIGJhY2tncm91bmQ6IHdoaXRlOwogICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICNiNmI2YjY7CiAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgICAgcmVzaXplOiBib3RoOwogICAgICAgIHdpZHRoOiAxMDAlOwogICAgICB9CgogICAgICAudGV4dGFyZWEtd3JhcHBlciB0ZXh0YXJlYTpmb2N1cyB7CiAgICAgICAgb3V0bGluZS1jb2xvcjogIzEwNmJjNDsKICAgICAgfQoKICAgICAgLnRleHRhcmVhLXdyYXBwZXIgdGV4dGFyZWEuZXJyb3IgewogICAgICAgIG91dGxpbmUtY29sb3I6ICNkMDAyMWI7CiAgICAgIH0KCiAgICAgIC50ZXh0YXJlYS13cmFwcGVyIHRleHRhcmVhLmVycm9yOmZvY3VzIHsKICAgICAgICBvdXRsaW5lLWNvbG9yOiAjZDAwMjFiOwogICAgICB9CgogICAgICAuY2xlYXItYnV0dG9uIHsKICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgICAgdG9wOiA1cHg7CiAgICAgICAgcmlnaHQ6IDVweDsKICAgICAgICB3aWR0aDogMTZweDsKICAgICAgICBoZWlnaHQ6IDE2cHg7CiAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7CiAgICAgICAgYm9yZGVyOiBub25lOwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICBmb250LXNpemU6IDE2cHg7CiAgICAgICAgei1pbmRleDogMTsKICAgICAgfQoKICAgICAgLnRleHRhcmVhLXdyYXBwZXIuZGlzYWJsZWQgdGV4dGFyZWEgewogICAgICAgIGJhY2tncm91bmQ6ICNmNWY1ZjU7CiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2I2YjZiNjsKICAgICAgfQoKICAgICAgLnRleHRhcmVhLXdyYXBwZXIuZGlzYWJsZWQgLmNsZWFyLWJ1dHRvbiB7CiAgICAgICAgZGlzcGxheTogbm9uZTsKICAgICAgfQogICAg!./projects/se-ui-components-mf-lib/src/lib/components/textarea/textarea.component.ts"),textarea_component_default=__webpack_require__.n(textarea_component),core=__webpack_require__("./node_modules/@angular/core/fesm2022/core.mjs");let TextareaComponent=(TextareaComponent_1=_class=class TextareaComponent{constructor(renderer){this.renderer=renderer,this.label="",this.id="",this.readonly=!1,this.disabled=!1,this.error=!1,this.value="",this.focus=!1,this.onChange=()=>{},this.onTouched=()=>{}}ngAfterViewInit(){new ResizeObserver((()=>{this.positionClearButton()})).observe(this.textareaElement.nativeElement)}positionClearButton(){const top=this.textareaElement.nativeElement.offsetTop+5,left=this.textareaElement.nativeElement.offsetWidth-28;this.renderer.setStyle(this.clearButton.nativeElement,"top",`${top}px`),this.renderer.setStyle(this.clearButton.nativeElement,"left",`${left}px`)}onInput(value){this.value=value,this.onChange(value),this.onTouched()}clear(){this.value="",this.textareaElement.nativeElement.value="",this.onChange(this.value),this.onTouched()}writeValue(value){this.value=value}registerOnChange(fn){this.onChange=fn}registerOnTouched(fn){this.onTouched=fn}setDisabledState(isDisabled){this.disabled=isDisabled}},_class.ctorParameters=()=>[{type:core.Renderer2}],_class.propDecorators={clearButton:[{type:core.ViewChild,args:["clearButton"]}],textareaElement:[{type:core.ViewChild,args:["textareaElement"]}],label:[{type:core.Input}],id:[{type:core.Input}],readonly:[{type:core.Input}],disabled:[{type:core.Input}],error:[{type:core.Input}]},_class);TextareaComponent=TextareaComponent_1=(0,tslib_es6.gn)([(0,core.Component)({selector:"se-textarea",template:'\n    <div class="textarea-container">\n      <label [for]="id">{{ label }}</label>\n      <ng-container *ngIf="readonly; else textareaBlock">\n        <p class="readonly-text">{{ value }}</p>\n      </ng-container>\n      <ng-template #textareaBlock>\n        <div class="textarea-wrapper" [class.disabled]="disabled">\n          <textarea\n            #textareaElement\n            [attr.id]="id"\n            [class.error]="error"\n            (input)="onInput($event.target.value)"\n            (focus)="focus = true"\n            (blur)="focus = false"\n            [readonly]="readonly"\n            [disabled]="disabled"\n          ></textarea>\n          <button\n            #clearButton\n            [hidden]="!value"\n            class="clear-button"\n            (click)="clear()"\n            title="Clear"\n          >\n            &#10005;\n          </button>\n        </div>\n      </ng-template>\n    </div>\n  ',providers:[{provide:fesm2022_forms.JU,useExisting:(0,core.forwardRef)((()=>TextareaComponent_1)),multi:!0}],styles:[textarea_component_default()]})],TextareaComponent);var common=__webpack_require__("./node_modules/@angular/common/fesm2022/common.mjs");let SeTextareaModule=class SeTextareaModule{};SeTextareaModule=(0,tslib_es6.gn)([(0,core.NgModule)({declarations:[TextareaComponent],imports:[common.CommonModule,fesm2022_forms.UX,fesm2022_forms.u5],exports:[TextareaComponent]})],SeTextareaModule);const textarea_stories={title:"Components/Textarea",component:TextareaComponent,tags:["autodocs"],decorators:[(0,dist.moduleMetadata)({imports:[SeTextareaModule,fesm2022_forms.UX]})],args:{label:"Textarea label",disabled:!1,error:!1,readonly:!1},argTypes:{disabled:{description:"Determines if the textarea is disabled or not.",control:{type:"boolean"},table:{defaultValue:{summary:!1}}},error:{description:"Indicates if there is an error in the textarea.",control:{type:"boolean"},table:{defaultValue:{summary:!1}}},readonly:{description:"Indicates if the textarea is in read-only mode.",control:{type:"boolean"},table:{defaultValue:{summary:!1}}}},render:args=>({template:'\n      <form [formGroup]="form">\n        <se-textarea\n          [label]="label"\n          [readonly]="readonly"\n          [error]="error"\n          [disabled]="disabled"\n          [formControl]="form.controls[\'value\']">\n        </se-textarea>\n      </form>\n    ',props:{...args,form:new fesm2022_forms.cw({value:new fesm2022_forms.NI({value:"",disabled:args.disabled})})}})},Textarea={}},"./node_modules/css-loader/dist/runtime/api.js":module=>{"use strict";module.exports=function(cssWithMappingToString){var list=[];return list.toString=function toString(){return this.map((function(item){var content="",needLayer=void 0!==item[5];return item[4]&&(content+="@supports (".concat(item[4],") {")),item[2]&&(content+="@media ".concat(item[2]," {")),needLayer&&(content+="@layer".concat(item[5].length>0?" ".concat(item[5]):""," {")),content+=cssWithMappingToString(item),needLayer&&(content+="}"),item[2]&&(content+="}"),item[4]&&(content+="}"),content})).join("")},list.i=function i(modules,media,dedupe,supports,layer){"string"==typeof modules&&(modules=[[null,modules,void 0]]);var alreadyImportedModules={};if(dedupe)for(var k=0;k<this.length;k++){var id=this[k][0];null!=id&&(alreadyImportedModules[id]=!0)}for(var _k=0;_k<modules.length;_k++){var item=[].concat(modules[_k]);dedupe&&alreadyImportedModules[item[0]]||(void 0!==layer&&(void 0===item[5]||(item[1]="@layer".concat(item[5].length>0?" ".concat(item[5]):""," {").concat(item[1],"}")),item[5]=layer),media&&(item[2]?(item[1]="@media ".concat(item[2]," {").concat(item[1],"}"),item[2]=media):item[2]=media),supports&&(item[4]?(item[1]="@supports (".concat(item[4],") {").concat(item[1],"}"),item[4]=supports):item[4]="".concat(supports)),list.push(item))}},list}},"./node_modules/css-loader/dist/runtime/noSourceMaps.js":module=>{"use strict";module.exports=function(i){return i[1]}},"./projects/se-ui-components-mf-lib/src/lib/components/textarea/textarea.component.ts.css?ngResource!=!./node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=CiAgICAgIC50ZXh0YXJlYS1jb250YWluZXIgewogICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAgICAgfQoKICAgICAgbGFiZWwgewogICAgICAgIGZvbnQtZmFtaWx5OiBPcGVuIFNhbnM7CiAgICAgICAgZm9udC1zaXplOiAxM3B4OwogICAgICAgIGZvbnQtc3R5bGU6IG5vcm1hbDsKICAgICAgICBmb250LXdlaWdodDogNDAwOwogICAgICAgIGxpbmUtaGVpZ2h0OiAyMHB4OwogICAgICAgIG1hcmdpbi10b3A6IDRweDsKICAgICAgfQoKICAgICAgLnJlYWRvbmx5LXRleHQgewogICAgICAgIGZvbnQtZmFtaWx5OiBPcGVuIFNhbnM7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIGZvbnQtc3R5bGU6IG5vcm1hbDsKICAgICAgICBmb250LXdlaWdodDogNDAwOwogICAgICAgIGxpbmUtaGVpZ2h0OiAyMHB4OwogICAgICB9CgogICAgICAudGV4dGFyZWEtd3JhcHBlciB7CiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgICB9CgogICAgICAudGV4dGFyZWEtd3JhcHBlciB0ZXh0YXJlYSB7CiAgICAgICAgcGFkZGluZzogMTBweDsKICAgICAgICBwYWRkaW5nLXJpZ2h0OiAzMHB4OwogICAgICAgIGJhY2tncm91bmQ6IHdoaXRlOwogICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICNiNmI2YjY7CiAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgICAgcmVzaXplOiBib3RoOwogICAgICAgIHdpZHRoOiAxMDAlOwogICAgICB9CgogICAgICAudGV4dGFyZWEtd3JhcHBlciB0ZXh0YXJlYTpmb2N1cyB7CiAgICAgICAgb3V0bGluZS1jb2xvcjogIzEwNmJjNDsKICAgICAgfQoKICAgICAgLnRleHRhcmVhLXdyYXBwZXIgdGV4dGFyZWEuZXJyb3IgewogICAgICAgIG91dGxpbmUtY29sb3I6ICNkMDAyMWI7CiAgICAgIH0KCiAgICAgIC50ZXh0YXJlYS13cmFwcGVyIHRleHRhcmVhLmVycm9yOmZvY3VzIHsKICAgICAgICBvdXRsaW5lLWNvbG9yOiAjZDAwMjFiOwogICAgICB9CgogICAgICAuY2xlYXItYnV0dG9uIHsKICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgICAgdG9wOiA1cHg7CiAgICAgICAgcmlnaHQ6IDVweDsKICAgICAgICB3aWR0aDogMTZweDsKICAgICAgICBoZWlnaHQ6IDE2cHg7CiAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7CiAgICAgICAgYm9yZGVyOiBub25lOwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICBmb250LXNpemU6IDE2cHg7CiAgICAgICAgei1pbmRleDogMTsKICAgICAgfQoKICAgICAgLnRleHRhcmVhLXdyYXBwZXIuZGlzYWJsZWQgdGV4dGFyZWEgewogICAgICAgIGJhY2tncm91bmQ6ICNmNWY1ZjU7CiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2I2YjZiNjsKICAgICAgfQoKICAgICAgLnRleHRhcmVhLXdyYXBwZXIuZGlzYWJsZWQgLmNsZWFyLWJ1dHRvbiB7CiAgICAgICAgZGlzcGxheTogbm9uZTsKICAgICAgfQogICAg!./projects/se-ui-components-mf-lib/src/lib/components/textarea/textarea.component.ts":(module,__unused_webpack_exports,__webpack_require__)=>{var ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___=__webpack_require__("./node_modules/css-loader/dist/runtime/noSourceMaps.js"),___CSS_LOADER_EXPORT___=__webpack_require__("./node_modules/css-loader/dist/runtime/api.js")(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);___CSS_LOADER_EXPORT___.push([module.id,"\n      .textarea-container {\n        position: relative;\n      }\n\n      label {\n        font-family: Open Sans;\n        font-size: 13px;\n        font-style: normal;\n        font-weight: 400;\n        line-height: 20px;\n        margin-top: 4px;\n      }\n\n      .readonly-text {\n        font-family: Open Sans;\n        font-size: 14px;\n        font-style: normal;\n        font-weight: 400;\n        line-height: 20px;\n      }\n\n      .textarea-wrapper {\n        position: relative;\n      }\n\n      .textarea-wrapper textarea {\n        padding: 10px;\n        padding-right: 30px;\n        background: white;\n        border: 2px solid #b6b6b6;\n        box-sizing: border-box;\n        border-radius: 4px;\n        resize: both;\n        width: 100%;\n      }\n\n      .textarea-wrapper textarea:focus {\n        outline-color: #106bc4;\n      }\n\n      .textarea-wrapper textarea.error {\n        outline-color: #d0021b;\n      }\n\n      .textarea-wrapper textarea.error:focus {\n        outline-color: #d0021b;\n      }\n\n      .clear-button {\n        position: absolute;\n        top: 5px;\n        right: 5px;\n        width: 16px;\n        height: 16px;\n        background: transparent;\n        border: none;\n        cursor: pointer;\n        font-size: 16px;\n        z-index: 1;\n      }\n\n      .textarea-wrapper.disabled textarea {\n        background: #f5f5f5;\n        border: 1px solid #b6b6b6;\n      }\n\n      .textarea-wrapper.disabled .clear-button {\n        display: none;\n      }\n    ",""]),module.exports=___CSS_LOADER_EXPORT___.toString()}}]);