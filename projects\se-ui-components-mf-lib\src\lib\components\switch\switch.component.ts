import {Component, EventEmitter, forwardRef, Input, Output} from '@angular/core';
import {ControlContainer, ControlValueAccessor, FormControl, FormGroup, NG_VALUE_ACCESSOR} from '@angular/forms';

@Component({
  selector: 'se-switch',
  styleUrls: ['switch.component.scss'],
  template: `
    <span *ngIf="title" class="title-switch">{{ title }}</span>
    <div
      class="switch-container"
      >
      <div class="switch-container__label" [ngClass]="labelPosition">
        <label
          (click)="toggle()"
          [for]="id"
          [ngClass]="{
          'disabled': disabled,
          'bold': value,
          }"
          [innerHTML]="label | safeHtml">
        </label>
        <ng-icon class="tooltip-icon" *ngIf="tooltip" name="matInfo" [pTooltipAccessible]="tooltipText"></ng-icon>
        <span *ngIf="subtitle" class="subtitle-switch" [innerHTML]="subtitle | safeHtml"></span>
      </div>
      <div
        class="switch"
        (mouseenter)="hover = true"
        (mouseleave)="hover = false">
        <input
          type="checkbox"
          [attr.id]="id"
          [checked]="value"
        />
        <div
          class="slider"
          (click)="toggle()"
          [ngClass]="{
            disabled: disabled,
            checked: value,
            focus: focus && !disabled,
            hover: hover && !disabled
          }"
          (focus)="focus = true"
          (blur)="focus = false"
          (keyup.enter)="toggle()"
          tabindex="0">
          <div class="toggle-ball" [class.checked]="value">
            <ng-icon [ngClass]="{ disabled: disabled }" class="switch-icon" *ngIf="value" name="matCheckOutline"></ng-icon>
          </div>
        </div>
      </div>
    </div>
  `,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SwitchComponent),
      multi: true,
    },
  ],
})
export class SwitchComponent implements ControlValueAccessor {

  value: boolean = false;
  focus: boolean = false;
  hover: boolean = false;
  control!: FormControl;

  @Input() title: string | undefined;
  @Input() subtitle: string | undefined;
  @Input() label = '';
  @Input() labelPosition: 'left' | 'right' = 'right';
  @Input() id!: string;
  @Input() formControlName!: string;
  @Input() tooltip = false;
  @Input() tooltipText = '';

  @Input() set disabled(value: boolean) {
    this.setDisabledState(value);
    if(!value) {
      this.getFormControl()?.enable({ emitEvent: true });
    } else {
      this.getFormControl()?.disable({ emitEvent: true });
    }
  }

  get disabled() { return this._disabled };

  @Output() onToggle: EventEmitter<boolean> = new EventEmitter<boolean>();

  private _disabled: boolean = false;
  private onChange: (value: any) => void = () => { };
  private onTouched: () => void = () => { };

  constructor(private controlContainer: ControlContainer) { }

  ngOnInit() {
    this.control = this.getFormControl();

    this._disabled = this.control.disabled || this.disabled;

    this.control.registerOnDisabledChange((value) => { this.setDisabledState(value) })
  }

  toggle() {
    if (this.disabled) return;
    this.value = !this.value;
    this.onChange(this.value);
    this.onTouched();
    this.onToggle.emit(this.value);
  }

  writeValue(value: any): void {
    this.value = value;
  }

  registerOnChange(fn: (value: any) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this._disabled = isDisabled;
  }

  private getFormControl() : FormControl {
    if(this.control){
      return this.control;
    }

    return (this.controlContainer.control as FormGroup).get(
      this.formControlName
    ) as FormControl;
  }
}
