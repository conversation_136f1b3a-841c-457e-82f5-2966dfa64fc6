import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { FormGroup, FormControl, ReactiveFormsModule } from '@angular/forms';
import { HighlightRadioContainerDirective } from './highlight-radio-container.directive';
import { SeInputModule, SeRadioModule } from '../../components';
import { SeHighlightRadioContainerModule } from './highlight-radio-container.module';

const meta: Meta<HighlightRadioContainerDirective> = {
  title: 'Directives/Highlight Radio Container',
  component: HighlightRadioContainerDirective,
  decorators: [
    moduleMetadata({
      imports: [
        ReactiveFormsModule,
        SeRadioModule,
        SeInputModule,
        SeHighlightRadioContainerModule,
      ],
    }),
  ],
};

export default meta;
type Story = StoryObj<HighlightRadioContainerDirective>;

export const Default: Story = {
  render: (args) => ({
    props: {
      ...args,
      form: new FormGroup({
        radioValue: new FormControl('1'),
        inputValue: new FormControl(''),
      }),
    },
    template: `
      <form [formGroup]="form">
        <div seHighlightRadioContainer>
          <se-radio
            formControlName="radioValue"
            id="radio-1"
            name="radioValue"
            label="Option 1"
            value="1"
          ></se-radio>
          <div style="margin-top: 16px">          
            <se-input formControlName="inputValue" id="input-id" label="Input"></se-input>
          </div>
        </div>

        <div seHighlightRadioContainer style="margin-top: 16px;">
          <se-radio
            formControlName="radioValue"
            id="radio-1"
            name="radioValue"
            label="Option 2"
            value="2"
          ></se-radio>
        </div>
      </form>
    `,
  }),
};
