import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import { CellComponent, DateCellConfig, FlattenedCell } from '../cells.model';

@Component({
  selector: 'se-date-cell',
  template: `
    <span [ngStyle]="cellConfig['ngStyle']">
      {{ value | date : dateFormat }}
    </span>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DateCellComponent implements CellComponent {
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: DateCellConfig;

  get dateFormat(): string {
    return this.cellConfig?.dateFormat ?? 'dd/MM/yyyy';
  }
}
