:host ::ng-deep .p-progressbar-label {
  font-family: var(--font-primary) !important;
}
.se-progress-modal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: var(--font-primary);
  background-color: white;
  padding: 0 40px;
  min-height: 300px;

  &__spinner-container {
    margin-bottom: 16px;
    
    .lds-ring {
      display: inline-block;
      position: relative;
      width: 80px;
      height: 80px;

      .ring-segment {
        box-sizing: border-box;
        display: block;
        position: absolute;
        width: 64px;
        height: 64px;
        margin: 8px;
        border: 8px solid var(--color-primary-action);
        border-radius: 50%;
        animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
        border-color: var(--color-primary-action) transparent transparent transparent;
      }

      .ring-segment:nth-child(1) {
        animation-delay: -0.45s;
      }
      .ring-segment:nth-child(2) {
        animation-delay: -0.3s;
      }
      .ring-segment:nth-child(3) {
        animation-delay: -0.15s;
      }
    }
  }

  &__text-container {
    text-align: center;

    > .message {
      font-style: italic;
      color: var(--color-gray-700);
    }
  }
}

::ng-deep {
  .modal.fade {
    transition: opacity 0.05s linear;

    > .modal-dialog {
      transition: transform 0.15s ease-out;
    }
  }
}

@keyframes lds-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}