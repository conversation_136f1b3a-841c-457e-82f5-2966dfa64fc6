:host {

  .se-upload-files {
    .drop-area__container {
      width: 100%;
    }

    .drop-area--border {
      min-height: 120px;
      border: dashed 1px var(--color-gray-700);
      background-image: none;
      border-radius: 4px;
    }

    .drop-area {
      width: 100%;
      padding: 12px;
      display: flex;
      justify-content: center;
      align-items: center;

      &.disabled {
        cursor: not-allowed;
        opacity: 0.8;

        &:hover,
        &:focus {
          background-color: transparent;
        }

        .link-text-disabled{
          color: var(--color-gray-600);
        }
      }
    }

    .drop-area--highlight {
      background-color: var(--color-blue-200);
    }

    &__form {
      display: flex;
      text-align: center;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: var(--color-gray-700);
    }

    .blue {
      color: var(--color-blue-500);
    }

    .blue-icon {
      color: var(--color-blue-500);
      width: 20px;
      height: 20px;
      align-items: center;
      justify-content: center;
      display: flex;
    }

    .clickable {
      color: var(--color-blue-500);
      margin: 0px 4px;
      cursor: pointer;
      &:hover {
        color: var(--color-blue-600);
      }
      &:focus-visible {
        outline: var(--color-black) auto 1px;
      }
    }

    .subtitle {
      color: var(--color-gray-550);
    }

    .button {
      display: inline-block;
      padding: 10px;
      background: #ccc;
      cursor: pointer;
      border-radius: 5px;
      border: 1px solid #ccc;
    }
  }

  p.text-sm.semi-bold.font-primary{
      white-space: pre-line;
  }
}