import { Directive, EventEmitter, Input, OnInit, Output, ViewContainerRef } from '@angular/core';
import { FlattenedRow } from '../rows/rows.model';

@Directive({
  selector: '[rowTemplate]',
})
export class RowTemplateDirective implements OnInit {
  @Input('rowTemplate') row!: FlattenedRow;
  @Output() rowClick: EventEmitter<FlattenedRow> = new EventEmitter<FlattenedRow>();
  
  constructor(private viewContainer: ViewContainerRef) {}

  ngOnInit(): void {
    const rowComponent = this.viewContainer.createComponent(
      this.row.rowComponent
    );

    rowComponent.instance.row = this.row;
    rowComponent.instance.rowConfig = this.row.rowConfig;

    if(this.row.isGrouped){
      const element = rowComponent.location.nativeElement;
      element.classList.add('children-row');
    }

    rowComponent.changeDetectorRef.markForCheck();
    rowComponent.instance.rowClick.subscribe((row: FlattenedRow) => {
      this.rowClick.emit(row);
    });
  }
}
