import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { SeGetEnvironmentResponse, SeLoginResponse, SeLoginSimulatRequest, SeLogoutResponse } from '../auth/auth.model';
import { SeAuthService } from '../auth/auth.service';
import { SeHttpRequest } from '../http/http-service.model';
import { SeHttpService } from '../http/http-service.service';

@Injectable({
  providedIn: 'root'
})
export class LoginEndpointsService {

  // Seguretat API URL
  private seguretatApiUrl: string = '/api/seguretat';
  // Seguretat Privat API URL
  private seguretatPrivatApiUrl: string = '/api/seguretat-privat';
  // Seguretat Admin
  private seguretatAdminApiUrl: string = '/api/seguretat-admin';

  constructor(
    private authService: SeAuthService,
    private httpService: SeHttpService,
  ) { }

  /**
   * Seguretat API URL
   * @description Modify the default Seguretat API URL
   */
  setSeguretatBaseUrl = (baseUrl: string): void => {
    this.seguretatApiUrl = baseUrl;
  };

  /**
   * Seguretat API URL
   * @description Modify the default Seguretat API URL
   */
  getSeguretatBaseUrl = (): string => this.seguretatApiUrl;

  /**
   * Login > Request
   * @param urlSeguretat (Optional) Pass as parameter the "seguretat" api base url
   * @returns
   */
    loginRequest = (): Observable<SeLoginResponse> => {
      const httpRequest: SeHttpRequest = {
        baseUrl: this.seguretatApiUrl,
        url: `/login`,
        method: 'get',
      };
      // Set headers
      if (this.authService.getSessionStorageUser()?.origin_id) {
        httpRequest.headers = {
          origin_id: this.authService.getSessionStorageUser()?.origin_id,
          // gicar_id: ''
        };
      }
      
      return this.httpService.get(httpRequest);
    };

    
  /**
   * Login simulat > Request
   * @param urlSeguretat (Optional) Pass as parameter the "seguretat" api base url
   * @returns
   */
  loginSimulatRequest = (
    request: SeLoginSimulatRequest
  ): Observable<SeLoginResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: this.seguretatPrivatApiUrl,
      url: `/login-simulat`,
      method: 'post',
      body: request,
    };
    // Set headers
    if (this.authService.getSessionStorageUser()?.origin_id) {
      httpRequest.headers = {
        origin_id: this.authService.getSessionStorageUser()?.origin_id,
        // gicar_id: ''
      };
    }
    return this.httpService.post(httpRequest);
  };

  /**
 * Logout > Request
 * @param urlSeguretat (Optional) Pass as parameter the "seguretat" api base url
 * @returns
 */
  logoutRequest = (): Observable<SeLogoutResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: this.seguretatApiUrl,
      url: `/logout`,
      method: 'get',
    };
    // Set headers
    if (this.authService.getSessionStorageUser()?.origin_id) {
      httpRequest.headers = {
        origin_id: this.authService.getSessionStorageUser()?.origin_id,
      };
    }
    return this.httpService.get(httpRequest);
  };

      /**
   * Get Environment > Request
   * @returns Observable<SeGetEnvironmentResponse>
   */
  endpointGetEnvironment = (): Observable<SeGetEnvironmentResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: this.seguretatAdminApiUrl,
      url: '/environment',
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  };

  /**
 * Get Seu Url > Request
 * @returns Observable<SeGetEnvironmentResponse>
 */
  endpointGetSeuUrlByEnv = (): Observable<SeGetEnvironmentResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: this.seguretatAdminApiUrl,
      url: '/1.0/adreca',
      method: 'get',
    };

    return this.httpService.get(httpRequest);
  };
}