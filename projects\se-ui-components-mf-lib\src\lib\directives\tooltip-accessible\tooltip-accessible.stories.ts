import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { TooltipAccessibleDirective } from './tooltip-accessible.directive';
import { SeTooltipAccessibleModule } from './tooltip-accessible.module';
import { SeSharedModule } from '../../shared/shared.module';

const meta: Meta<TooltipAccessibleDirective> = {
  title: 'Directives/Tooltip Accessible',
  component: TooltipAccessibleDirective,
  decorators: [
    moduleMetadata({
      imports: [SeTooltipAccessibleModule,SeSharedModule],
    }),
  ],
  args: {
    textAccessible: 'Example text',
    tooltipEvent: 'hover-focus',
    positionLeft: 0,
    positionTop: 0
  },
  argTypes: {
    textAccessible: {
      description: 'Text of the tooltip',
      control: { type: 'text' },
      table: { defaultValue: { summary: undefined } },
    },
    tooltipEvent: {
      description: 'Event of the tooltip. If the event is \'hover-focus\' the tooltip is shown with tab key and mouse.',
      control: { type: 'text' },
      table: { 
        defaultValue: { summary: 'hover' },
        type: { summary: 'hover | focus | hover-focus' }
      },
    },
    positionLeft: {
      description: 'Horizontal position of the tooltip',
      type: 'number',
      table: {
        defaultValue: { summary: 0 },
      },
    },
    positionTop: {
      description: 'Vertical position of the tooltip',
      type: 'number',
      table: {
        defaultValue: { summary: 0 },
      },
    },
  },
  tags: ['autodocs'],
  render: (args) => ({
    props: {
      ...args,
    },
    template: `
      <button 
        class="icon-button"
        [pTooltipAccessible]="textAccessible"
        [tooltipEvent]="tooltipEvent"
        [positionLeft]="positionLeft"
        [positionTop]="positionTop"
        [tabindex]="
          (textAccessible && (tooltipEvent === 'focus' ||
          tooltipEvent === 'hover-focus'))
            ? 0
            : -1
        "
      >
        <ng-icon class="info-icon text-md" name="matInfo"></ng-icon>
      </button>
    `,
    styles: [
      `
        .icon-button {
          all: initial;
          width: 16px;
          height: 16px;
          font-size: 16px;
          cursor: pointer;
          border-radius: 8px;
      
          &:focus-visible {
            outline: var(--color-black) auto 1px;
          }
        }
      `
    ]
  }),
};

export default meta;
type Story = StoryObj<TooltipAccessibleDirective>;

export const Default: Story = {};
