import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { Nullable } from 'primeng/ts-helpers';
import { SeButton } from '../button';
import { AlertType, OrderedListData } from './alert.model';

@Component({
  selector: 'se-alert',
  template: `
    <div *ngIf="visible" class="alert" [ngClass]="[type, alertClass]">
      <div *ngIf="showAlertIcon" class="alert__left-content">
        <i class="icon"
          ><ng-icon class="text-xl" [name]="getAlertIcon(type)"></ng-icon
        ></i>
      </div>
      <div
        class="d-flex justify-content-between w-100"
        [ngClass]="[contentClass]"
      >
        <div class="alert__content">
          <div
            *ngIf="title"
            class="title"
            [class.bold]="hasContent || list.length || subtitle"
            [ngClass]="[titleClass]"
            [innerHTML]="title | translate | safeHtml"
          ></div>
          <div
            *ngIf="subtitle"
            class="subtitle text-sm"
            [class.bold]="hasContent || list.length"
            [ngClass]="[subtitleClass]"
            [innerHTML]="subtitle | translate | safeHtml"
          ></div>
          <div>
            <div
              *ngIf="filteredList.length || orderedListData?.items?.length"
              [ngClass]="{ 'mt-2': title || subtitle, 'mb-2': hasContent }"
            >
              <ul *ngIf="filteredList.length" class="alert__list">
                <li
                  *ngFor="let text of filteredList"
                  [innerHTML]="text | translate | safeHtml"
                ></li>
              </ul>
              <ol
                *ngIf="orderedListData?.items?.length"
                class="alert__list--ordered mt-2"
                [type]="orderedListData?.orderedType"
              >
                <li
                  *ngFor="let item of orderedListData?.items"
                  [ngClass]="{ 'mt-2': item.subList?.length }"
                >
                  <span [innerHTML]="item.text | translate | safeHtml"></span>
                  <ol
                    *ngIf="item.subList?.length"
                    [ngClass]="item.subListOrderedType"
                    [type]="item.subListOrderedType"
                  >
                    <li
                      *ngFor="let subListText of item.subList"
                      [innerHTML]="subListText | translate | safeHtml"
                    ></li>
                  </ol>
                </li>
              </ol>
            </div>
            <div
              [ngClass]="{
                alert__ng_content:
                  hasContent &&
                  (title ||
                    subtitle ||
                    list.length ||
                    orderedListData?.items?.length)
              }"
              class="text-sm content-container"
              #alertContent
              *ngIf="(collapseButton && !collapsed) || !collapseButton"
            >
              <ng-content></ng-content>
            </div>
          </div>
        </div>
        <div *ngIf="closeButton" class="alert__right-content">
          <button
            class="close-icon text-xl"
            (click)="onClose($event)"
            [attr.aria-label]="'UI_COMPONENTS.BUTTONS.CLOSE' | translate"
          >
            <i><ng-icon class="text-xl" name="matCloseOutline"></ng-icon></i>
          </button>
        </div>
        <div *ngIf="collapseButton" class="collapse-button">
          <ng-container *ngTemplateOutlet="alertButton; context: { button: collapseButton,buttonFn: collapseToggle.bind(this) }"></ng-container>
        </div>
        <div *ngIf="filterButton" class="collapse-button">
          <ng-container *ngTemplateOutlet="alertButton; context: { button: filterButton,buttonFn: filterToggle.bind(this) }"></ng-container>
        </div>
      </div>
    </div>

    <ng-template #alertButton let-button="button" let-buttonFn="buttonFn">
      <se-button
        [btnTheme]="button.btnTheme ?? 'trueOnlyText'"
        [tooltipText]="button.tooltipText"
        [tooltipPosition]="button.tooltipPosition"
        [icon]="button.icon || ''"
        [iconSize]="button.iconSize || ''"
        [iconPosition]="button.iconPosition || 'left'"
        (onClick)="buttonFn()"
        [size]="'small'"
        >{{
          (collapsed || filtered
            ? button.alternateLabel
            : button.label) ?? '' | translate
        }}
      </se-button>
    </ng-template>
  `,
  styleUrls: ['./alert.component.scss'],
})
export class AlertComponent {
  @ViewChild('alertContent') alertContent: ElementRef | undefined;

  hasContent: boolean = true;
  visible: boolean = true;

  @Input() id: string = 'se-alert-id';
  @Input() title!: string;
  @Input() titleClass: string = '';
  @Input() subtitle: string = '';
  @Input() subtitleClass: string = '';
  @Input() type!: AlertType;
  @Input() list: string[] = [];
  @Input() orderedListData?: OrderedListData;
  @Input() closeButton: boolean = true;
  @Input() showAlertIcon: boolean = true;
  @Input() collapsed: boolean = false;
  @Input() filtered: boolean = false;
  @Input() minFilteredListLength: number = 0;
  @Input() collapseButton: Nullable<SeButton>;
  @Input() filterButton: Nullable<SeButton>;
  @Input() contentClass: string = 'flex-row';
  @Input() alertClass: string = '';

  @Output() close: EventEmitter<string> = new EventEmitter();
  @Output() collapse: EventEmitter<boolean> = new EventEmitter();

  private _listFiltered: string[] = [];
  
  get filteredList(): string[] {
    if(this.filtered) {
      if (!this._listFiltered.length) {
        this._listFiltered = this.list.slice(0, this.minFilteredListLength);
      }
    }
    return this._listFiltered.length ? this._listFiltered : this.list;
  }

  ngAfterViewInit() {
    this.hasContent = !!this.alertContent?.nativeElement.innerHTML.trim();
  }

  onClose(e: Event): void {
    this.visible = false;
    this.close.emit(this.id);
  }

  getAlertIcon(type: AlertType): string {
    const icons: Partial<Record<AlertType, string>> = {
      info: 'matInfo',
      error: 'matCancel',
      warning: 'matWarningOutline',
      success: 'matCheckCircle',
    };

    return icons[type] ?? '';
  }

  collapseToggle(): void {
    this.collapsed = !this.collapsed;
    this.collapse.emit(this.collapsed);
  }

  filterToggle(): void {
    if(this.filtered) {
      this._listFiltered = this.list;
      this.filtered = false;
    } else {
      this._listFiltered = this.list.slice(0, this.minFilteredListLength);
      this.filtered = true;
    }
  }
}
