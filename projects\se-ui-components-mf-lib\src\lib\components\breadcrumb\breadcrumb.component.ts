import { Component, Input, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { filter, map } from 'rxjs';
import { Breadcrumb, SeBreadcrumbThemeEnum } from './breadcrumb.model';

@Component({
  selector: 'se-breadcrumb',
  template: `
    <div class="se-breadcrumb" [ngClass]="breadcrumbTheme">
      <nav class="se-breadcrumb-nav breadcrumb">
        <ng-container *ngFor="let crumb of breadcrumbs; let last = last">
          <ng-container *ngIf="!last">
            <a
              class="text-sm"
              [routerLink]="isCurrentUrl(crumb.url) ? null : crumb.url"
              [ngClass]="{
                active: isCurrentUrl(crumb.url),
                inactive: !isCurrentUrl(crumb.url)
              }"
              [attr.aria-disabled]="isCurrentUrl(crumb.url)"
              (click)="isCurrentUrl(crumb.url) ? $event.preventDefault() : {}"
            >
              {{ crumb.label }}
            </a>
            <span class="separator">
              <ng-icon name="matChevronRightOutline"></ng-icon>
            </span>
          </ng-container>
          <ng-container *ngIf="last">
            <span
              class="text-sm"
              [ngClass]="isCurrentUrl(crumb.url) ? 'active' : 'inactive'"
            >
              {{ crumb.label }}
            </span>
          </ng-container>
        </ng-container>
      </nav>

      <h2 class="se-breadcrumb-title" *ngIf="title">{{title}}</h2>
    </div>
  `,
  styleUrls: ['./breadcrumb.component.scss'],
})
export class BreadcrumbComponent implements OnInit {
  @Input() inputBreadcrumbs: Breadcrumb[] | null = null;
  @Input() activeUrl: string | null = null;
  @Input() title: string | null = null;
  @Input() breadcrumbTheme: SeBreadcrumbThemeEnum = SeBreadcrumbThemeEnum.DEFAULT;

  breadcrumbs: Breadcrumb[] = [];
  currentUrl: string = '';

  constructor(private router: Router) {}

  ngOnInit(): void {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        map((event) => event as NavigationEnd)
      )
      .subscribe((event) => {
        this.currentUrl = event.urlAfterRedirects;

        if (this.inputBreadcrumbs) {
          this.breadcrumbs = this.inputBreadcrumbs.filter((crumb) =>
            this.currentUrl.includes(crumb.url)
          );
        } else {
          const paths = this.currentUrl.split('/').filter(Boolean);
          this.breadcrumbs = paths.map((path, index) => {
            return {
              label: path,
              url: `/${paths.slice(0, index + 1).join('/')}`,
            } as Breadcrumb;
          });
        }
      });
  }

  isCurrentUrl(url: string): boolean {
    return this.activeUrl ? this.activeUrl === url : this.currentUrl === url;
  }
}
