import type { <PERSON><PERSON>, StoryObj } from '@storybook/angular';
import { applicationConfig, moduleMetadata } from "@storybook/angular";
import { provideAnimations } from "@angular/platform-browser/animations";
import { SeSideTabsModule } from './side-tabs.module';
import { SeSideTabsComponent } from './side-tabs.component';

// More on how to set up stories at: https://storybook.js.org/docs/angular/writing-stories/introduction
const meta: Meta<SeSideTabsComponent> = {
  title: 'Components/Side tabs',
  component: SeSideTabsComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [SeSideTabsModule],
    }),
    applicationConfig({
      providers: [provideAnimations()],
    }),
  ],
  args: {
    sections: [
      {
        id: '1',
        title: 'Tab 1'
      },
      {
        id: '2',
        title: 'Tab 2'
      },
      {
        id: '3',
        title: 'Tab 3',
        description:
          'Optional parameter that displays a description of the current tab section',
      },
      {
        id: '4',
        title: 'Tab 4'
      },
    ]
  },
  render: (args) => ({
    props: {
      ...args,
      onSelected: (event: string) => {
        console.log('Selected tab:', event);
      }
    },
    template: `
    <se-side-tabs
    [(sections)]="sections"
    [activeTabIndex]="activeTabIndex"
    [selectMode]="selectMode"
    (onSelected)="onSelected($event)">
    <ng-template [sideTabContent]="1">
      ESTO ES 1
    </ng-template>
    <ng-template [sideTabContent]="2">
      esto es 2
    </ng-template>
    <ng-template [sideTabContent]="3">
      esto es 3
    </ng-template>
    <ng-template [sideTabContent]="4">
      esto es 4
    </ng-template>
  </se-side-tabs>
    `,
  }),
};

export default meta;
type Story = StoryObj<SeSideTabsComponent>;

export const Default: Story = {
  args: {
    sections: [{
			id: '1',
			title: 'Tab 1'
		},
		{
			id: '2',
			title: 'Tab 2'
		},
		{
			id: '3',
			title: 'Tab 3',
			description:
				'Optional parameter that displays a description of the current tab section',
		},
		{
			id: '4',
			title: 'Tab 4'
		},],
  }
};


export const ByActiveTabIndex: Story = {
  args: {
    sections: [{
			id: '1',
			title: 'Tab 1'
		},
		{
			id: '2',
			title: 'Tab 2'
		},
		{
			id: '3',
			title: 'Tab 3',
			description:
				'Optional parameter that displays a description of the current tab section',
		},
		{
			id: '4',
			title: 'Tab 4'
		},],
    activeTabIndex: '3',
  }
};

export const SelectMode: Story = {
  args: {
    sections: [{
			id: '1',
			title: 'Tab 1'
		},
		{
			id: '2',
			title: 'Tab 2'
		},
		{
			id: '3',
			title: 'Tab 3',
			description:
				'Optional parameter that displays a description of the current tab section',
		},
		{
			id: '4',
			title: 'Tab 4'
		},],
    activeTabIndex: '3',
    selectMode: true,
  }
};
