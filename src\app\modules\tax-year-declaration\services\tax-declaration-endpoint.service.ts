import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { Observable } from 'rxjs';
import { SeHttpResponse, SeHttpService } from 'se-ui-components-mf-lib';
import {
  DeclarationTypes,
  PutTaxDeclarationData,
  ValidationProgress,
} from '../tax-year-declaration.model';

@Injectable({
  providedIn: 'root',
})
export class TaxYearDeclarationEndpointService {
  constructor(private httpService: SeHttpService) {
    // Empty constructor
  }

  getDeclarationTypes(): Observable<SeHttpResponse<DeclarationTypes[]>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/model`,
      clearExceptions: true,
    });
  }

  getTaxYearsByImpost(model: string): Observable<SeHttpResponse<string[]>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/model/${model}/exercici`,
      clearExceptions: true,
    });
  }

  putTaxDeclarationData(
    body: PutTaxDeclarationData,
    idTramit: string,
  ): Observable<SeHttpResponse> {
    return this.httpService.put({
      method: 'put',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/${idTramit}`,
      body,
      clearExceptions: true,
    });
  }

  postValidateTaxDeclaration(idTramit: string): Observable<SeHttpResponse<string>> {
    return this.httpService.post({
      method: 'post',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/${idTramit}/validacio`,
      clearExceptions: true,
    });
  }

  getValidationStatus(idValidation: string): Observable<SeHttpResponse<ValidationProgress>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/${idValidation}/validacio`,
      clearExceptions: true,
    });
  }
}
