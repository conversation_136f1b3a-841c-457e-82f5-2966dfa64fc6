import { Injectable } from '@angular/core';
import { SeModal } from './modal.model';
import { NgbModalRef, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ModalComponent } from './modal.component';
import { ProgressModalComponent } from '../progress-modal/progress-modal.component';
import { Subject } from 'rxjs';
import { SeButton } from '../button';

@Injectable({
  providedIn: 'root',
})
export class SeModalService {
  constructor(private modalService: NgbModal) {}

  openModal(modalData: SeModal, modalDataAsInput: boolean = true): NgbModalRef {
    const modalRef: NgbModalRef = this.modalService.open(
      modalData.component ?? ModalComponent,
      {
        size: modalData?.size ?? 'lg',
        windowClass: modalData?.windowClass ?? '',
        backdrop: modalData?.backdrop ?? true,
        keyboard: modalData?.keyboard ?? true,
        centered: modalData?.centered ?? false,
      }
    );

    if (modalDataAsInput) modalRef.componentInstance.data = modalData;

    return modalRef;
  }

  openSidebarModal(
    modalData: SeModal,
    side: 'right' | 'left' = 'right'
  ): NgbModalRef {
    const modalRef: NgbModalRef = this.openModal({
      ...modalData,
      size: 'sidebar',
      windowClass: (modalData?.windowClass ?? '') + ` modal-${side}`,
    });

    return modalRef;
  }

  openProgressModal(
    interval: number,
    message: string,
    subtitle?: string,
    progressValue$?: Subject<number>,
    customButton?: SeButton
  ): NgbModalRef {
    const modalRef: NgbModalRef = this.modalService.open(
      ProgressModalComponent,
      {
        windowClass: '',
        backdrop: 'static',
        // centered: true
      }
    );

    modalRef.componentInstance.message = message;
    modalRef.componentInstance.subtitle = subtitle;
    modalRef.componentInstance.interval = interval;
    modalRef.componentInstance.customButton = customButton;
    modalRef.componentInstance.progressValue$ = progressValue$;

    return modalRef;
  }
}
