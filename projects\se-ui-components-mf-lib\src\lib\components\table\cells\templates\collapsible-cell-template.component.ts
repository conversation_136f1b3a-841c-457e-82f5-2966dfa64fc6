// cell-template.component.ts
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import { CellComponent, CellConfig, FlattenedCell } from '../cells.model';

@Component({
  selector: 'se-collapsible-cell',
  template: `
    <div class="d-flex align-items-center">
      <ng-icon
        *ngIf="row.isParent"
        size="24"
        [name]="
          row.isCollapsed
            ? 'matKeyboardArrowRightOutline'
            : 'matKeyboardArrowDownOutline'
        "
      ></ng-icon>
      <div
        [ngClass]="{
          'text-ellipsis': cellConfig.ellipsis,
          'text-nowrap': cellConfig.nowrap
        }"
        [ngStyle]="cellConfig['ngStyle']"
        [innerHTML]="value | translate | safeHtml"
      ></div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
/**
 * Representa un componente de celda colapsable utilizado en una tabla.
 * Para la implementación, consulte el ejemplo CollapsibleRows en table.stories.
 * Si es padre, muestra un icono de flecha hacia abajo o hacia la derecha, dependiendo si está colapsado o no.
 * Resumen: utiliza los valores isParent, isChild, isCollapsed y keyGroup de row para mostrar la primera linea de las que tengan el mismo KeyGroup.
 */
export class CollapsibleCellComponent implements CellComponent {
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;
}
