import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

import { AccordionComponent } from './accordion.component';
import { SeSharedModule } from '../../shared/shared.module';
import { SeCheckboxModule } from '../checkbox/checkbox.module';
import { SeRadioModule } from '../radio/radio.module';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

@NgModule({
  declarations: [AccordionComponent],
  imports: [
    CommonModule,
    SeSharedModule,
    SeCheckboxModule,
    SeRadioModule,
    TranslateModule.forChild(),
    ReactiveFormsModule,
    FormsModule,
  ],
  exports: [AccordionComponent],

})
export class SeAccordionModule {}
