import { DOCUMENT } from '@angular/common';
import {
  ComponentFactoryResolver,
  ComponentRef,
  Injectable,
  ViewContainerRef,
  Inject,
  SecurityContext,
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Subject, Subscription } from 'rxjs';
import { WcComponent, WcComponentEvent } from './webcomponent.model';
import { SePageLayoutService } from '../page-layout/page-layout.service';

@Injectable({
  providedIn: 'root',
})
export class WebComponentsService {
  webComponent!: ComponentRef<any>;
  webComponents!: { [x: string]: WcComponent };
  currentComponent!: WcComponent;

  // WebComponent > ViewContainerRef
  wcVCRef!: ViewContainerRef;

  // WebComponent > Output event
  wcEvent: Subject<WcComponentEvent> = new Subject();

  constructor(
    // TODO: Change ComponentFactoryResolver with ViewContainerRef.createComponent
    private resolver: ComponentFactoryResolver,
    private pageService: SePageLayoutService,
    private sanitizer: DomSanitizer,
    @Inject(DOCUMENT) private document: any //es tipo Document pero el compilador falla si no pones any
  ) {}

  /**
   * WebComponent > ViewContainerRef > SET
   * @param vcRef Container reference of the WebComponent
   */
  setWCViewContainer = (vcRef: ViewContainerRef): void => {
    this.wcVCRef = vcRef;
  };

  /**
   * WebComponent > ViewContainerRef > GET
   * @returns Container reference of the WebComponent
   */
  getWCViewContainer = (): ViewContainerRef => {
    return this.wcVCRef;
  };

  /**
   * WebComponent > Components paths > SET
   * @param path Component path
   */
  setWCComponentsPaths = (components: { [x: string]: WcComponent }) => {
    this.webComponents = components;
  };

  /**
   * WebComponent > Components paths > GET
   * @returns Components lists with the paths
   */
  getWCComponentsPaths = (): { [x: string]: WcComponent } => {
    return this.webComponents;
  };

  // WebComponent > SET
  loadWebComponent = (path: string): void => {
    console.log('WebComponent > loadWebComponent - path: ', path);
    let componentEvents: Subscription;

    // Destroy and unsubscribe from the component
    if (this.webComponent) {
      this.webComponent.destroy();
    }

    // Generate the new component
    const component = this.webComponents[path];

    if (component) {
      // Create component
      const componentFactory = this.resolver.resolveComponentFactory<any>(
        component.component
      );
      this.wcVCRef.clear();
      this.webComponent = this.wcVCRef.createComponent(componentFactory);
      const webComponentInstance = this.webComponent.instance;

      // Update page layout
      this.pageService.updatePageLayout(component);

      // Subscribe to the component output event emitter
      if (webComponentInstance.componentEvent) {
        componentEvents = webComponentInstance.componentEvent.subscribe(
          (componentEvent: WcComponentEvent) => {
            console.log(
              'WebComponent > Emit event - componentEvent: ',
              componentEvent
            );
            this.wcEvent.next(componentEvent);
          }
        );
      }
    } else {
      console.warn(
        'The is no component with this path (path, webComponents): ',
        path,
        this.webComponents
      );
    }
  };

  /**
   * Set Style
   * @description Set the css path based on the webcomponent
   */
  setWebComponentStyle = (cssURL: string, webcomponent: string = ''): void => {
    if (this.document.getElementById(`${webcomponent}-style-css`)) return;

    const head = this.document.getElementsByTagName('head')[0];
    const style = this.document.createElement('link');
    style.id = `${webcomponent}-style-css`;
    style.rel = 'stylesheet';
    style.href = `${this.sanitizer.sanitize(SecurityContext.URL, cssURL)}`;
    head.appendChild(style);
  };
}
