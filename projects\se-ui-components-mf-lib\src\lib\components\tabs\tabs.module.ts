import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TabViewComponent } from './tab-view/tab-view.component';
import { TabItemComponent } from './tab-item/tab-item.component';
import { TabHeaderComponent } from './tab-header/tab-header.component';
import { SeSharedModule } from '../../shared/shared.module';
import { SeBadgeModule } from '../badge/badge.module';

@NgModule({
  imports: [
    CommonModule, 
    SeSharedModule,
    SeBadgeModule
  ],
  declarations: [TabViewComponent, TabItemComponent, TabHeaderComponent],
  exports: [TabViewComponent, TabItemComponent, TabHeaderComponent],
})
export class SeTabsModule {}
