import { TemplateRef } from '@angular/core';
import { SeButtonTheme } from './models';

export interface SeButton {
  label?: string;
  alternateLabel?: string;
  ariaLabel?: string;
  size?: 'large' | 'default' | 'small';
  btnTheme?: SeButtonTheme;
  disabled?: boolean;
  icon?: string;
  title?: string;
  iconSize?: string;
  iconPosition?: 'right' | 'left';
  type?: string;
  tooltipText?: TemplateRef<HTMLElement> | string;
  tooltipPosition?: 'left' | 'top' | 'bottom' | 'right';
}
