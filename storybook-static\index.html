<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />

    <title>@storybook/angular - Storybook</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    
    <link rel="icon" type="image/svg+xml" href="./favicon.svg" />
    
    <link
      rel="prefetch"
      href="./sb-common-assets/nunito-sans-regular.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="prefetch"
      href="./sb-common-assets/nunito-sans-bold.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link rel="stylesheet" href="./sb-common-assets/fonts.css" />

    <link href="./sb-manager/runtime.js" rel="modulepreload" />

    
    <link href="./sb-addons/links-0/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/essentials-controls-1/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/essentials-actions-2/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/essentials-backgrounds-3/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/essentials-viewport-4/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/essentials-toolbars-5/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/essentials-measure-6/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/essentials-outline-7/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/interactions-8/manager-bundle.js" rel="modulepreload" />
       

    <style>
      #storybook-root[hidden] {
        display: none !important;
      }
    </style>

    
  </head>
  <body>
    <div id="root"></div>

    
    <script>
      
        
          window['FEATURES'] = {
  "warnOnLegacyHierarchySeparator": true,
  "buildStoriesJson": false,
  "storyStoreV7": true,
  "argTypeTargetsV7": true,
  "legacyDecoratorFileOrder": false
};
        
      
        
          window['REFS'] = {};
        
      
        
          window['LOGLEVEL'] = "info";
        
      
        
          window['DOCS_OPTIONS'] = {
  "defaultName": "Docs",
  "autodocs": "tag"
};
        
      
        
          window['CONFIG_TYPE'] = "PRODUCTION";
        
      
        
      
        
      
        
      
    </script>
    

    <script type="module">
      import './sb-manager/runtime.js';

      
      import './sb-addons/links-0/manager-bundle.js';
      
      import './sb-addons/essentials-controls-1/manager-bundle.js';
      
      import './sb-addons/essentials-actions-2/manager-bundle.js';
      
      import './sb-addons/essentials-backgrounds-3/manager-bundle.js';
      
      import './sb-addons/essentials-viewport-4/manager-bundle.js';
      
      import './sb-addons/essentials-toolbars-5/manager-bundle.js';
      
      import './sb-addons/essentials-measure-6/manager-bundle.js';
      
      import './sb-addons/essentials-outline-7/manager-bundle.js';
      
      import './sb-addons/interactions-8/manager-bundle.js';
      
    </script>

    <link href="./sb-preview/runtime.js" rel="prefetch" as="script" />
  </body>
</html>
