// cell-template.component.ts
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Column } from '../../columns/column.model';
import { FlattenedRow } from '../../rows/rows.model';
import { CellComponent, CellConfig, FlattenedCell } from '../cells.model';

@Component({
  selector: 'se-default-cell',
  template: `
    <div class="d-flex align-items-center gap-2">
      <div
        class="wrap-break-word"
        [ngClass]="{
          'text-ellipsis': cellConfig.ellipsis,
          'text-nowrap': cellConfig.nowrap
        }"
        [ngStyle]="cellConfig['ngStyle']"
        [innerHTML]="value | translate | safeHtml"
      ></div>
      <ng-icon
        [style]="cellConfig['ngStyle']"
        name="matInfo"
        [pTooltipAccessible]="getTooltip()"
        tooltipPosition="bottom"
      ></ng-icon>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
/**
 * Represents a default cell component used in a table.
 */
export class SeparatedTooltipCellComponent implements CellComponent {
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

  /**
   * Returns the tooltip text for the cell.
   * If the cell configuration has a tooltip, it returns the tooltip text.
   * If the tooltip text is not provided, it returns the cell value as the tooltip text.
   * If the cell configuration does not have a tooltip, it returns an empty string.
   *
   * @returns The tooltip text for the cell.
   */
  getTooltip = (): string =>
    this.cellConfig.tooltip ? this.cellConfig.tooltipText ?? this.value : '';
}
