import { AfterViewInit, Directive, ElementRef, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { AriaLabelInput } from './aria-label.model';

@Directive({
  selector: '[ariaLabel]',
})
export class SeAriaLabelDirective implements AfterViewInit {
  
  @Input('ariaLabel') ariaLabelInput!: AriaLabelInput;
  
  constructor(
    private el: ElementRef,
    private translateService: TranslateService
  ) {}

  ngAfterViewInit(): void {
    setTimeout(() => {
      const htmlElement = this.el.nativeElement.querySelector(
        this.ariaLabelInput.querySelector
      ) as HTMLButtonElement;
      if (htmlElement) {
        this.translateService
          .get(this.ariaLabelInput.translationKey)
          .subscribe((response) =>
            htmlElement.setAttribute('aria-label', response)
          );
      }
    },0)
  }
}
