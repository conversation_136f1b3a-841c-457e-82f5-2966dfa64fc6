import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { MenuModule } from 'primeng/menu';
import { ButtonDropdownComponent } from './button-dropdown.component';

import { SeSharedModule } from '../../shared/shared.module';
import { SeButtonModule } from '../button/button.module';

@NgModule({
  imports: [
    CommonModule,
    SeSharedModule,
    SeButtonModule,
    MenuModule,
    TranslateModule,
  ],
  exports: [ButtonDropdownComponent],
  declarations: [ButtonDropdownComponent],
})
export class SeButtonDropdownModule {
  // Intencionadamente vacío
}
