:host ::ng-deep .se-button-dropdown {
  p-menu > .p-menu {
    .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus
      > .p-menuitem-content {
      background-color: var(--color-blue-200);
    }
  }
  .p-menu {
    margin-top: 4px;
    width: max-content;
    padding: 0;
    border: 1px solid var(--color-gray-300);
    border-radius: 4px;
    overflow: hidden;

    .p-menu-list {
      margin-left: 0;
      margin-bottom: 0;
      padding: 0;
      a {
        text-decoration: none;
        span {
          font-family: var(--font-primary);
          font-size: var(--text-sm);
        }
        &:not(.p-highlight):not(.p-disabled):hover {
          color: var(--color-back);
          background: var(--color-blue-200);
        }
      }
    }
  }
}
