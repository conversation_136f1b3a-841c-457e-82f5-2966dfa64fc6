import { Meta, moduleMetadata } from '@storybook/angular';
import { BreadcrumbComponent } from './breadcrumb.component';
import { MockRoutingModule } from '../../shared/mocks/mock-routing.module';
import { SeBreadcrumbdModule } from './breadcrumb.module';
import { NavigationEnd, Router } from '@angular/router';
import { MockRouter } from '../../shared/mocks/mock-router';
import { SeBreadcrumbThemeEnum } from './breadcrumb.model';

const mockRoutes = [
  { label: 'Home', url: '/' },
  { label: 'About', url: '/about' },
  { label: 'Contact', url: '/contact' },
  { label: 'Products', url: '/products' },
  { label: 'Electronics', url: '/products/electronics' },
  { label: 'Mobile', url: '/products/electronics/mobile' },
];

function createNewMockRouterInstance(initialUrl: string): MockRouter {
  const mockRouter = new MockRouter();
  mockRouter.emitEvent(new NavigationEnd(0, initialUrl, initialUrl));
  return mockRouter;
}

const meta: Meta = {
  title: 'Components/Breadcrumb',
  component: BreadcrumbComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [MockRoutingModule, SeBreadcrumbdModule],
    }),
  ],
  args: {
    inputBreadcrumbs: mockRoutes,
  },
  render: (args) => ({
    props: args,
    template: `
      <se-breadcrumb
        [inputBreadcrumbs]="inputBreadcrumbs">
      </se-breadcrumb>
    `,
  }),
};

export default meta;

export const Default = {
  args: {
    inputBreadcrumbs: mockRoutes,
  },
  decorators: [
    moduleMetadata({
      providers: [
        {
          provide: Router,
          useFactory: () => createNewMockRouterInstance('/'),
        },
      ],
    }),
  ],
};

export const ChildRouteAbout = {
  args: {
    inputBreadcrumbs: mockRoutes,
  },
  decorators: [
    moduleMetadata({
      providers: [
        {
          provide: Router,
          useFactory: () => createNewMockRouterInstance('/about'),
        },
      ],
    }),
  ],
};

export const NestedChildRoute = {
  args: {
    inputBreadcrumbs: mockRoutes,
  },
  decorators: [
    moduleMetadata({
      providers: [
        {
          provide: Router,
          useFactory: () =>
            createNewMockRouterInstance('/products/electronics/mobile'),
        },
      ],
    }),
  ],
};

export const ActiveNotLast = {
  args: {
    inputBreadcrumbs: mockRoutes,
    activeUrl: '/products',
  },
  decorators: [
    moduleMetadata({
      providers: [
        {
          provide: Router,
          useFactory: () =>
            createNewMockRouterInstance('/products/electronics/mobile'),
        },
      ],
    }),
  ],
  render: (args: any) => ({
    props: args,
    template: `
      <se-breadcrumb
        [inputBreadcrumbs]="inputBreadcrumbs"
        [activeUrl]="activeUrl">
      </se-breadcrumb>
    `,
  }),
};

export const NoInput = {
  args: {
    inputBreadcrumbs: null,
  },
  decorators: [
    moduleMetadata({
      providers: [
        {
          provide: Router,
          useFactory: () => createNewMockRouterInstance('/'),
        },
      ],
    }),
  ],
};

export const WithBackground = {
  args: {
    inputBreadcrumbs: mockRoutes,
    title: 'Product Categories',
    breadcrumbTheme: SeBreadcrumbThemeEnum.SECONDARY,
  },
  decorators: [
    moduleMetadata({
      providers: [
        {
          provide: Router,
          useFactory: () =>
            createNewMockRouterInstance('/products/electronics/mobile'),
        },
      ],
    }),
  ],
  render: (args: any) => ({
    props: args,
    template: `
      <se-breadcrumb
        [inputBreadcrumbs]="inputBreadcrumbs"
        [title]="title"
        [breadcrumbTheme]="breadcrumbTheme">
      </se-breadcrumb>
    `,
  }),
};
