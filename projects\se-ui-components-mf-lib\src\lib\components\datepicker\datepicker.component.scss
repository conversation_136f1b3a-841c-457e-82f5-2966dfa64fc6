::ng-deep .se-datepicker {
  margin-bottom: 1rem;

  [class*="-icon"]:not(.tooltip-icon) {
    width: 25px;
    height: 25px;
    font-size: 25px;
  }

  &__container {
    position: relative;
    height: 40px;

    .valid-icon {
      color: var(--color-green-300);
      position: absolute;
      bottom: 8px;
      right: 8px;
      z-index: 1;
    }

    .calendar-icon {
      color: var(--color-blue-500);
      position: absolute;
      bottom: 8px;
      right: 8px;
      z-index: 1;
      cursor: pointer;
      
      &.invalid {
        color: var(--color-red-400);
      }

      &.disabled {
        color: var(--color-gray-500);
        opacity: 0.8;
        cursor: not-allowed;
      }
    }
  }

  &.showIcon {
    .valid-icon {
      right: 41px;
    }
  }

  

  p-calendar.calendar-input {

    .p-calendar {
      width: 100%;
    
      .p-inputtext {
        padding: 8px;
        font-family: var(--font-primary);
        font-size: var(--text-sm);
        line-height: var(--line-sm);
        border: 1px solid var(--color-gray-400);
        height: 40px;

        &:enabled:focus {
          box-shadow: none !important;
        }
      }
    
      .p-datepicker-header {
        border-bottom: none;

        button.p-datepicker-prev,
        button.p-datepicker-next {
          color: var(--color-blue-500);
          border-radius: 5px;
      
          &:enabled:hover {
            background-color: var(--color-blue-600);
            color: var(--color-white);
            border: 1px solid var(--color-blue-600);
          }
      
          &:enabled:focus {
            background-color: var(--color-blue-500);
            color: var(--color-white);
            box-shadow: 0 0 0 2px var(--color-white), 0 0 0 4px var(--color-blue-500);
          }
        }
      }

      .p-datepicker-group-container {
        font-family: var(--font-primary);
      }

      .p-datepicker {
        font-family: var(--font-primary);
      
        &:not(.p-datepicker-inline) {
          border: 1px solid var(--color-gray-300); 
          margin-top: 4px;
        }

        table { 
          
            td, th {
            text-align: center;

            &.p-datepicker-today > span {
              border-radius: 5px;
              background-color: var(--color-white);
              color: var(--color-blue-500);
              border: 1px solid var(--color-blue-500);
            }
        
            > span {

              &.p-highlight{
                border-radius: 5px;
                background-color: var(--color-blue-500);
                color: var(--color-white);
                border: 1px solid var(--color-blue-500);
                &:hover{
                  background-color: var(--color-blue-600);
                }
              }
        
              &:focus {
                box-shadow: 0 0 0 2px var(--color-white), 0 0 0 4px var(--color-blue-500);
              }
            }
          }
        }
      }
    }

    &:not(.p-disabled) table td span:not(.p-highlight):not(.p-disabled):hover {
      border-radius: 5px;
      background-color: var(--color-blue-600);
      color: var(--color-white);
      border: 1px solid var(--color-blue-600);
    }

    &.disabled {
      .p-calendar {
        .p-inputtext{
          background: var(--color-gray-200);
          text-decoration: none;
          opacity: 0.8;
          cursor: not-allowed;
        }
      }
    }
  
    &.invalid {
      .p-calendar {
        .p-inputtext{
          box-shadow: none;
          border-color: var(--color-red-400);
        }
      }
    }
  
    &:not(.invalid) {
      .p-calendar {
        .p-inputtext:enabled:focus{
          box-shadow: none;
          border-color: var(--color-blue-500);
          outline: 2px solid var(--color-primary-link);
          outline-offset: 1px;
        }
      } 
    }
  }
}
