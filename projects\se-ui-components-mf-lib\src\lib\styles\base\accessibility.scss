/* Normalment es pot utilitzar la classe ".visually-hidden", <PERSON> <PERSON><PERSON><PERSON>,
quan volem ocultar algun element que no volem mostrar, però que poden
llegir els lectors de la pantalla. Per<PERSON>, en el cas de voler ocultar un
text que ha de llegir el lector de pantalla incorporat del navegador
Microsoft Edge, no podem utilitzar la classe anterior ja que conté la
propietat overflow: hidden; Això fa que el lector de Edge no ho
llegeixi. És per això que aquesta altra classe que s’ha creat a
continuació, que en lloc d’establir el valor de overflow: hidden;
estableix el valor de desbordament: clip; permet al lector de pantalla
de Edge llegir el text. */
.visually-hidden-text {
  font-size: 0 !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: clip !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
  position: absolute !important;
}
