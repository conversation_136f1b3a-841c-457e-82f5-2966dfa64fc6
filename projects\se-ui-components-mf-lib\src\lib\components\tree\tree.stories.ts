import { Meta, moduleMetadata, StoryObj } from "@storybook/angular";
import { SeTreeModule } from "./tree.module";
import { TreeComponent } from "./tree.component";

const meta: Meta<TreeComponent> = {
  title: 'Components/Tree',
  component: TreeComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [SeTreeModule],
    }),
  ],
  args: {
    tree: [
        {
            key: '0',
            label: 'Documents',
            data: 'Documents Folder',
            icon: 'matFolderOpenOutline',
            children: [
                {
                    key: '0-0',
                    label: 'Work',
                    data: 'Work Folder',
                    icon: 'matFolderOpenOutline',
                    children: [
                        { key: '0-0-0', label: 'Expenses.doc', icon: 'matTextFormatOutline', data: 'Expenses Document' },
                        { key: '0-0-1', label: 'Resume.doc', icon: 'matTextFormatOutline', data: 'Resume Document' }
                    ]
                },
                {
                    key: '0-1',
                    label: 'Home',
                    data: 'Home Folder',
                    icon: 'matFolderOpenOutline',
                    children: [{ key: '0-1-0', label: 'Invoices.txt', icon: 'matTextFormatOutline', data: 'Invoices for this month' }]
                }
            ]
        },
        {
            key: '1',
            label: 'Events',
            data: 'Events Folder',
            icon: 'matFolderOpenOutline',
            children: [
                { key: '1-0', label: 'Meeting', icon: 'matCalendarMonthOutline', data: 'Meeting' },
                { key: '1-1', label: 'Product Launch', icon: 'matCalendarMonthOutline', data: 'Product Launch' },
                { key: '1-2', label: 'Report Review', icon: 'matCalendarMonthOutline', data: 'Report Review' }
            ]
        },
        {
            key: '2',
            label: 'Movies',
            data: 'Movies Folder',
            icon: 'matFolderOpenOutline',
            children: [
                {
                    key: '2-0',
                    icon: 'matFolderOpenOutline',
                    label: 'Al Pacino',
                    data: 'Pacino Movies',
                    children: [
                        { key: '2-0-0', label: 'Scarface', icon: 'matLocalMoviesSharp', data: 'Scarface Movie' },
                        { key: '2-0-1', label: 'Serpico', icon: 'matLocalMoviesSharp', data: 'Serpico Movie' }
                    ]
                },
                {
                    key: '2-1',
                    label: 'Robert De Niro',
                    icon: 'matFolderOpenOutline',
                    data: 'De Niro Movies',
                    children: [
                        { key: '2-1-0', label: 'Goodfellas', icon: 'matLocalMoviesSharp', data: 'Goodfellas Movie' },
                        { key: '2-1-1', label: 'Untouchables', icon: 'matLocalMoviesSharp', data: 'Untouchables Movie' }
                    ]
                }
            ]
        }
    ],
  },
  argTypes: {
    tree: {
      description: 'Tree data',
      table: {
        defaultValue: { summary: 'TreeNode[]' },
      },
    },

  },
  render: (args) => ({
    props: args,
    template: `
      <se-tree
        [tree]="tree"
      >
      </se-tree>
    `,
  }),
};

export default meta;
type Story = StoryObj<TreeComponent>;

export const Tree: Story = {};
