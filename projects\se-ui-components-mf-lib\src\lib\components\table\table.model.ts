import { Type } from '@angular/core';
import { CellComponentConfig } from './cells/cells.model';
import { RowComponentKeys } from './rows/row-component-registry';
import { RowConfig, RowComponent } from './rows/rows.model';

export type TemplatePriorityOrder =
  | 'row-column-cell'
  | 'column-row-cell'
  | 'cell-row-column';

export interface TemplateCacheKey {
  rowId: string | number;
  columnKey: string;
}

export interface Row extends CellComponentConfig {
  id?: string;
  data: { [key: string]: Cell };
  rowConfig?: RowConfig;
  rowComponent?: Type<RowComponent>;
  rowComponentName?: RowComponentKeys;
  isDisabled?: boolean;
  keyGroup?: string;
  isChild?: boolean;
  isParent?: boolean;
  isCollapsed?: boolean;
  showInSummary?: boolean; // Whether to show this row in summary mode (mobile). Defaults to true.
}

export interface GroupRow{
  key: string;
  name: string;
  isCollapsed: boolean;
}

export interface Cell extends CellComponentConfig {
  value: any;
}

export enum TableSortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

export interface SortParams {
  columnKey: string;
  sortOrder: TableSortOrder;
}

export enum TableMobileLayoutMode {
  NORMAL_MODE = 'NORMAL_MODE',
  SUMMARY_MODE = 'SUMMARY_MODE',
}
