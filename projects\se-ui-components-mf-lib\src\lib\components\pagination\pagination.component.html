<div class="paginator-container">
  <div class="counter-dropdown-container">
    <p class="paginator-container__counter">
      {{ innerText | translate }} {{ currentPage }} de {{ totalPages }}
    </p>
    <form *ngIf="showRowsPerPage" class="me-2" [formGroup]="form">
      <se-dropdown
        [options]="rowsPerPage"
        formControlName="rowsPerPageDropdown"
        (dropdownOutput)="updateRowsPerPage()"
      ></se-dropdown>
    </form>
    <div class="text-sm" *ngIf="selectedText">{{selectedText | translate}}</div>
    <ng-container *ngIf="isMobile$ | async">
      <ng-container *ngTemplateOutlet="downloadButtonTemplate"></ng-container>
    </ng-container>
  </div>
  <div class="row flex-wrap">
    <p-paginator
      class="col"
      [ngClass]="{ 'buttons-paginator': !showNumberReportPaginator }"
      (onPageChange)="handlePageChange($event)"
      [first]="first"
      [rows]="rows"
      [totalRecords]="totalRecords"
      [showPageLinks]="showNumberReportPaginator"
      [showFirstLastIcon]="showNumberReportPaginator"
    >
    </p-paginator>

    <ng-container *ngIf="!(isMobile$ | async)">
      <ng-container *ngTemplateOutlet="downloadButtonTemplate; context: { class: 'col' }"></ng-container>
    </ng-container>
  </div>
</div>

<ng-template #downloadButtonTemplate let-class="class">
  <se-button
    *ngIf="downloadButton"
    [ngClass]="class"
    [btnTheme]="downloadButton.btnTheme || 'secondary'"
    [size]="downloadButton.size || 'small'"
    [disabled]="!!downloadButton.disabled"
    [icon]="downloadButton.icon"
    [iconSize]="downloadButton.iconSize || '20px'"
    [ariaLabel]="'UI_COMPONENTS.BUTTONS.DOWNLOAD' | translate"
    (onClick)="downloadClick.emit()"
  >
  </se-button>
</ng-template>
