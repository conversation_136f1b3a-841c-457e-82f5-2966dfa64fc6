.user-info-bar {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: 50% 50%;
  background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='1920px' height='1009px' viewBox='0 0 1920 1009' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3C!-- Generator: sketchtool 48.2 (47327) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3E1E317F59-F8DC-4830-8B2A-8292638ECC4A%3C/title%3E%3Cdesc%3ECreated with sketchtool.%3C/desc%3E%3Cdefs%3E%3Cpolygon id='path-1' points='0 0 1920 0 1920 1008.5 0 1008.5'%3E%3C/polygon%3E%3C/defs%3E%3Cg id='VITRALL' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' transform='translate(0.000000, -1945.000000)' opacity='0.1'%3E%3Cg id='Vitrall-Gris' transform='translate(0.000000, 1945.000000)'%3E%3Cmask id='mask-2' fill='white'%3E%3Cuse xlink:href='%23path-1'%3E%3C/use%3E%3C/mask%3E%3Cuse id='Mask' fill='%23595853' xlink:href='%23path-1'%3E%3C/use%3E%3Cg id='Group' mask='url(%23mask-2)' fill-opacity='0.25'%3E%3Cg transform='translate(-498.000000, -649.000000)'%3E%3Cg id='2-focus' stroke-width='1' fill='none' transform='translate(280.000000, 0.000000)'%3E%3Cpolygon id='Triangle-Copy-2' fill='%23FFFFFF' points='1598 1709 1285 545 1911 545'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-5' fill='%23FFFFFF' transform='translate(361.500000, 1127.000000) rotate(180.000000) translate(-361.500000, -1127.000000) ' points='361.5 1799 0 455 723 455'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-3' fill='%23FFFFFF' transform='translate(1780.380898, 624.924727) rotate(-60.000000) translate(-1780.380898, -624.924727) ' points='1780.3809 1271.98503 1432.36042 -22.1355732 2128.40138 -22.1355732'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-4' fill='%23FFFFFF' transform='translate(1980.979956, 1648.599704) rotate(240.000000) translate(-1980.979956, -1648.599704) ' points='1980.97996 2679.46473 1426.53064 617.734681 2535.42927 617.734681'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-4' fill='%23FFFFFF' transform='translate(1175.552234, 1158.126073) rotate(150.000000) translate(-1175.552234, -1158.126073) ' points='1175.55223 2030.5225 706.33503 285.729642 1644.76944 285.729642'%3E%3C/polygon%3E%3C/g%3E%3Cpolygon id='Triangle-Copy-3' fill='%23FFFFFF' transform='translate(1029.797297, 1101.961946) rotate(30.000000) translate(-1029.797297, -1101.961946) ' points='1029.7973 1960.11622 568.240215 243.807668 1491.35438 243.807668'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-6' fill='%23FFFFFF' transform='translate(2548.797297, 1056.961946) rotate(-30.000000) translate(-2548.797297, -1056.961946) ' points='2548.7973 1915.11622 2087.24022 198.807668 3010.35438 198.807668'%3E%3C/polygon%3E%3Cpolygon id='Triangle-Copy-3' fill='%23DDDDDD' transform='translate(1379.395013, 1413.163450) rotate(120.000000) translate(-1379.395013, -1413.163450) ' points='1395.68084 2646.18225 756.256189 180.144654 2002.53384 224.011277'%3E%3C/polygon%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  border-radius: 6px;
  padding: 24px 24px;
  height: 26px;
  box-shadow: 0 1px 9px 0 rgba(0, 0, 0, 0.16);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .icon {
    display: flex;
    color: var(--color-black);
    font-size: 24px;
    margin-right: 30px;
  }

  .name {
    color: var(--color-gray-700);
    font-family: var(--font-primary);
    font-weight: var(--font-semibold);
    padding-right: 20px;
    border-right: 1px solid var(--color-gray-400);
  }

  .nif {
    padding-left: 20px;
    font-family: var(--font-primary);
    color: var(--color-black);
  }

  .name-prefix {
    font-size: 18px;
    color: var(--color-gray-700);
    font-family: var(--font-primary);
    padding-right: 20px;
  }

  .user-info {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .user-login {
    padding-left: 20px;

    .logout-icon {
      display: flex;
      margin-right: 8px;
      font-size: 16px;
    }
  }
}
