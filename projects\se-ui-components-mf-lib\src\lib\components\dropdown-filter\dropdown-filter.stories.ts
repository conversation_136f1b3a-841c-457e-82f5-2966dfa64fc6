import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { DropdownFilterComponent } from './dropdown-filter.component';
import { ListType } from './dropdown-filter.model';
import { SeDropdownFilterModule } from './dropdown-filter.module';
import { SeButtonSizeEnum, SeButtonThemeEnum } from '../button';

const meta: Meta<DropdownFilterComponent> = {
  title: 'Components/Dropdown filter',
  component: DropdownFilterComponent,
  decorators: [
    moduleMetadata({
      imports: [SeDropdownFilterModule, ReactiveFormsModule],
    }),
  ],
  args: {
    placeholder: 'Select an option',
    type: ListType.CHECKBOX,
    showSelectedOptions: false,
    resetButton: {
      size: SeButtonSizeEnum.DEFAULT,
      btnTheme: SeButtonThemeEnum.ONLY_TEXT,
      disabled: false,
      label: 'SE_COMPONENTS.DROPDOWN.RESET',
    },
    applyButton: {
      size: SeButtonSizeEnum.DEFAULT,
      btnTheme: SeButtonThemeEnum.PRIMARY,
      disabled: false,
      label: 'SE_COMPONENTS.DROPDOWN.APPLY_FILTERS',
    },
    showClear: true,
    options: [
      {
        value: 'item-1',
        label: 'Item 1 with a very long example label',
        id: 'item-1',
      },
      { value: 'item-2', label: 'Item 2', id: 'item-2' },
      { value: 'item-3', label: 'Item 3', id: 'item-3' },
      { value: 'item-4', label: 'Item 4', id: 'item-4' },
      { value: 'item-5', label: 'Item 5', id: 'item-5' },
      { value: 'item-6', label: 'Item 6', id: 'item-6' },
      { value: 'item-7', label: 'Item 7', id: 'item-7' },
      { value: 'item-8', label: 'Item 8', id: 'item-8' },
      { value: 'item-9', label: 'Item 9', id: 'item-9' },
      { value: 'item-10', label: 'Item 10', id: 'item-10' },
      { value: 'item-11', label: 'Item 11', id: 'item-11' },
      { value: 'item-12', label: 'Item 12', id: 'item-12' },
    ],
  },
  argTypes: {
    options: {
      description: 'List of options to be displayed in the dropdown.',
      control: { type: 'object' },
      table: { defaultValue: { summary: 'undefined' } },
    },
    type: {
      description: 'The type of dropdown to be displayed.',
      control: { type: 'ListType' },
      table: { defaultValue: { summary: 'checkbox' } },
    },
    placeholder: {
      description:
        'The placeholder text for the dropdown when no option is selected.',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'Select an option' } },
    },
    showSelectedOptions: {
      description: 'Show the selected options.',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: 'false' } },
    },
    showClear: {
      description: 'Show clear icon',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: 'true' } },
    },
  },
  tags: ['autodocs'],
  render: (args) => {
    const form = new FormGroup({
      dropdownFilterForm: new FormControl(['item-1', 'item-2']),
    });

    return {
      template: `
        <form [formGroup]="form" style="height: 600px;">
          <div class="row">
            <div class="col-12 col-sm-4">
              <se-dropdown-filter
                (onSelectionChange)="onClick($event)"
                [formControlName]="'dropdownFilterForm'"
                [placeholder]="placeholder"
                [showClear]="showClear"
                [showSelectedOptions]="showSelectedOptions"
                [options]="options"
                [type]="type"
                [applyButton]="applyButton"
                [resetButton]="resetButton">
              </se-dropdown-filter>
            </div>
          </div>
        </form>
        {{form.value | json}}
      `,
      props: {
        ...args,
        form,
        onClick: (event: any) => {
          console.log('Clicked! - ', event);
          console.log('form - ', form.value);
        },
      },
    };
  },
};

export default meta;
type Story = StoryObj<DropdownFilterComponent>;

export const DropdownCheckbox: Story = {};
