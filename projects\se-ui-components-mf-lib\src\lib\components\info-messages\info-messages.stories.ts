import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { InfoMessagesComponent } from './info-messages.component';
import { SeInfoMessagesModule } from './info-messages.module';
import { SeButtonModule } from '../button';

const meta: Meta<InfoMessagesComponent> = {
  title: 'Components/Info Messages',
  component: InfoMessagesComponent,
  decorators: [
    moduleMetadata({
      imports: [SeInfoMessagesModule, SeButtonModule],
    }),
  ],
  args: {
    id: 'test',
    title: 'Title',
    image: 'calendar',
    text: [
      'First message',
      'Second message',
      'Third message'
    ],
    primaryButton: { label: 'Primary Button' },
    secondaryButton: { label: 'Secondary Button' },
    theme: 'primary',
    subtitle: '<b>Subtitle.</b> this is subtitle',
  },
  argTypes: {
    id: {
      description: 'Unique id for the info message.',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
    title: {
      description: 'Title of the info message.',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
    image: {
      description: 'Name of the icon to be displayed in the info message',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
    text: {
      description: 'Messages list',
      table: {
        defaultValue: { summary: '[]' },
      },
    },
    theme: {
      description: 'Theme of the info message',
      control: { type: 'select', options: ['primary', 'secondary'] },
      table: {
        defaultValue: { summary: 'primary' },
      },
    },
    primaryButton: {
      control: { type: 'object' },
      table: { defaultValue: { summary: '' } },
    },
    secondaryButton: {
      control: { type: 'object' },
      table: { defaultValue: { summary: '' } },
    }
  },
  tags: ['autodocs'],
  render: (args) => ({
    template: `
      <se-info-messages
        [id]="id"
        [title]="title"
        [image]="image"
        [text]="text"
        [theme]="theme"
        [primaryButton]="primaryButton"
        [secondaryButton]="secondaryButton"
        [subtitle]="subtitle">
      </se-info-messages>
    `,
    props: {
      ...args
    },
  }),
};


export default meta;
type Story = StoryObj<InfoMessagesComponent>;

export const Default: Story = {};

export const Secondary: Story = {
  args: { theme: 'secondary', image: 'co2-car' },
};
