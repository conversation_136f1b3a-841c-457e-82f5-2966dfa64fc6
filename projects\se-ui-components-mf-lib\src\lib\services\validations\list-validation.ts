import { AbstractControl, ValidationErrors, ValidatorFn } from "@angular/forms";
import { TranslateError } from "./validations.model";

export function listValidations(validations: TranslateError[]): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    
    const errors = validations.map(validation => {
      const validationError = validation.validator(control);
      const errorKey = validationError ? Object.keys(validationError)[0] : null;      
      const returnValidation = 
        (validation.translation || validation.translationByKey) && 
        validationError && errorKey;
      
      if (returnValidation) {
        const defaultValue = validationError[errorKey] instanceof Object ? 
          validationError[errorKey] : {[errorKey]: validationError[errorKey]};
        
        return {
          [errorKey] : {
            ...defaultValue, 
            translation: validation.translation,
            translateParams:validation.translateParams,
            translationByKey:validation.translationByKey,
            translationByKeyParams:validation.translationByKeyParams,
          }
        }
      }

      return validationError;

    }).filter(Boolean);

    return errors.length ? Object.assign({},...errors) : null;
  };
}