<div class="info-message d-flex m-0 gap-3 p-3" [ngClass]="'info-message__'+theme" [id]="id">
  <div class="info-message__left-content" *ngIf="image">
    <div class="info-message__img" >
      <img [src]="src" alt="" />
    </div>
  </div>

  <div class="info-message__content">
    <div *ngIf="title" class="title" [innerHTML]="title | translate | safeHtml"></div>
    <div *ngIf="subtitle" class="subtitle" [innerHTML]="subtitle"></div>
    <ul class="info-message__list" *ngIf="isArrayList && list.length">
      <li
        *ngFor="let text of list"
        [innerHTML]="text | translate | safeHtml"
        class="text-list"
      ></li>
    </ul>

    <span *ngIf="text && !isArrayList" [innerHtml]="text.toString() | safeHtml"></span>

    <div
      class="d-flex flex-row justify-content-between justify-content-sm-start mt-1 gap-2"
    >
      <se-button
        *ngIf="secondaryButton"
        [icon]="secondaryButton.icon"
        [btnTheme]="'secondary'"
        (onClick)="secondaryButtonClick.emit()"
      >
        {{ secondaryButton.label ?? '' | translate }}
      </se-button>
      <se-button
        *ngIf="primaryButton"
        [icon]="primaryButton.icon"
        [iconPosition]="primaryButton.iconPosition"
        [iconSize]="primaryButton.iconSize"
        (onClick)="primaryButtonClick.emit()"
        [btnTheme]="'primary'"
      >
        {{ primaryButton.label ?? '' | translate }}
      </se-button>
    </div>
  </div>
</div>
