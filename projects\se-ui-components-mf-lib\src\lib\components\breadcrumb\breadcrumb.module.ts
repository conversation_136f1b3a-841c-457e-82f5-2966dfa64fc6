import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { BreadcrumbComponent } from './breadcrumb.component';
import { RouterModule } from '@angular/router';
import { SeSharedModule } from '../../shared/shared.module';

@NgModule({
  declarations: [BreadcrumbComponent],
  imports: [CommonModule, RouterModule, SeSharedModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [BreadcrumbComponent],
})
export class SeBreadcrumbdModule {}
