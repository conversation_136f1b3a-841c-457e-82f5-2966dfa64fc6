import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { StepperComponent } from './stepper.component';
import { SeStepperdModule } from './stepper.module';
import { SeStep } from './stepper.model';

const meta: Meta<StepperComponent> = {
  title: 'Components/Stepper',
  component: StepperComponent,
  decorators: [
    moduleMetadata({
      imports: [SeStepperdModule],
    }),
  ],
  tags: ['autodocs'],
  args: {
    canNavigate: true,
    steps: [
      { id: '1', label: 'Step 1', status: 'PAST' },
      { id: '2', label: 'Step 2', status: 'ON' },
      { id: '3', label: 'Step 3', status: 'OFF' },
    ] as SeStep[],
  },
  argTypes: {
    steps: {
      description: 'Array of step objects with id, title, and status',
      control: { type: 'array', required: true },
      table: {
        defaultValue: { summary: [] },
      },
    },
  },
  render: (args) => ({
    props: args,
    template: `
      <se-stepper
        [canNavigate]="canNavigate"
        [steps]="steps">
      </se-stepper>
    `,
  }),
};

export default meta;
type Story = StoryObj<StepperComponent>;

export const Default: Story = {};

export const canNavigateFalse: Story = {
  args: {
    canNavigate: false,
    steps: [
      { id: '1', label: 'Step 1', status: 'PAST' },
      { id: '2', label: 'Step 2', status: 'PAST' },
      {
        id: '3',
        label: 'Step 3',
        status: 'ON',
        tooltipText: 'Step 3 - Testing tooltip',
      }
    ] as SeStep[],
  },
};

export const AllActive: Story = {
  args: {
    steps: [
      { id: '1', label: 'Step 1', status: 'ON' },
      { id: '2', label: 'Step 2', status: 'ON' },
      { id: '3', label: 'Step 3', status: 'ON', tooltipText: "Step 3 - Testing tooltip" }
    ] as SeStep[],
  },
};

export const NoSteps: Story = {
  args: {
    steps: [],
  },
};
