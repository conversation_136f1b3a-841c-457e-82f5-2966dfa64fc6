<div *ngIf="exceptions.length">
  <se-alert
    *ngFor="let exception of exceptions"
    [type]="exception.severity || 'error'"
    [title]="exception.title || '' | translate : exception.titleParams"
    [subtitle]="exception.subtitle || '' | translate : exception.subtitleParams"
    [titleClass]="exception.titleClass || 'semi-bold'"
    [subtitleClass]="'text-sm'"
    [closeButton]="exception.closable ?? true"
    (close)="closeError(exception)"
  >
  </se-alert>

</div>
