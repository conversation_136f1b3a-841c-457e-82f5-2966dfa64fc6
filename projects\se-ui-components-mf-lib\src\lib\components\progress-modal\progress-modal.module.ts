import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { ProgressBarModule } from 'primeng/progressbar';
import { ProgressModalComponent } from './progress-modal.component';
import { SeButtonModule } from '../button';


@NgModule({
  declarations: [ProgressModalComponent],
  imports: [
    TranslateModule.forChild(),
    ProgressBarModule,
    SeButtonModule,
    CommonModule
  ],
  exports: [ProgressModalComponent],
})
export class SeProgressModalModule { }
