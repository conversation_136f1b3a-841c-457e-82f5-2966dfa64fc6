import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CheckboxComponent } from './checkbox.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SeSharedModule } from '../../shared/shared.module';
import { SeFormControlErrorModule } from '../form-control-error/form-control-error.module';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    SeSharedModule,
    SeFormControlErrorModule,
    TranslateModule.forChild()
  ],
  declarations: [CheckboxComponent],
  exports: [CheckboxComponent],
})
export class SeCheckboxModule {}
