import { TemplateRef } from "@angular/core";

export enum SeStepStatus {
    PAST = 'PAST',
    OFF = 'OFF',
    ON = 'ON'
  }

export interface SeStep {
  id: string;
  label: string;
  status?: SeStepStatus;
  routerLink?: string;
  url?: string;
  tooltipText?: string | TemplateRef<HTMLElement> | undefined;
}

export class SeStepper implements SeStep {
  id: string;
  label: string;
  status: SeStepStatus;
  url?: string;
  tooltipText?: string | TemplateRef<HTMLElement> | undefined;

  constructor(id: string, label: string, url?: string, tooltipText?: string) {
    this.id = id;
    this.label = label;
    this.status = SeStepStatus.OFF;
    this.url = url;
    this.tooltipText = tooltipText;
  }
}
