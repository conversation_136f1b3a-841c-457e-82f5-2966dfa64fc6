import { Component, isDevMode } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { filter, Observable, Subject, takeUntil } from 'rxjs';
import {
  SeAuthService,
  SeLoginService,
  SePageLayoutService,
  SeStep,
  SeUser,
} from 'se-ui-components-mf-lib';

import { HeaderInfoService } from './core/services/header-info.service';
import { StepperService } from '@core/services';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
})
export class AppComponent {
  steps$: Observable<SeStep[]> = new Observable<SeStep[]>();
  presenterName: string | undefined;
  presenterNif: string | undefined;

  private destroyed$ = new Subject<void>();

  constructor(
    public router: Router,
    public pageLayoutService: SePageLayoutService,
    protected header: HeaderInfoService,
    private stepperService: StepperService,
    private loginService: SeLoginService,
    private authService: SeAuthService,
    private translateService: TranslateService,
  ) {
    this.router.initialNavigation();
    // Listen to the route changes (updates page layout)
    this.pageLayoutService.listenNavigation();
    // Listen steps changes
    this.stepperService.initializeAvailableRoutes();
    this.steps$ = this.pageLayoutService.getCurrentSteps();

    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this.destroyed$),
      )
      .subscribe(() => {
        // Scroll to top
        window.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
      });

    // Secured
    if (window.location.href.includes('/secured')) {
      this.getUser().then((user) => {
        this.setUserData(user);
      });
    }
  }

  private getUser(): Promise<SeUser> {
    return isDevMode() ? this.getUserDevMode() : this.getUserFromSession();
  }

  private async getUserDevMode(): Promise<SeUser> {
    const { content } = await this.loginService.login(true);
    return content?.usuario;
  }

  private getUserFromSession(): Promise<SeUser> {
    return Promise.resolve(this.authService.getSessionStorageUser());
  }

  private setUserData(user: SeUser): void {
    if (user) {
      this.presenterName = user?.nombrePresentador;
      this.presenterNif = user?.nifPresentador;
    }
  }

  protected onHelpButtonClick(): void {
    const URL = `https://atc.gencat.cat/${this.translateService.currentLang}/gestions/declaracions-informatives/`;
    window.open(URL, '_blank');
  }
}
