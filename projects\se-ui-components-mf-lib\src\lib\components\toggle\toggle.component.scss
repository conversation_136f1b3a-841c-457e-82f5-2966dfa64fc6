.toggle-container{
  display:flex;
  width: 100%;

  .toggle-label {
    font-family: var(--font-primary);
    text-align: center;
    height: 100%;
    padding: 8px;
    cursor: pointer;
    border: 1px solid;
    color: var(--color-primary-action);
    border-color: var(--color-primary-action);
    background-color: var(--color-white);

    &.disabled {
      cursor: not-allowed;
      background: transparent;
      color: var(--color-gray-400);
      border-color: var(--color-gray-400);
      text-decoration: none;
    }

    &.large {
      width: inherit;
    }

    &.default {
      width: 20%;
    }

    &.small {
      width: 10%;
    }
  }
  
  .toggle-label:hover,
  .toggle-label:focus {
    text-align: center;
    background-color:  var(--color-primary-action);
    color: var(--color-white);
    &.disabled {
      cursor: not-allowed;
      background: transparent;
      color: var(--color-gray-400);
      text-decoration: none;
      border-color: var(--color-gray-400);
    }
  }
  .toggle-label.active {
    text-align: center;
    background-color:  var(--color-primary-action);
    color: var(--color-white);
    &.disabled {
      cursor: not-allowed;
      background: var(--color-gray-500);;
      color: var(--color-white);
      text-decoration: none;
      border-color: var(--color-gray-400);
    }
  }
  .toggle-label.inactive {
    text-align: center;
    background-color:  var(--color-white);
    border-color: var(--color-primary-action);
    color: var(--color-primary-action);
  }
  .toggle-label.active:hover,
  .toggle-label.active:focus {
    text-align: center;
    background-color:  var(--color-primary-action);
    color: var(--color-white);
    &.disabled {
      cursor: not-allowed;
      background: var(--color-gray-500);;
      color: var(--color-white);
      text-decoration: none;
      border-color: var(--color-gray-400);
    }
  }

}




