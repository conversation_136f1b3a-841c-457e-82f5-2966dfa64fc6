import { HostListener, Injectable } from '@angular/core';
import { SeMessage, SeMessageI } from './message.model';
import {
  SeExceptionViewerAction,
  SeExceptionViewerEvent,
} from './exception-viewer.model';

@Injectable({
  providedIn: 'root',
})
export class SeMessageService {

  // Add message
  addMessages = (exceptions: SeMessageI[]): void => {
    // Add new messages
    // Send an event to the component that will show the messages
    const eventDetail: SeExceptionViewerEvent = {
      action: SeExceptionViewerAction.ADD,
      messages: exceptions,
    };

    document.dispatchEvent(
      new CustomEvent('exceptionsEvent', { detail: eventDetail })
    );

    // Scroll
    this.scroll();
  };

  /**
   * Scroll
   * @description When an exception is shown, scroll to the exceptions container. If the exceptions container wasn't found, scroll to the top.
   */
  scroll = (): void => {
    // When an exception is shown, scroll to the exceptions
    const domExContainer: HTMLElement = document.getElementById(
      'pt-exceptions-container'
    )!;
    if (domExContainer) {
      domExContainer.scrollIntoView({ block: 'start', behavior: 'smooth' });
    } else {
      window.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
    }
  };

  // Delete message
  deleteMessage = (item: SeMessage): void => {
    // Send an event to the component that will show the messages
    const eventDetail: SeExceptionViewerEvent = {
      action: SeExceptionViewerAction.DELETE,
      messages: [item],
    };
    
    document.dispatchEvent(
      new CustomEvent('exceptionsEvent', { detail: eventDetail })
    );
  };

  // Delete all messages
  resetMessages = (): void => {
    const eventDetail: SeExceptionViewerEvent = {
      action: SeExceptionViewerAction.RESET,
      messages: [],
    };

    // this.exceptions = [];

    document.dispatchEvent(
      new CustomEvent('exceptionsEvent', { detail: eventDetail })
    );
  };

  // Check duplicated messages
  isDuplicated = (exceptions: SeMessage[], item: SeMessage): boolean => {
    return exceptions.some((exc) => this.compareMsg(exc, item));
  };

  compareMsg = (a: SeMessage, b: SeMessage): boolean => {
    return a?.title === b?.title &&
    a?.severity === b?.severity &&
    a?.subtitle === b?.subtitle;
  }

}
