import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  Meta,
  StoryObj,
  applicationConfig,
  moduleMetadata,
} from '@storybook/angular';
import { DatepickerComponent } from './datepicker.component';
import { SeDatepickerModule } from './datepicker.module';
import { provideAnimations } from '@angular/platform-browser/animations';

const meta: Meta<DatepickerComponent> = {
  title: 'Components/Datepicker',
  component: DatepickerComponent,
  decorators: [
    moduleMetadata({
      imports: [SeDatepickerModule, ReactiveFormsModule],
    }),
    applicationConfig({
      providers: [provideAnimations()],
    }),
  ],
  args: {
    id: 'date1',
    label: 'Select Date',
    formControlName: 'selectedDate',
    minDate: new Date('2021-01-01'),
    maxDate: new Date('2023-12-31'),
    disabledDates: [],
    showTime: false,
    showIcon: true,
    placeholder: 'dd/mm/aaaa',
    inline: false,
    disabled: false,
    selectionMode: 'single',
    defaultDate: new Date('2021-01-01')
  },
  argTypes: {
    label: {
      description: 'Label for the input field.',
      control: { type: 'text' },
      table: { defaultValue: { summary: 'Input label' } },
    },
    disabled: {
      description: 'Determines if the input is disabled or not.',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    defaultDate: {
      description: 'Set the date to highlight on first opening if the field is blank.',
      control: { type: 'date' },
      table: { defaultValue: { summary: new Date() } },
    },
    showTime: {
      description: 'Determines if the time picker should be displayed.',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    tooltip: {
      description: 'tooltip options',
      control: { type: 'boolean' },
      table: { defaultValue: { summary: false } },
    },
    tooltipText: {
      description: 'tooltip message',
      control: { type: 'text' },
      table: { defaultValue: { summary: '' } },
    },
  },
  tags: ['autodocs'],
  render: (args) => ({
    template: `
      <form [formGroup]="formGroup" style="min-height: 450px;">
        <se-datepicker
          [id]="id"
          [label]="label"
          [formControlName]="formControlName"
          [minDate]="minDate"
          [maxDate]="maxDate"
          [disabledDates]="disabledDates"
          [showTime]="showTime"
          [showIcon]="showIcon"
          [placeholder]="placeholder"
          [inline]="inline"
          [disabled]="disabled"
          [selectionMode]="selectionMode"
          [tooltip]="tooltip"
          [tooltipText]="tooltipText"
          [defaultDate]="defaultDate"
          (dateSelectedEvent)="handleDateSelected($event)"
        >
        </se-datepicker>
      </form>
    `,
    props: {
      ...args,
      formGroup: new FormGroup({
        selectedDate: new FormControl(null,
          Validators.required
        ),
      }),
      handleDateSelected: (date: Date) => {
        console.log(`Date selected: ${date}`);
      },
    },
  }),
};

export default meta;
type Story = StoryObj<DatepickerComponent>;

export const Datepicker: Story = {};
export const sinLabel: Story = { args: { label: undefined } };
export const labelVacio: Story = { args: { label: '' } };
export const WithTooltip: Story = { args: { tooltip: true, tooltipText: "Esto es un tooltip" } };
export const DisabledDatepicker: Story = {
  args:{
    id:'datepickerDisabled',
    label:'Disabled',
    showIcon: true,
    disabled: true,
  }
}

export const RangeSelectionDatepicker: Story = {
  args:{
    id:'datepickerDisabled',
    label:'Disabled',
    showIcon: true,
    selectionMode: 'range'
  }
}

export const MultipleSelectionDatepicker: Story = {
  args:{
    id:'datepickerDisabled',
    label:'Disabled',
    showIcon: true,
    selectionMode: 'multiple',
  },
}
