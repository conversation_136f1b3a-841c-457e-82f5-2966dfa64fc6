import { HttpHeaders, HttpParams } from '@angular/common/http';
import { SeMessage, SeMessageI } from '../message/message.model';
import { SeDropdownOption } from '../../components/dropdown/dropdown.model';

export interface SeHttpOptions {
  headers: HttpHeaders | { [x: string]: string | string[] };
  params: HttpParams | { [x: string]: string | string[] };
  responseType: any;
  reportProgress: boolean;
  body?: { [x: string]: any } | any;
}

export interface SeHttpRequest {
  url: string;
  baseUrl?: string;
  method: 'get' | 'post' | 'put' | 'delete';
  params?: HttpParams | { [x: string]: string | string[] };
  body?: { [x: string]: any } | any;
  headers?: string | { [name: string]: string | string[] } | undefined;
  responseType?: any; // 'arraybuffer' | 'blob' | 'text' | 'json'
  reportProgress?: boolean;
  timeout?: number | Date;
  parseResult?: boolean;
  spinner?: boolean;
  spinnerMsg?: string;
  spinnerSubtitle?: string;
  returnType?: 'promise' | 'observable';
  clearExceptions?: boolean;
  suppressErrorMessage?: boolean;
}

export interface SeHttpResponse<T = any> {
  messages?: SeHttpBackendMessage[];
  content?: T;
}

export interface DownloadResponse {
  base64File: string;
  format: string;
  length: number;
}

export interface DownloadHttpResponse extends SeHttpResponse {
  content?: DownloadResponse;
}

export interface SeHttpBackendMessage extends SeMessageI {
  code: number | null;
}

export class SeHttpBackendMessage extends SeMessage {
  code: number | null = null;

  constructor(
    code: number | null,
    severity: 'info' | 'warning' | 'error' | 'success',
    title?: string,
    subtitle?: string,
    closable?: boolean,
    closableFn?: () => void,
    titleParams?: {},
    subtitleParams?: {}
  ) {
    super(
      severity,
      title,
      subtitle,
      closable,
      closableFn,
      titleParams,
      subtitleParams
    );
    this.code = code || null;
  }
}

// Response READ: List
export interface ResponseList extends SeHttpResponse {
  content: SeDropdownOption[];
}
