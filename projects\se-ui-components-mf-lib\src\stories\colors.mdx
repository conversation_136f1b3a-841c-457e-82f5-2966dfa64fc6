import { Meta, ColorPalette, ColorItem } from "@storybook/blocks";

<Meta title="Core/Colors" />

# Color

Els colors transmeten la personalitat de la nostra marca, però també ajuden els usuaris a navegar per la interfície. La combinació correcta de colors pot influir en com se senten, pensen i es comporten.
El color és una eina utilitzada per expressar estil, evocar emocions i comunicar significat. Una paleta de colors estandarditzada i la seva aplicació intencionada garanteixen una experiència familiar, còmoda i consistent.

## 1.1 Colors primaris

<ColorPalette>
  <ColorItem
    title="color-primary-action"
    subtitle="Color corporativo. Acciones principales. Botones, Etiquetas, Toggle"
    colors={{ MarcaAzul: "var(--color-primary-action)" }}
  />
  <ColorItem
    title="color-primary-link"
    subtitle="Color corporativo. Enlaces, Indicadores elementos de posición, Iconos al pie de página"
    colors={{ MarcaRosa: "var(--color-primary-link)" }}
  />
</ColorPalette>

## 1.2 Colors secundaris

Els colors secundaris s'empren per a detalls i complements, així com per accentuar elements dins d'un context. Poden representar elements secundaris. No s'han d'utilitzar en excés ni prevaldre sobre els colors primaris en cap moment. També es poden utilitzar en gràfics.

<ColorPalette>
  <ColorItem
    title="Blue"
    subtitle="color-blue-{number}"
    colors={{
      700: "var(--color-blue-700)",
      600: "var(--color-blue-600)",
      500: "var(--color-blue-500)",
      400: "var(--color-blue-400)",
      300: "var(--color-blue-300)",
      200: "var(--color-blue-200)",
      100: "var(--color-blue-100)",
    }}
  />
  <ColorItem
    title="Pink"
    subtitle="color-pink-{number}"
    colors={{
      700: "var(--color-pink-700)",
      600: "var(--color-pink-600)",
      500: "var(--color-pink-500)",
      400: "var(--color-pink-400)",
      300: "var(--color-pink-300)",
      200: "var(--color-pink-200)",
      100: "var(--color-pink-100)",
    }}
  />
</ColorPalette>

## 1.3 Escala de grisos

Els colors grisos tenen tons clars i foscos que s'utilitzaran per crear contrast i jerarquia.

En el disseny, els grisos són molt útils per aportar subtilesa i equilibri a una paleta de colors. Els tons clars de grisos poden ser emprats per a fons o espais més suaus, mentre que els tons foscos de grisos poden destacar elements importants o ser utilitzats per aportar profunditat a la interfície.

El contrast entre els grisos i altres colors és clau per a guiar l'atenció dels usuaris cap a elements específics o jerarquitzar la informació. Això permet que els usuaris puguin identificar fàcilment els elements més rellevants en una pàgina o aplicació.

L'ús adequat dels colors grisos en el disseny pot ajudar a millorar la legibilitat, l'experiència de l'usuari i l'aspecte general del producte o servei digital.

<ColorPalette>
  <ColorItem
    title="All gray tones"
    subtitle="color-gray-{number}"
    colors={{
      Black: "var(--color-black)",
      700: "var(--color-gray-700)",
      600: "var(--color-gray-600)",
      500: "var(--color-gray-500)",
      400: "var(--color-gray-400)",
      300: "var(--color-gray-300)",
      200: "var(--color-gray-200)",
      100: "var(--color-gray-100)",
    }}
  />
  <ColorItem
    title="Text"
    subtitle="Gray tones for text"
    colors={{
      Inverse: "var(--color-white)",
      Base: "var(--color-gray-500)",
      Medium: "var(--color-gray-600)",
      Dark: "var(--color-gray-700)",
      Black: "var(--color-black)",
    }}
  />
  <ColorItem
    title="Backgrounds and lines"
    subtitle="Backgrounds and lines"
    colors={{
      Inverse: "var(--color-white)",
      "Background extra light": "var(--color-gray-100)",
      "Background light": "var(--color-gray-200)",
      "Lines light": "var(--color-gray-300)",
      "Background Disabled": "var(--color-gray-400)",
    }}
  />
</ColorPalette>

## 1.4 Colors semàntics

Els colors seleccionats en la paleta compartida s'utilitzen específicament per comunicar retroalimentació, estat o urgència. Aquests es coneixen com a colors semàntics i sempre han de transmetre informació important.

<ColorPalette>
  <ColorItem
    title="Error"
    subtitle="color-red-{number}"
    colors={{
      500: "var(--color-red-500)",
      400: "var(--color-red-400)",
      300: "var(--color-red-300)",
      200: "var(--color-red-200)",
      100: "var(--color-red-100)",
      50: "var(--color-red-50)",
    }}
  />
  <ColorItem
    title="Warning"
    subtitle="color-orange-{number}"
    colors={{
      500: "var(--color-orange-500)",
      400: "var(--color-orange-400)",
      300: "var(--color-orange-300)",
      200: "var(--color-orange-200)",
      100: "var(--color-orange-100)",
      50: "var(--color-orange-50)",
    }}
  />
  <ColorItem
    title="Success"
    subtitle="color-green-{number}"
    colors={{
      400: "var(--color-green-400)",
      300: "var(--color-green-300)",
      200: "var(--color-green-200)",
      100: "var(--color-green-100)",
      50: "var(--color-green-50)",
    }}
  />
  <ColorItem
    title="Neutral"
    subtitle="color-purple-{number}"
    colors={{
      500: "var(--color-purple-500)",
      400: "var(--color-purple-400)",
      300: "var(--color-purple-300)",
      200: "var(--color-purple-200)",
      100: "var(--color-purple-100)",
    }}
  />
</ColorPalette>
