import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  forwardRef,
  HostListener,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  ControlContainer,
  ControlValueAccessor,
  FormBuilder,
  FormControl,
  FormGroup,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { Nullable } from '../../models';
import { generateUniqueId } from '../../shared/utils/generate-unique-id/generate-unique-id';
import { SeButton } from '../button';
import { SeCheckbox } from '../checkbox/checkbox.model';
import { ListType } from './dropdown-filter.model';
import { NgIcon } from '@ng-icons/core';

@Component({
  selector: 'se-dropdown-filter',
  template: `
    <div class="se-dropdown-filter" [id]="id">
      <div
        class="dropdown-header"
        (click)="toggleDropdown()"
        tabindex="0"
        (keydown)="handleKeyboardEvent($event)"
      >
        <div
          class="dropdown-title text-sm"
          [ngClass]="{
            'dropdown-title-selected': selectedItemsCount > 0 && !showClear,
            'dropdown-title-selected__clear-icon': selectedItemsCount > 0 && showClear,
          }"

        >
          {{ placeholder }}
          <div
            class="selected-count-badge"
            *ngIf="selectedItemsCount > 0 && !showSelectedOptions"
          >
            <se-badge
              [badgeTheme]="'info'"
              [textInsideCircle]="selectedItemsCount.toString()"
            ></se-badge>
          </div>
          <ng-container *ngIf="showSelectedOptions">
            <div
              *ngFor="let item of selectedItems"
              class="selected-options-badge"
            >
              <se-badge
                [badgeTheme]="'info'"
                [textInsideCircle]="item.label"
              ></se-badge>
            </div>
          </ng-container>
        </div>
        <ng-icon
          *ngIf="showClear && selectedItemsCount > 0"
          [name]="'matCloseOutline'"
          id="clear-icon"
          (click)="resetForm($event)"
          tabindex="0"></ng-icon>
        <ng-icon #ngIcon id="drop-icon" [name]="getIcon()"></ng-icon>
      </div>
      <div
        class="dropdown-list"
        *ngIf="dropdownOpen"
        [ngClass]="{ open: dropdownOpen }"
      >
        <div class="options-list" [ngStyle]="{ 'max-height': scrollHeight }">
          <ng-container *ngIf="type === ListType.CHECKBOX">
            <form [formGroup]="form">
              <ng-container *ngFor="let item of options">
                <div *ngIf="dropdownOpen" class="dropdown-filter-checkbox">
                  <se-checkbox
                    [formControlName]="formControlName"
                    [value]="item.value"
                    [label]="item.label ?? ''"
                    (onClick)="onItemClicked()"
                    [binary]="false"
                    [id]="item.id"
                    [styleClass]="'label-ellipsis'"
                  >
                  </se-checkbox>
                </div>
              </ng-container>
            </form>
          </ng-container>
        </div>
        <div ngClass="action-buttons" *ngIf="resetButton || applyButton">
          <se-button
            *ngIf="resetButton"
            (onClick)="resetForm()"
            [size]="'small'"
            [btnTheme]="'onlyText'"
            [disabled]="!!resetButton.disabled"
            >{{ resetButton.label || '' | translate }}</se-button
          >
          <se-button
            *ngIf="applyButton"
            (onClick)="emitApply()"
            [size]="'small'"
            [disabled]="!!applyButton.disabled"
            >{{ applyButton.label || '' | translate }}</se-button
          >
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./dropdown-filter.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DropdownFilterComponent),
      multi: true,
    },
  ],
})
export class DropdownFilterComponent
  implements ControlValueAccessor, OnInit, AfterViewInit
{
  @ViewChild('ngIcon', {static:false}) set ngIcon(value: NgIcon) {
    if(value) {
      const ngIconSvg = ((value?.['elementRef'] as ElementRef).nativeElement?.firstElementChild as SVGElement);

      ngIconSvg.id = `dropdown-ng-icon-svg-${this.id}`;
    }
  }

  @Input() placeholder: string = 'SE_COMPONENTS.DROPDOWN.PLACEHOLDER';
  @Input() options: SeCheckbox[] | any[] | undefined;
  @Input() type: ListType = ListType.CHECKBOX;
  @Input() formControlName: string | number | null = null;
  @Input() resetButton: SeButton | undefined;
  @Input() applyButton: SeButton | undefined;
  @Input() scrollHeight: string = '460px';
  @Input() showSelectedOptions: boolean = false;
  @Input() id: string = 'dropdown_id_' + generateUniqueId();
  @Input() showClear: boolean = true;

  @Output() onSelectionChange: EventEmitter<any[]> = new EventEmitter<any[]>();
  @Output() onApply: EventEmitter<any[]> = new EventEmitter<any[]>();

  protected form: FormGroup;

  private defaultValue: Nullable<any>;
  private defaultFormValue: Nullable<any>;

  protected dropdownOpen = false;
  protected ListType = ListType;
  protected control: FormControl | undefined;
  protected value: any[] = [];

  get selectedItemsCount(): number {
    return this.value.length;
  }

  get selectedItems(): SeCheckbox[] | any [] | undefined {
    return this.options?.filter(option => this.value.includes(option?.['id']));
  }

  private onChange: (value: any) => void = () => {};
  private onTouched: () => void = () => {};

  constructor(
    private fb: FormBuilder,
    private controlContainer: ControlContainer
  ) {
    this.form = this.fb.group({});
  }

  ngOnInit(): void {
    this.control = this.getFormControl();
    this.buildForm();
  }

  ngAfterViewInit(): void {
    this.defaultValue = this.value;
    this.defaultFormValue = this.control?.value;
  }

  setDefaultFormValue(): void {
    this.writeValue(this.defaultValue);
    this.value = this.defaultValue;
    this.form.get(this.formControlName!.toString())?.setValue(this.defaultFormValue);
    if (this.control) this.control.setValue(this.defaultValue);
    // this.onApply.emit(this.defaultValue);
    this.form.updateValueAndValidity();
  }

  buildForm(): void {
    this.form.addControl(
      this.formControlName!.toString(),
      this.fb.control(this.value)
    );
  }

  toggleDropdown(): void {
    this.dropdownOpen = !this.dropdownOpen;
    if (!this.dropdownOpen && !this.applyButton) {
      this.emitApply();
    }
    if (this.dropdownOpen && this.applyButton) {
      this.defaultValue = this.value;
      this.defaultFormValue = this.control?.value;
    }
    this.onTouched();
  }

  getIcon(): string {
    return this.dropdownOpen ? 'matExpandLessOutline' : 'matExpandMoreOutline';
  }

  onItemClicked(): void {
    const value = this.form.get(this.formControlName!.toString())?.value;

    if (this.control) {
      this.control.setValue(value);
    }

    this.onChange(this.value);
    this.onSelectionChange.emit(this.value);
    this.onTouched();
  }

  handleKeyboardEvent(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      this.toggleDropdown();
    }
  }

  writeValue(value: any[]): void {
    this.value = value || [];
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  private getFormControl(): FormControl {
    if (this.control) {
      return this.control;
    }
    return (this.controlContainer.control as FormGroup).get(
      this.formControlName!.toString()
    ) as FormControl;
  }

  emitApply(): void {
    this.onApply.emit(this.value);
    this.dropdownOpen = false;
  }

  resetForm(event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.form.reset();
    this.value = [];
    this.onChange(this.value);
    this.onApply.emit(this.value);
    this.onTouched();
    this.dropdownOpen = false;
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent): void {
    const targetElement = event.target as HTMLElement;
    event.stopPropagation();

    if (
      this.dropdownOpen &&
      !targetElement.closest(`.se-dropdown-filter#${this.id}`) &&
      !targetElement.closest(`svg#dropdown-ng-icon-svg-${this.id}`)
    ) {
      if (this.applyButton) {
        this.setDefaultFormValue();
      }
      this.dropdownOpen = false;
    }
  }
}
