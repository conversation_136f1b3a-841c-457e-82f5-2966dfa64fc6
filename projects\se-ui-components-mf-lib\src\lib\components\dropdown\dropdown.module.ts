import { NgModule } from '@angular/core';
import { DropdownModule } from 'primeng/dropdown';
import { DropdownComponent } from './dropdown.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { TimesIcon } from 'primeng/icons/times';
import { ChevronUpIcon } from 'primeng/icons/chevronup';
import { ChevronDownIcon } from 'primeng/icons/chevrondown';
import { SeFormControlErrorModule } from '../form-control-error/form-control-error.module';
import { SeSharedModule } from '../../shared/shared.module';
import { SeAriaHiddenFocusableDirective } from '../../directives/aria-hidden-focusable/aria-hidden-focusable.directive';

@NgModule({
  imports: [
    CommonModule, 
    FormsModule, 
    ReactiveFormsModule, 
    SeSharedModule,
    DropdownModule,
    TranslateModule,
    TimesIcon,
    ChevronUpIcon,
    ChevronDownIcon,
    SeFormControlErrorModule,
    SeAriaHiddenFocusableDirective
  ],
  declarations: [DropdownComponent],
  exports: [DropdownComponent],
})
export class SeDropdownModule {}
