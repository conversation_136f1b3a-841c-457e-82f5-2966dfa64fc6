import { AbstractControl, ValidatorFn, ValidationErrors } from '@angular/forms';
/**
 * Comprueba si la fecha del controlador es menor que la fecha minima o maxima introducida y devolvera un mensaje de error con el texto introducido
 *
 * @param min Fecha o form control minima
 * @param max Fecha o form control maxima
 * @param translateErrorMin literal o texto para tranlate error min
 * @param translateErrorMax literal o texto para tranlate error min
 * @returns Mensaje de error con el literal del error min o maximo
 */
export const dateRangeValidator = (
  min: AbstractControl | Date |null,
  max: AbstractControl | Date |null,
  translateErrorMin?: string,
  translateErrorMax?: string
): ValidatorFn => {
  return (formGroup: AbstractControl<Date>): ValidationErrors | null => {
    let result: ValidationErrors | null = null;
    let dateMin = min instanceof AbstractControl ? min.value : min;
    let dateMax = max instanceof AbstractControl ? max.value : max;
    if (min !== null && formGroup.value !== null && formGroup.value < dateMin) {
      result = { dateRange: { translation: translateErrorMin }};
    } else if (max !== null && formGroup.value !== null && formGroup.value > dateMax) {
      result = { dateRange: { translation: translateErrorMax }};
    }
    return result;
  };
};
