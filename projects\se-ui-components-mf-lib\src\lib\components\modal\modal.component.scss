@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

.modal-container {
  font-family: var(--font-primary);
  width: 100%;
  height: 100%;

  .modal-subtitle {
    white-space: pre-line;
  }

  .modal-content {
    border: none;
    border-radius: 0;

    &.alert-default {
      .modal-header {
        display: block;
        text-align: left;
        background-color: var(--color-blue-200);
      }
      .modal-footer {
        background-color: var(--color-gray-200);
      }
      .modal-body {
        padding: 24px;
      }
    }

    &.se-alert-danger {
      border-top: 4px solid var(--color-red-400);
      .modal-header {
        background-color: var(--color-red-50);
        .text-2xl {
          color: var(--color-red-400);
        }
      }
    }

    &.se-alert-success {
      border-top: 4px solid var(--color-green-300);
      .modal-header {
        background-color: var(--color-green-50);
        .text-2xl {
          color: var(--color-green-300);
        }
      }
    }

    &.se-alert-warning {
      border-top: 4px solid var(--color-orange-300);
      .modal-header {
        background-color: var(--color-orange-50);
        .text-2xl {
          color: var(--color-orange-300);
        }
      }
    }

    &.se-alert-info {
      border-top: 4px solid var(--color-blue-600);
      .modal-header {
        background-color: var(--color-blue-200);
        .text-2xl {
          color: var(--color-blue-600);
        }
      }
    }

    &.se-alert-danger,
    &.se-alert-success,
    &.se-alert-warning,
    &.se-alert-info {
      .modal-header {
        display: block;
        text-align: center;
        border-radius: 0;
      }

      .modal-footer {
        background-color: var(--color-gray-200);
        border-radius: 0;
      }
    }

    .modal-header {
      padding: 24px;
      width: 100%;
      position: relative;

      .close-button {
        font-size: 16px;
        position: absolute;
        top: 20px;
        right: 20px;
        cursor: pointer;
        border: none;
        background-color: transparent;
        padding: 0;
      }
    }

    .modal-body {
      padding: 24px;
    }

    .modal-footer {
      padding: 1rem;
      padding-bottom: 2rem;

      @include media-breakpoint-up(md) {
        padding: 1.5rem;
      }

      .action-buttons {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        @include media-breakpoint-up(md) {
          flex-direction: row;
          justify-content: flex-end;
        }

        se-button,
        se-button ::ng-deep .se-button {
          width: 100%;

          @include media-breakpoint-up(md) {
            width: auto;
          }
        }
      }
    }
  }
}

.no-border {
  .modal-content {
    border-top: none !important;
    .modal-header {
      text-align: left !important;
    }
  }
}

::ng-deep {
  .modal-sidebar,
  .modal-fullscreen {
    .modal-container {
      min-height: calc(100vh - 2px);

      .modal-content{
        .modal-body {
          padding: 24px;
          max-width: 1228px;
          left: 50%;
          transform: translateX(-50%);
          overflow: hidden;
        }

        .modal-footer {
          .action-buttons{
            max-width: 1180px;
            margin: auto;
          }
        }

        .modal-header .modal-title{
          max-width: 1180px;
          margin: auto;
          
          .back-button {
            display: flex;
            align-items: center;

            > button {
              margin-bottom: 8px;
              color: var(--color-primary-action);
              background: transparent;
              border: none;
              display: inline-flex;
              justify-content: center;
              padding: 0;
              gap: 4px;

              &:hover {
                color: var(--color-blue-700);
                border: none;
              }

              > span {
                font-size: var(--text-sm);
                line-height: var(--line-sm);
                font-family: var(--font-primary);
                font-weight: var(--font-semibold);
              }

              .arrow-back {
                font-size: var(--text-lg);
                margin-left: -4px;
              }
            }
          }
        }
        
      }
    }
  }

  .modal-fullscreen {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
}

// RESPONSIVE

::ng-deep {
  // @media (min-width: 768px) {
  @include media-breakpoint-up(md) {
    .modal-dialog {
      &.modal-lg,
      &.modal-xl {
        min-width: 700px;
      }
    }
  }
}
