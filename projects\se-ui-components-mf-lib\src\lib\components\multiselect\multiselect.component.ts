import {
  AfterViewInit,
  Component,
  Input,
  OnInit,
  ViewChild,
  forwardRef,
} from '@angular/core';
import {
  ControlValueAccessor,
  FormBuilder,
  FormControl,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { SelectModel } from './multiselect.model';
import { MultiSelect } from 'primeng/multiselect';

@Component({
  selector: 'se-multiselect',
  template: `
    <p-multiSelect
      #multiSelect
      [ariaLabel]="ariaLabel"
      [id]="'multiselect'"
      [inputId]="id"
      [formControl]="formControl"
      [options]="filteredOptions"
      [optionValue]="optionValue"
      [optionLabel]="optionLabel"
      [display]="display"
      [disabled]="disabled"
      [filter]="filter"
      [showToggleAll]="showToggleAll"
      [showHeader]="showHeader"
      [maxSelectedLabels]="maxSelectedLabels"
      [selectedItemsLabel]="selectedItemsLabel"
      [ngClass]="hasErrors() ? 'token-error' : ''"
      [placeholder]="placeholder"
      (onPanelHide)="isPanelShown = false"
      (onPanelShow)="isPanelShown = true"
      [showClear]="showClear"
      (onPanelShow)="setPanelAria()"
    >

      <!-- Block browser translation on options -->
      <ng-template let-option pTemplate="item">
        <div class="scrollable-label-wrapper">
          <span [attr.translate]="allowTranslations === false ? 'no' : null">{{ option.label }}</span>
        </div>
      </ng-template>

      <ng-template pTemplate="removetokenicon">
        <ng-icon class="closeIcon" name="matCloseOutline"></ng-icon>
      </ng-template>

      <ng-template pTemplate="filter">
        <form [formGroup]="componentForm" class="row multiselect-input">
          <div class="col-12">
            <se-input
              [buttonClass]="'secondary'"
              class="multiselect-input"
              [showValidation]="false"
              [formControlName]="'multiselectFilter'"
              [id]="'multiselectFilter'"
              [icon]="'matSearchOutline'"
              (onClick)="filterList()"
              [searchButtonTooltip]="searchButtonTooltipText"
            ></se-input>
          </div>
        </form>
      </ng-template>

      <ng-template pTemplate="clearicon">
        <ng-icon class="clear-icon"
          [name]="'matCloseOutline'"
          [attr.tabindex]="hasSelectedItems ? -1 : 0"
          [attr.aria-hidden]="hasSelectedItems"
        ></ng-icon>
      </ng-template>

      <ng-template pTemplate="dropdownicon">
        <ng-icon
          class="dropdown-icon"
          *ngIf="!isPanelShown"
          [name]="'matExpandMoreOutline'"
        ></ng-icon>
        <ng-icon
          class="dropdown-icon"
          *ngIf="isPanelShown"
          [name]="'matExpandLessOutline'"
        ></ng-icon>
      </ng-template>
    </p-multiSelect>
    <se-error-message [control]="formControl"></se-error-message>
  `,
  styleUrls: ['./multiselect.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => MultiselectComponent),
      multi: true,
    },
  ],
})
export class MultiselectComponent
  implements ControlValueAccessor, AfterViewInit, OnInit
{
  @ViewChild('multiSelect') multiSelect!: MultiSelect;

  @Input() id: string | undefined;
  @Input() ariaLabel: string | undefined;
  @Input() showHeader: boolean = false;
  @Input() showToggleAll: boolean = false;
  @Input() disabled: boolean = false;
  @Input() filter: boolean = false;
  @Input() set options(options: SelectModel[]) {
    this.optionsList = options;
    this.filteredOptions = options;
  }
  @Input() display: 'comma' | 'chip' = 'chip';
  @Input() optionValue: keyof SelectModel = 'id';
  @Input() optionLabel: string = 'label';
  @Input() maxSelectedLabels: number = 3;
  @Input() selectedItemsLabel: string = 'ellipsis';
  @Input() formControlName!: string;
  @Input() placeholder: string = '';
  @Input() showClear: boolean = false;
  @Input() allowTranslations: boolean = true;
  @Input() searchButtonTooltipText: string = '';

  isPanelShown: boolean = false;

  formControl = new FormControl();
  componentForm = this.fb.group({
    multiselectFilter: [''],
  });
  optionsList: SelectModel[] = [];
  filteredOptions: SelectModel[] = [];

  constructor(private fb: FormBuilder) {
    //empty
  }
  ngOnInit(): void {
    this.optionsList.forEach((option, index) => {
      option.posInSet = (index + 1).toString();
    });
  }

  ngAfterViewInit(): void {
    this.setCollapsedAriaAttributes();
  }

  setCollapsedAriaAttributes(): void {
    setTimeout(() => {
      const combobox =
        this.multiSelect?.containerViewChild?.nativeElement.querySelector(
          'div .p-hidden-accessible input'
        );

      if (combobox) {
        combobox.role = 'combobox';
        combobox.setAttribute('aria-expanded', false);
        combobox.setAttribute('aria-label', this.id);
      }
    }, 0);
  }

  setPanelAria(): void {
    setTimeout(() => {
      const container: HTMLElement =
        this.multiSelect?.containerViewChild?.nativeElement;
      const overlayEl: HTMLElement =
        this.multiSelect?.overlayViewChild?.el?.nativeElement;

      if (overlayEl) {
        overlayEl.setAttribute('role', 'region');
        overlayEl.setAttribute('aria-label', this.ariaLabel!);
      }

      const list = container?.querySelector<HTMLUListElement>(
        'div p-overlay .p-overlay.p-component .p-overlay-content .p-multiselect-panel .p-multiselect-items-wrapper ul'
      );
      const listItems = container?.querySelectorAll<HTMLLIElement>(
        'div p-overlay .p-overlay.p-component .p-overlay-content .p-multiselect-panel .p-multiselect-items-wrapper ul .p-element li'
      );
      const multiSelectItems = container?.querySelectorAll<HTMLElement>(
        'div p-overlay .p-overlay.p-component .p-overlay-content .p-multiselect-panel .p-multiselect-items-wrapper ul p-multiselectitem'
      );
      const hiddenSpans = container?.querySelectorAll<HTMLSpanElement>(
        'div p-overlay .p-overlay.p-component .p-overlay-content .p-multiselect-panel .p-hidden-accessible'
      );

      if (list) {
        list.setAttribute('role', 'listbox');
        list.setAttribute('aria-label', 'Available options');
        list.removeAttribute('aria-multiselectable');
      }

      listItems?.forEach((item) => {
        item.setAttribute('tabindex', '-1');
        item.setAttribute('aria-hidden', 'true');
      });

      multiSelectItems?.forEach((item, index) => {
        const posInSet = index + 1;
        item.setAttribute('role', 'option');
        item.setAttribute('tabindex', '0');
        item.setAttribute('aria-posinset', posInSet.toString());
        item.setAttribute('aria-setsize', multiSelectItems.length.toString());

        const option = this.optionsList.find(
          (opt) => opt.posInSet === posInSet.toString()
        );
        item.setAttribute(
          'aria-selected',
          option?.selected?.toString() || 'false'
        );

        item
          .querySelector('.p-checkbox .p-checkbox-box')
          ?.removeAttribute('aria-checked');

        item.onkeydown = (ev: KeyboardEvent) => {
          if (ev.key === 'Enter' || ev.key === ' ') {
            ev.preventDefault();
            const selectedOption = this.optionsList.find(
              (opt) => opt.posInSet === posInSet.toString()
            );
            if (selectedOption) {
              selectedOption.selected = !selectedOption.selected;
              item.setAttribute(
                'aria-selected',
                selectedOption.selected.toString()
              );
              const currentValue = this.formControl.value || [];
              const valueKey = selectedOption[this.optionValue];
              this.formControl.setValue(
                selectedOption.selected
                  ? [...currentValue, valueKey]
                  : currentValue.filter((val: any) => val !== valueKey)
              );
            }
          } else if (ev.key === 'ArrowDown' || ev.key === 'ArrowUp') {
            ev.preventDefault();
            const nextIndex =
              ev.key === 'ArrowDown'
                ? Math.min(index + 1, multiSelectItems.length - 1)
                : Math.max(index - 1, 0);
            multiSelectItems[nextIndex]?.focus();
          }
        };
      });

      hiddenSpans?.forEach((span) => span.setAttribute('tabindex', '-1'));

      const multiSelectItemsWrapper = overlayEl?.querySelector(
        '.p-multiselect-items'
      );
      multiSelectItemsWrapper
        ?.querySelectorAll<HTMLElement>('p-multiselectitem')
        .forEach((item) => {
          const label =
            item.querySelector('li')?.getAttribute('aria-label') || 'option';
          item.setAttribute('aria-label', label);
        });
    }, 0);
  }
  private onChange: (value: any) => void = () => {};
  private onTouched: () => void = () => {};

  hasErrors = (): boolean =>
    this.formControl.get(this.formControlName)?.errors !== undefined;

  onOptionSelected(event: any) {
    this.onChange(event.value);
  }

  writeValue(value: any): void {
    this.formControl.setValue(value);
  }

  registerOnChange(fn: (value: any) => void): void {
    this.onChange = fn;
    this.formControl.valueChanges.subscribe(fn);
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  filterList() {
    const filterValue =
      this.componentForm.get('multiselectFilter')?.value?.toLowerCase() ?? '';
    const selectedValues = this.formControl.value || [];

    const filtered = this.optionsList.filter(
      (option) =>
        option.label.toLowerCase().includes(filterValue) ||
        selectedValues.includes(option[this.optionValue])
    );

    const selectedOptions = filtered.filter((option) =>
      selectedValues.includes(option[this.optionValue])
    );
    const nonSelectedOptions = filtered.filter(
      (option) => !selectedValues.includes(option[this.optionValue])
    );

    selectedOptions.sort((a, b) => a.label.localeCompare(b.label));
    nonSelectedOptions.sort((a, b) => a.label.localeCompare(b.label));

    this.filteredOptions = [...selectedOptions, ...nonSelectedOptions];
  }

  get hasSelectedItems(): boolean {
    return this.formControl.value && this.formControl.value.length > 0;
  }
}
