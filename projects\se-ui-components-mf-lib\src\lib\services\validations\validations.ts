import { ValidatorFn } from '@angular/forms';
import * as IdDocumentValidations from './identity-document-validations';
import * as PatternsValidations from './pattern-validations';
import * as StringValidations from './string-validations';
import * as ComparativeValidations from './comparative-validations';
import * as IbanValidations from './iban-validations';
import * as RtcValidations from './rtc-validations';
import * as ListValidations from './list-validation';
import * as DateValidations from './date-validations';
import * as OverrideValidations from './override-validations';
import * as NumberValidations from './number-validations';

export class SeValidations {
  /* Validations: available custom validations */

  // ID Documents
  static dni: ValidatorFn = IdDocumentValidations.validatorDNI;
  static cif: ValidatorFn = IdDocumentValidations.validatorCIF;
  static nie: ValidatorFn = IdDocumentValidations.validatorNIE;
  static dniNie = IdDocumentValidations.validatorDniNie;
  static dniNieCif = IdDocumentValidations.validatorDniNieCif;

  // Patterns
  static phone: ValidatorFn = PatternsValidations.validatorPhone as ValidatorFn;
  static email: ValidatorFn = PatternsValidations.validatorEmail;
  static postalCode: ValidatorFn =
    PatternsValidations.validatorPostalCode as ValidatorFn;
  static numbers: ValidatorFn =
    PatternsValidations.validatorNumbers as ValidatorFn;
  static letters: ValidatorFn =
    PatternsValidations.validatorLetters as ValidatorFn;
  static noWhitespaces: ValidatorFn =
    PatternsValidations.validatorNoWhitespaces as ValidatorFn;
  static registration: ValidatorFn = PatternsValidations.validatorRegistrationPlate as ValidatorFn

  // String validations
  static equals = StringValidations.validatorEquals;
  static notEquals = StringValidations.validatorNotEqualsValue;
  static notEqualsList = StringValidations.validatorNotEqualsList;
  static referenciaCatastral: ValidatorFn =
    StringValidations.validatorReferenciaCatastral;
  static digitLength = StringValidations.validatorDigitLength;
  static digitRange = StringValidations.validatorDigitRange;
  static searchLength = StringValidations.validatorSearchLength;

  // Number validations
  static lessThan = NumberValidations.validatorLessThan;
  static lessOrEqualThan = NumberValidations.validatorLessOrEqualThan;

  static greaterThan = NumberValidations.validatorGreaterThan;

  static greaterOrEqualThan = NumberValidations.validatorGreaterOrEqualThan;


  // Date Validations
  static dateRange = DateValidations.dateRangeValidator;

  // Override Validations
  static required = OverrideValidations.requiredValidator;
  static pattern = OverrideValidations.patternValidator;

  // Comparative validations
  static lessThanOther = ComparativeValidations.validatorLessThanOther;
  static equalsOrLessThanOther = ComparativeValidations.validatorEqualsOrLessThanOther;
  static greaterThanOther = ComparativeValidations.validatorGreaterThanOther;
  static equalsOrGreaterThanOther =
    ComparativeValidations.validatorEqualsOrGreaterThanOther;
  static atLeastOneRequired =
    ComparativeValidations.validatorAtLeastOneRequired;
  static sellers = ComparativeValidations.validatorSellers;
  static differentValues = ComparativeValidations.validatorDifferentValues;

  // IBAN
  static iban: ValidatorFn = IbanValidations.validatorIban;
  static ibanAllowedBanks: ValidatorFn =
    IbanValidations.validatorIbanAllowedBanks;
  static ibanInternalForm = IbanValidations.validatorIbanInternalForm;

  // RTC
  static rtc1: ValidatorFn = RtcValidations.validatorRtc1;
  static rtc2: ValidatorFn = RtcValidations.validatorRtc2;
  static rtc3: ValidatorFn = RtcValidations.validatorRtc3;
  static rtc1Provisional: ValidatorFn = RtcValidations.validatorRtc1Provisional;
  static rtc2Provisional: ValidatorFn = RtcValidations.validatorRtc2Provisional;
  static rtc3Provisional: ValidatorFn = RtcValidations.validatorRtc3Provisional;
  static rtc: ValidatorFn = RtcValidations.validatorRtc;
  static rtcProvisional: ValidatorFn = RtcValidations.validatorRtcProvisional;
  static rtcInternalForm = RtcValidations.validatorRtcInternalForm;

  // Translate error
  static listValidations = ListValidations.listValidations;
}
