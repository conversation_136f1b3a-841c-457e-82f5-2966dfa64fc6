import { ValidatorFn, AbstractControl, ValidationErrors } from '@angular/forms';

export const validatorLessThan = (value: number): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    if (control.value >= value) {
      return {
        lessThan: {
          translation: 'SE_COMPONENTS.VALIDATIONS_ERRORS.lessThan',
          translateParams: { lessThan: value },
        },
      };
    }
    return null;
  };
};

export const validatorLessOrEqualThan = (value: number): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    if (control.value > value) {
      return {
        lessOrEqualThan: {
          translation: 'SE_COMPONENTS.VALIDATIONS_ERRORS.lessOrEqualThan',
          translateParams: { lessOrEqualThan: value },
        },
      };
    }
    return null;
  };
};

export const validatorGreaterThan = (
  value: number,
  translation?: string
): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    if (control.value <= value) {
      return {
        greaterThan: {
          translation:
            translation ?? 'SE_COMPONENTS.VALIDATIONS_ERRORS.greaterThan',
          translateParams: { greaterThan: value },
        },
      };
    }
    return null;
  };
};

export const validatorGreaterOrEqualThan = (value: number): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    if (control.value < value) {
      return {
        greaterOrEqualThan: {
          translation: 'SE_COMPONENTS.VALIDATIONS_ERRORS.greaterOrEqualThan',
          translateParams: { greaterOrEqualThan: value },
        },
      };
    }
    return null;
  };
};
