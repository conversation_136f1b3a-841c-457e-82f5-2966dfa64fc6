{"version": 3, "file": "954.0e2f8804.iframe.bundle.js", "mappings": ";;AAKA;;AAEA;AACA;ACFA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;AAEA;;;;;;AC3SA;;;;;;;AAWA;;;;;;;AAWA", "sources": ["webpack://se-ui-components-mf-lib/./node_modules/@storybook/addon-backgrounds/dist/chunk-GRJZJKJ4.mjs", "webpack://se-ui-components-mf-lib/./node_modules/@storybook/addon-outline/dist/preview.mjs", "webpack://se-ui-components-mf-lib/./node_modules/@storybook/angular/dist/client/angular-beta/utils/PropertyExtractor.js"], "sourcesContent": ["import { global } from '@storybook/global';\nimport { dedent } from 'ts-dedent';\nimport { logger } from '@storybook/client-logger';\n\nvar ADDON_ID=\"storybook/background\",PARAM_KEY=\"backgrounds\";var {document,window}=global,isReduceMotionEnabled=()=>window.matchMedia(\"(prefers-reduced-motion: reduce)\").matches,getBackgroundColorByName=(currentSelectedValue,backgrounds=[],defaultName)=>{if(currentSelectedValue===\"transparent\")return \"transparent\";if(backgrounds.find(background=>background.value===currentSelectedValue))return currentSelectedValue;let defaultBackground=backgrounds.find(background=>background.name===defaultName);if(defaultBackground)return defaultBackground.value;if(defaultName){let availableColors=backgrounds.map(background=>background.name).join(\", \");logger.warn(dedent`\n        Backgrounds Addon: could not find the default color \"${defaultName}\".\n        These are the available colors for your story based on your configuration:\n        ${availableColors}.\n      `);}return \"transparent\"},clearStyles=selector=>{(Array.isArray(selector)?selector:[selector]).forEach(clearStyle);},clearStyle=selector=>{let element=document.getElementById(selector);element&&element.parentElement.removeChild(element);},addGridStyle=(selector,css)=>{let existingStyle=document.getElementById(selector);if(existingStyle)existingStyle.innerHTML!==css&&(existingStyle.innerHTML=css);else {let style=document.createElement(\"style\");style.setAttribute(\"id\",selector),style.innerHTML=css,document.head.appendChild(style);}},addBackgroundStyle=(selector,css,storyId)=>{let existingStyle=document.getElementById(selector);if(existingStyle)existingStyle.innerHTML!==css&&(existingStyle.innerHTML=css);else {let style=document.createElement(\"style\");style.setAttribute(\"id\",selector),style.innerHTML=css;let gridStyleSelector=`addon-backgrounds-grid${storyId?`-docs-${storyId}`:\"\"}`,existingGridStyle=document.getElementById(gridStyleSelector);existingGridStyle?existingGridStyle.parentElement.insertBefore(style,existingGridStyle):document.head.appendChild(style);}};\n\nexport { ADDON_ID, PARAM_KEY, addBackgroundStyle, addGridStyle, clearStyles, getBackgroundColorByName, isReduceMotionEnabled };\n", "import { PARAM_KEY } from './chunk-2DMOCDBJ.mjs';\nimport { useMemo, useEffect } from '@storybook/preview-api';\nimport { global } from '@storybook/global';\nimport { dedent } from 'ts-dedent';\n\nvar clearStyles=selector=>{(Array.isArray(selector)?selector:[selector]).forEach(clearStyle);},clearStyle=input=>{let selector=typeof input==\"string\"?input:input.join(\"\"),element=global.document.getElementById(selector);element&&element.parentElement&&element.parentElement.removeChild(element);},addOutlineStyles=(selector,css)=>{let existingStyle=global.document.getElementById(selector);if(existingStyle)existingStyle.innerHTML!==css&&(existingStyle.innerHTML=css);else {let style=global.document.createElement(\"style\");style.setAttribute(\"id\",selector),style.innerHTML=css,global.document.head.appendChild(style);}};function outlineCSS(selector){return dedent`\n    ${selector} body {\n      outline: 1px solid #2980b9 !important;\n    }\n\n    ${selector} article {\n      outline: 1px solid #3498db !important;\n    }\n\n    ${selector} nav {\n      outline: 1px solid #0088c3 !important;\n    }\n\n    ${selector} aside {\n      outline: 1px solid #33a0ce !important;\n    }\n\n    ${selector} section {\n      outline: 1px solid #66b8da !important;\n    }\n\n    ${selector} header {\n      outline: 1px solid #99cfe7 !important;\n    }\n\n    ${selector} footer {\n      outline: 1px solid #cce7f3 !important;\n    }\n\n    ${selector} h1 {\n      outline: 1px solid #162544 !important;\n    }\n\n    ${selector} h2 {\n      outline: 1px solid #314e6e !important;\n    }\n\n    ${selector} h3 {\n      outline: 1px solid #3e5e85 !important;\n    }\n\n    ${selector} h4 {\n      outline: 1px solid #449baf !important;\n    }\n\n    ${selector} h5 {\n      outline: 1px solid #c7d1cb !important;\n    }\n\n    ${selector} h6 {\n      outline: 1px solid #4371d0 !important;\n    }\n\n    ${selector} main {\n      outline: 1px solid #2f4f90 !important;\n    }\n\n    ${selector} address {\n      outline: 1px solid #1a2c51 !important;\n    }\n\n    ${selector} div {\n      outline: 1px solid #036cdb !important;\n    }\n\n    ${selector} p {\n      outline: 1px solid #ac050b !important;\n    }\n\n    ${selector} hr {\n      outline: 1px solid #ff063f !important;\n    }\n\n    ${selector} pre {\n      outline: 1px solid #850440 !important;\n    }\n\n    ${selector} blockquote {\n      outline: 1px solid #f1b8e7 !important;\n    }\n\n    ${selector} ol {\n      outline: 1px solid #ff050c !important;\n    }\n\n    ${selector} ul {\n      outline: 1px solid #d90416 !important;\n    }\n\n    ${selector} li {\n      outline: 1px solid #d90416 !important;\n    }\n\n    ${selector} dl {\n      outline: 1px solid #fd3427 !important;\n    }\n\n    ${selector} dt {\n      outline: 1px solid #ff0043 !important;\n    }\n\n    ${selector} dd {\n      outline: 1px solid #e80174 !important;\n    }\n\n    ${selector} figure {\n      outline: 1px solid #ff00bb !important;\n    }\n\n    ${selector} figcaption {\n      outline: 1px solid #bf0032 !important;\n    }\n\n    ${selector} table {\n      outline: 1px solid #00cc99 !important;\n    }\n\n    ${selector} caption {\n      outline: 1px solid #37ffc4 !important;\n    }\n\n    ${selector} thead {\n      outline: 1px solid #98daca !important;\n    }\n\n    ${selector} tbody {\n      outline: 1px solid #64a7a0 !important;\n    }\n\n    ${selector} tfoot {\n      outline: 1px solid #22746b !important;\n    }\n\n    ${selector} tr {\n      outline: 1px solid #86c0b2 !important;\n    }\n\n    ${selector} th {\n      outline: 1px solid #a1e7d6 !important;\n    }\n\n    ${selector} td {\n      outline: 1px solid #3f5a54 !important;\n    }\n\n    ${selector} col {\n      outline: 1px solid #6c9a8f !important;\n    }\n\n    ${selector} colgroup {\n      outline: 1px solid #6c9a9d !important;\n    }\n\n    ${selector} button {\n      outline: 1px solid #da8301 !important;\n    }\n\n    ${selector} datalist {\n      outline: 1px solid #c06000 !important;\n    }\n\n    ${selector} fieldset {\n      outline: 1px solid #d95100 !important;\n    }\n\n    ${selector} form {\n      outline: 1px solid #d23600 !important;\n    }\n\n    ${selector} input {\n      outline: 1px solid #fca600 !important;\n    }\n\n    ${selector} keygen {\n      outline: 1px solid #b31e00 !important;\n    }\n\n    ${selector} label {\n      outline: 1px solid #ee8900 !important;\n    }\n\n    ${selector} legend {\n      outline: 1px solid #de6d00 !important;\n    }\n\n    ${selector} meter {\n      outline: 1px solid #e8630c !important;\n    }\n\n    ${selector} optgroup {\n      outline: 1px solid #b33600 !important;\n    }\n\n    ${selector} option {\n      outline: 1px solid #ff8a00 !important;\n    }\n\n    ${selector} output {\n      outline: 1px solid #ff9619 !important;\n    }\n\n    ${selector} progress {\n      outline: 1px solid #e57c00 !important;\n    }\n\n    ${selector} select {\n      outline: 1px solid #e26e0f !important;\n    }\n\n    ${selector} textarea {\n      outline: 1px solid #cc5400 !important;\n    }\n\n    ${selector} details {\n      outline: 1px solid #33848f !important;\n    }\n\n    ${selector} summary {\n      outline: 1px solid #60a1a6 !important;\n    }\n\n    ${selector} command {\n      outline: 1px solid #438da1 !important;\n    }\n\n    ${selector} menu {\n      outline: 1px solid #449da6 !important;\n    }\n\n    ${selector} del {\n      outline: 1px solid #bf0000 !important;\n    }\n\n    ${selector} ins {\n      outline: 1px solid #400000 !important;\n    }\n\n    ${selector} img {\n      outline: 1px solid #22746b !important;\n    }\n\n    ${selector} iframe {\n      outline: 1px solid #64a7a0 !important;\n    }\n\n    ${selector} embed {\n      outline: 1px solid #98daca !important;\n    }\n\n    ${selector} object {\n      outline: 1px solid #00cc99 !important;\n    }\n\n    ${selector} param {\n      outline: 1px solid #37ffc4 !important;\n    }\n\n    ${selector} video {\n      outline: 1px solid #6ee866 !important;\n    }\n\n    ${selector} audio {\n      outline: 1px solid #027353 !important;\n    }\n\n    ${selector} source {\n      outline: 1px solid #012426 !important;\n    }\n\n    ${selector} canvas {\n      outline: 1px solid #a2f570 !important;\n    }\n\n    ${selector} track {\n      outline: 1px solid #59a600 !important;\n    }\n\n    ${selector} map {\n      outline: 1px solid #7be500 !important;\n    }\n\n    ${selector} area {\n      outline: 1px solid #305900 !important;\n    }\n\n    ${selector} a {\n      outline: 1px solid #ff62ab !important;\n    }\n\n    ${selector} em {\n      outline: 1px solid #800b41 !important;\n    }\n\n    ${selector} strong {\n      outline: 1px solid #ff1583 !important;\n    }\n\n    ${selector} i {\n      outline: 1px solid #803156 !important;\n    }\n\n    ${selector} b {\n      outline: 1px solid #cc1169 !important;\n    }\n\n    ${selector} u {\n      outline: 1px solid #ff0430 !important;\n    }\n\n    ${selector} s {\n      outline: 1px solid #f805e3 !important;\n    }\n\n    ${selector} small {\n      outline: 1px solid #d107b2 !important;\n    }\n\n    ${selector} abbr {\n      outline: 1px solid #4a0263 !important;\n    }\n\n    ${selector} q {\n      outline: 1px solid #240018 !important;\n    }\n\n    ${selector} cite {\n      outline: 1px solid #64003c !important;\n    }\n\n    ${selector} dfn {\n      outline: 1px solid #b4005a !important;\n    }\n\n    ${selector} sub {\n      outline: 1px solid #dba0c8 !important;\n    }\n\n    ${selector} sup {\n      outline: 1px solid #cc0256 !important;\n    }\n\n    ${selector} time {\n      outline: 1px solid #d6606d !important;\n    }\n\n    ${selector} code {\n      outline: 1px solid #e04251 !important;\n    }\n\n    ${selector} kbd {\n      outline: 1px solid #5e001f !important;\n    }\n\n    ${selector} samp {\n      outline: 1px solid #9c0033 !important;\n    }\n\n    ${selector} var {\n      outline: 1px solid #d90047 !important;\n    }\n\n    ${selector} mark {\n      outline: 1px solid #ff0053 !important;\n    }\n\n    ${selector} bdi {\n      outline: 1px solid #bf3668 !important;\n    }\n\n    ${selector} bdo {\n      outline: 1px solid #6f1400 !important;\n    }\n\n    ${selector} ruby {\n      outline: 1px solid #ff7b93 !important;\n    }\n\n    ${selector} rt {\n      outline: 1px solid #ff2f54 !important;\n    }\n\n    ${selector} rp {\n      outline: 1px solid #803e49 !important;\n    }\n\n    ${selector} span {\n      outline: 1px solid #cc2643 !important;\n    }\n\n    ${selector} br {\n      outline: 1px solid #db687d !important;\n    }\n\n    ${selector} wbr {\n      outline: 1px solid #db175b !important;\n    }`}var withOutline=(StoryFn,context)=>{let{globals:globals2}=context,isActive=[!0,\"true\"].includes(globals2[PARAM_KEY]),isInDocs=context.viewMode===\"docs\",outlineStyles=useMemo(()=>outlineCSS(isInDocs?'[data-story-block=\"true\"]':\".sb-show-main\"),[context]);return useEffect(()=>{let selectorId=isInDocs?`addon-outline-docs-${context.id}`:\"addon-outline\";return isActive?addOutlineStyles(selectorId,outlineStyles):clearStyles(selectorId),()=>{clearStyles(selectorId);}},[isActive,outlineStyles,context]),StoryFn()};var decorators=[withOutline],globals={[PARAM_KEY]:!1};\n\nexport { decorators, globals };\n", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.PropertyExtractor = exports.uniqueArray = exports.REMOVED_MODULES = exports.reflectionCapabilities = void 0;\n/* eslint-disable no-console */\nconst common_1 = require(\"@angular/common\");\nconst core_1 = require(\"@angular/core\");\nconst platform_browser_1 = require(\"@angular/platform-browser\");\nconst animations_1 = require(\"@angular/platform-browser/animations\");\nconst ts_dedent_1 = __importDefault(require(\"ts-dedent\"));\nconst NgModulesAnalyzer_1 = require(\"./NgModulesAnalyzer\");\nexports.reflectionCapabilities = new core_1.ɵReflectionCapabilities();\nexports.REMOVED_MODULES = new core_1.InjectionToken('REMOVED_MODULES');\nconst uniqueArray = (arr) => {\n    return arr\n        .flat(Number.MAX_VALUE)\n        .filter(Boolean)\n        .filter((value, index, self) => self.indexOf(value) === index);\n};\nexports.uniqueArray = uniqueArray;\nclass PropertyExtractor {\n    /* eslint-enable @typescript-eslint/lines-between-class-members */\n    constructor(metadata, component) {\n        this.metadata = metadata;\n        this.component = component;\n        /* eslint-disable @typescript-eslint/lines-between-class-members */\n        this.declarations = [];\n        /**\n         * Analyze NgModule Metadata\n         *\n         * - Removes Restricted Imports\n         * - Extracts providers from ModuleWithProviders\n         * - Returns a new NgModuleMetadata object\n         *\n         *\n         */\n        this.analyzeMetadata = (metadata) => {\n            const declarations = [...(metadata?.declarations || [])];\n            const providers = [...(metadata?.providers || [])];\n            const applicationProviders = [];\n            const imports = [...(metadata?.imports || [])].reduce((acc, imported) => {\n                // remove ngModule and use only its providers if it is restricted\n                // (e.g. BrowserModule, BrowserAnimationsModule, NoopAnimationsModule, ...etc)\n                const [isRestricted, restrictedProviders] = PropertyExtractor.analyzeRestricted(imported);\n                if (isRestricted) {\n                    applicationProviders.unshift(restrictedProviders || []);\n                    return acc;\n                }\n                acc.push(imported);\n                return acc;\n            }, []);\n            return { ...metadata, imports, providers, applicationProviders, declarations };\n        };\n        this.init();\n    }\n    // With the new way of mounting standalone components to the DOM via bootstrapApplication API,\n    // we should now pass ModuleWithProviders to the providers array of the bootstrapApplication function.\n    static warnImportsModuleWithProviders(propertyExtractor) {\n        const hasModuleWithProvidersImport = propertyExtractor.imports.some((importedModule) => 'ngModule' in importedModule);\n        if (hasModuleWithProvidersImport) {\n            console.warn((0, ts_dedent_1.default)(`\n          Storybook Warning: \n          moduleMetadata property 'imports' contains one or more ModuleWithProviders, likely the result of a 'Module.forRoot()'-style call.\n          In Storybook 7.0 we use Angular's new 'bootstrapApplication' API to mount the component to the DOM, which accepts a list of providers to set up application-wide providers.\n          Use the 'applicationConfig' decorator from '@storybook/angular' to pass your ModuleWithProviders to the 'providers' property in combination with the importProvidersFrom helper function from '@angular/core' to extract all the necessary providers.\n          Visit https://angular.io/guide/standalone-components#configuring-dependency-injection for more information\n          `));\n        }\n    }\n    init() {\n        const analyzed = this.analyzeMetadata(this.metadata);\n        this.imports = (0, exports.uniqueArray)([common_1.CommonModule, analyzed.imports]);\n        this.providers = (0, exports.uniqueArray)(analyzed.providers);\n        this.applicationProviders = (0, exports.uniqueArray)(analyzed.applicationProviders);\n        this.declarations = (0, exports.uniqueArray)(analyzed.declarations);\n        if (this.component) {\n            const { isDeclarable, isStandalone } = PropertyExtractor.analyzeDecorators(this.component);\n            const isDeclared = (0, NgModulesAnalyzer_1.isComponentAlreadyDeclared)(this.component, analyzed.declarations, this.imports);\n            if (isStandalone) {\n                this.imports.push(this.component);\n            }\n            else if (isDeclarable && !isDeclared) {\n                this.declarations.push(this.component);\n            }\n        }\n    }\n}\nexports.PropertyExtractor = PropertyExtractor;\n_a = PropertyExtractor;\nPropertyExtractor.analyzeRestricted = (ngModule) => {\n    if (ngModule === platform_browser_1.BrowserModule) {\n        console.warn((0, ts_dedent_1.default) `\n          Storybook Warning:\n          You have imported the \"BrowserModule\", which is not necessary anymore. \n          In Storybook v7.0 we are using Angular's new bootstrapApplication API to mount an Angular application to the DOM.\n          Note that the BrowserModule providers are automatically included when starting an application with bootstrapApplication()\n          Please remove the \"BrowserModule\" from the list of imports in your moduleMetadata definition to remove this warning.\n        `);\n        return [true];\n    }\n    if (ngModule === animations_1.BrowserAnimationsModule) {\n        console.warn((0, ts_dedent_1.default) `\n          Storybook Warning:\n          You have added the \"BrowserAnimationsModule\" to the list of \"imports\" in your moduleMetadata definition of your Story.\n          In Storybook 7.0 we use Angular's new 'bootstrapApplication' API to mount the component to the DOM, which accepts a list of providers to set up application-wide providers.\n          Use the 'applicationConfig' decorator from '@storybook/angular' and add the \"provideAnimations\" function to the list of \"providers\".\n          If your Angular version does not support \"provide-like\" functions, use the helper function importProvidersFrom instead to set up animations. For this case, please add \"importProvidersFrom(BrowserAnimationsModule)\" to the list of providers of your applicationConfig definition.\n          Please visit https://angular.io/guide/standalone-components#configuring-dependency-injection for more information.\n        `);\n        return [true, (0, animations_1.provideAnimations)()];\n    }\n    if (ngModule === animations_1.NoopAnimationsModule) {\n        console.warn((0, ts_dedent_1.default) `\n          Storybook Warning:\n          You have added the \"NoopAnimationsModule\" to the list of \"imports\" in your moduleMetadata definition of your Story.\n          In Storybook v7.0 we are using Angular's new bootstrapApplication API to mount an Angular application to the DOM, which accepts a list of providers to set up application-wide providers.\n          Use the 'applicationConfig' decorator from '@storybook/angular' and add the \"provideNoopAnimations\" function to the list of \"providers\".\n          If your Angular version does not support \"provide-like\" functions, use the helper function importProvidersFrom instead to set up noop animations and to extract all necessary providers from NoopAnimationsModule. For this case, please add \"importProvidersFrom(NoopAnimationsModule)\" to the list of providers of your applicationConfig definition.\n          Please visit https://angular.io/guide/standalone-components#configuring-dependency-injection for more information.\n        `);\n        return [true, (0, animations_1.provideNoopAnimations)()];\n    }\n    return [false];\n};\nPropertyExtractor.analyzeDecorators = (component) => {\n    const decorators = exports.reflectionCapabilities.annotations(component);\n    const isComponent = decorators.some((d) => _a.isDecoratorInstanceOf(d, 'Component'));\n    const isDirective = decorators.some((d) => _a.isDecoratorInstanceOf(d, 'Directive'));\n    const isPipe = decorators.some((d) => _a.isDecoratorInstanceOf(d, 'Pipe'));\n    const isDeclarable = isComponent || isDirective || isPipe;\n    const isStandalone = isComponent && decorators.some((d) => d.standalone);\n    return { isDeclarable, isStandalone };\n};\nPropertyExtractor.isDecoratorInstanceOf = (decorator, name) => {\n    let factory;\n    switch (name) {\n        case 'Component':\n            factory = core_1.Component;\n            break;\n        case 'Directive':\n            factory = core_1.Directive;\n            break;\n        case 'Pipe':\n            factory = core_1.Pipe;\n            break;\n        case 'Injectable':\n            factory = core_1.Injectable;\n            break;\n        case 'Input':\n            factory = core_1.Input;\n            break;\n        case 'Output':\n            factory = core_1.Output;\n            break;\n        default:\n            throw new Error(`Unknown decorator type: ${name}`);\n    }\n    return decorator instanceof factory || decorator.ngMetadataName === name;\n};\n"], "names": [], "sourceRoot": ""}