import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  Renderer2,
  SimpleChanges,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { PaginatorState } from 'primeng/paginator';
import { Nullable } from '../../models';
import { SeButton } from '../button';
import { SeDropdownOption } from '../dropdown/dropdown.model';
import { SeDeviceService } from '../../services';
import { Observable } from 'rxjs';

/**
 * Represents a pagination component.
 *
 * @remarks
 * This component is responsible for displaying pagination controls and managing pagination state.
 *
 * @example
 * ```html
 * <se-pagination
 *   [first]="0"
 *   [rows]="10"
 *   [totalRecords]="100"
 *   innerText="Pagination"
 *   [prevButtonLabel]="'Previous'"
 *   [selectedText]="'Nre. d'autoliquidacions seleccionades: 2 de 16'"
 *   [nextButtonLabel]="'Next'"
 *   [rowsPerPageOptions]="[10, 25, 50]"
 *   [showRowsPerPage]="true"
 *   [showNumberReportPaginator]="true"
 *   (onPageChangeEvent)="handlePageChange($event)"
 *   (rowsPerPageChangeEvent)="handleRowsPerPageChange($event)"
 * ></se-pagination>
 * ```
 */
@Component({
  selector: 'se-pagination',
  styleUrls: ['./pagination.component.scss'],
  templateUrl: './pagination.component.html',
})
export class PaginationComponent implements OnInit, OnChanges {
  @Input() first: number = 0;
  @Input() rows: number = 0;
  @Input() totalRecords: number = 0;
  @Input() innerText: string = '';
  @Input() selectedText: string = "";
  @Input() prevButtonLabel?: string = '';
  @Input() nextButtonLabel?: string = '';
  @Input() downloadButton: Nullable<SeButton>;
  /**
   * Sets the options for the number of rows per page in the pagination component.
   *
   * @param value - An array of numbers representing the available options.
   * @remarks
   * This method updates the `rowsPerPage` property of the pagination component with the provided options.
   * It also triggers the `buildForm` method to rebuild the form with the updated options.
   */
  @Input() set rowsPerPageOptions(value: number[] | undefined) {
    if (value) {
      this.rowsPerPage = value.map((item) => ({
        label: item.toString(),
        id: item,
      }));
      this.buildForm();
    }
  }
  @Input() showRowsPerPage: boolean = false;
  @Input() showNumberReportPaginator: boolean = true;
  @Output() onPageChangeEvent: EventEmitter<PaginatorState> =
    new EventEmitter<PaginatorState>();

  @Output() downloadClick: EventEmitter<void> = new EventEmitter<void>();

  currentPage: number = 0;
  totalPages: number = 0;
  rowsPerPage: SeDropdownOption[] = [
    { label: '10', id: 10 },
    { label: '25', id: 25 },
    { label: '50', id: 50 },
  ];
  form!: FormGroup;

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private formBuilder: FormBuilder,
    private deviceService: SeDeviceService
  ) {}

  ngOnInit() {
    this.buildForm();
    this.updateCurrentPage();
    this.updateTotalPages();
  }

  get isMobile$(): Observable<boolean> {
    return this.deviceService.isMobile$;
  }

  ngOnChanges(changes: SimpleChanges): void {
    const firstChanged = this.propertyHasChanged(changes, 'first');
    const rowChanged = this.propertyHasChanged(changes, 'rows');
    const totalRecordChanged = this.propertyHasChanged(changes, 'totalRecords');

    if (firstChanged || rowChanged) {
      this.updateCurrentPage();
    }
    if (rowChanged || totalRecordChanged) {
      this.updateTotalPages();
    }
  }

  ngAfterViewInit() {
    if (!this.showNumberReportPaginator) {
      const prevButtonElement = this.el.nativeElement.querySelector(
        '.paginator-container .buttons-paginator .p-paginator .p-paginator-prev'
      );
      const nextButtonElement = this.el.nativeElement.querySelector(
        '.paginator-container .buttons-paginator .p-paginator .p-paginator-next'
      );
      if (prevButtonElement && nextButtonElement) {
        this.renderer.setProperty(
          prevButtonElement,
          'textContent',
          this.prevButtonLabel || ''
        );
        this.renderer.setProperty(
          nextButtonElement,
          'textContent',
          this.nextButtonLabel || ''
        );
      }
    }
  }

  /**
   * Handles the page change event of the pagination component.
   *
   * @param event - The PaginatorState object containing the new page information.
   * @returns void
   */
  handlePageChange(event: PaginatorState) {
    const newPage = event.page ?? 0;

    if (this.currentPage !== newPage + 1) {
      console.log('handlePageChange', event);
      this.first = event.first ?? 0;
      this.rows = event.rows ?? 0;
      this.currentPage = newPage + 1;
      this.onPageChangeEvent.emit(event);
    }
  }

  /**
   * Builds the form for the pagination component.
   */
  buildForm(): void {
    this.form = this.formBuilder.group({
      rowsPerPageDropdown: [this.rowsPerPage[0]?.id],
    });
  }

  /**
   * Emits the selected number of rows per page and triggers the rows per page change event.
   */
  updateRowsPerPage(): void {
    this.rows = this.form.get('rowsPerPageDropdown')?.value;
    this.totalPages = Math.ceil(this.totalRecords / this.rows);
    this.currentPage = 1;
    this.first = 0;

    const paginatorState: PaginatorState = {
      page: 0,
      first: 0,
      rows: this.rows,
      pageCount: this.totalPages,
    };
    this.onPageChangeEvent.emit(paginatorState);
  }

  private propertyHasChanged(changes: SimpleChanges, key: string): boolean {
    const change = changes[key];
    return change && change.currentValue !== change.previousValue;
  }

  private updateCurrentPage(): void {
    this.currentPage = Math.floor(this.first / this.rows) + 1;
  }

  private updateTotalPages(): void {
    this.totalPages = Math.ceil(this.totalRecords / this.rows);
  }
}
