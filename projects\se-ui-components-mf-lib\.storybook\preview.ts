import { Preview, applicationConfig } from '@storybook/angular';
import { StorybookTranslateModule } from './storybook-translation.module';
import { importProvidersFrom } from '@angular/core';
// import 'bootstrap/dist/css/bootstrap.min.css';
import config from '../src/config/feature-flags.json';

// @ts-ignore
// Load styles to make them global in storybook
// Info: https://github.com/storybookjs/storybook/issues/16950
import GlobalStyles from '!style-loader!css-loader!sass-loader!../src/lib/styles.scss';
const storybookStyles = document.createElement('style');
storybookStyles.innerHTML = GlobalStyles;
document.body.appendChild(storybookStyles);

// Providers and Modules
// https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#angular-application-providers-and-modulewithproviders
const preview: Preview = {
  decorators: [
    applicationConfig({
      providers: config.ENABLE_TRANSLATIONS
        ? [importProvidersFrom(StorybookTranslateModule)]
        : [],
    }),
  ],
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      expanded: true,
      hideNoControlsWarning: true,
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
  },
};

export default preview;
