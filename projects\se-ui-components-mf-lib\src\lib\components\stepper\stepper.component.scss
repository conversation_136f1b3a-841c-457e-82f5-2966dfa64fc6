:host ::ng-deep p-progressbar {
  .p-progressbar.p-component.p-progressbar-determinate {
    height: 6px;
    border-radius: 0px;
  }

  .p-progressbar-label {
    display: none !important;
  }
}

:host ::ng-deep p-card.se-stepper__mobile__card {

  .p-card {
    border-top-left-radius: 0;
    border-top-right-radius: 0;

    .p-card-body {
      padding: 0 16px;

      .p-card-content {
        padding: 0;
      }
    }
  }
}

.se-stepper {
  font-family: var(--font-primary);
  display: flex;
  align-items: stretch;
  width: 100%;
  flex-direction: row;

  &__mobile {
    display: none;
  }

  &__progress {
    width: 0;
  }

  &__desktop {
    display: flex;
    width: 100%;
  }

  &__mobile {
    display: none;

    &__card {
      display: none;
    }
  }

  .se-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    // width: 100%;
    overflow: hidden;
    // flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;

    &:hover {
      background-color: var(--color-blue-200);
    }

    &__container {
      display: flex;
      align-items: center;
      gap: 5px;
      width: 100%;
      cursor: default;
      padding: 8px 5px 0;
      height: 39px;

      .past-icon {
        font-size: 16px;
        height: 16px;
        width: 16px;
        color: var(--color-blue-500);
        margin-bottom: 8px;
      }
    }

    &__pointer {
      cursor: pointer;
    }

    &__circle {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      box-sizing: border-box;
      margin-bottom: 10px;
      flex-shrink: 0;

      &-off {
        border: 2px solid var(--color-gray-600);
        background-color: var(--color-white);
      }

      &-on {
        border: 4px solid var(--color-primary-link);
        background-color: var(--color-white);
      }
    }

    &__label {
      margin-bottom: 10px;
      text-wrap: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &-past {
        color: var(--color-blue-600);
        font-weight: var(--font-semibold);
      }

      &-off {
        color: var(--color-gray-600);
      }

      &-on {
        color: var(--color-gray-700);
        font-weight: var(--font-semibold);
      }
    }

    &__line {
      width: 100%;
      flex-grow: 1;
      height: 5px;

      &-past {
        border-bottom: 2px solid var(--color-blue-500);
      }

      &-off {
        border-bottom: 2px solid var(--color-gray-400);
      }

      &-on {
        border-bottom: 5px solid var(--color-primary-link);
      }
    }
  }
}

// RESPONSIVE
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

/* MOBILE */
// @media (max-width: 768px) {
@include media-breakpoint-down(md) {
  .se-stepper {
    flex-direction: column;
    position: relative;

    &__mobile {
      display: flex;
      font-size: 22px;
      flex-direction: row;
      gap: 8px;
      &-on {
        color: var(--color-primary-link);
      }
      &__label {
        display: flex;
        flex-direction: column;
        width: 80%;

        &-next {
          font-size: 16px;
          color: var(--color-gray-600);
        }
      }

      &__card {
        z-index: 1000;
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 100%;
        width: 100%;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        .se-step {
        
          &:not(:last-child) {
            border-bottom: 1px solid var(--color-gray-400);
          }

          &__line {
            display: none;
          }
        }
      }

      &__button {
        color: var(--color-blue-500);
        align-self: center;
        background: none;
        border: none;

        ._icon {
          --ng-icon__size: 1.5em !important;
        }
      }
    }

    &__progress {
      margin-top: 8px;
      width: auto;
      height: 6px;
    }

    &__desktop {
      display: none;
    }
  }
}
