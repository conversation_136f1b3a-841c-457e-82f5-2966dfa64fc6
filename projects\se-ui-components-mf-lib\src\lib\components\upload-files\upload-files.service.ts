import { Inject, Injectable, LOCALE_ID } from '@angular/core';
import { Row } from '../table/table.model';
import { AttachFile } from './modal-table/attach-modal.model';
import { EMPTY_SIZE, FileFormatsSeparation, ROW_CONFIG } from './upload-files.model';
import { SeDocumentsService } from '../../services';

@Injectable({
  providedIn: 'root',
})
export class UploadFilesService {
  constructor(
    private seDocumentsService: SeDocumentsService) {
    // Intencionadamente vacío
  }

  addTableRows(list: AttachFile[], useNameAsDescription?: boolean): Row[] {
    return list.map((file) => {
      return {
        data: {
          name: { value: file.name },
          size: { value: file[EMPTY_SIZE] ? '' : this.seDocumentsService.convertSize(this.getFileSizeInKb(file.size)) },
          description: { value: useNameAsDescription ? file.name : file['description'] },
          docType: { value: file['docType'] },
          id: { value: file?.id },
          file: { value: file },
        },
        rowConfig: file[ROW_CONFIG] ?? {}
      };
    });
  }

  setFilesDataFromRows(list: AttachFile[], rows: Row[]): AttachFile[] {
    return list.map((element: AttachFile) => {
      const { data } = rows.find((row: Row) => {
        return element.name === row?.data!['name']?.value;
      }) as Row;
      element['description'] = data['description']?.value;
      element['docType'] = data['docType']?.value;
      return element;
    })
  }

  getFileSizeInKb(size: number): number {
    return Number((size / 1024).toFixed(2));
  }

  getTotalFileSize(files: AttachFile[]): number {
    return [...files].reduce(this.getFileSize, 0);
  }

  private getFileSize = (sum: number, obj: AttachFile): number => {
    return sum + obj.size;
  };

  setFileFormat(value: string[], formatType: FileFormatsSeparation): string {
    if (formatType === FileFormatsSeparation.COMMA) {
      return this.seDocumentsService.setCommaFileFormat(value);
    } else if (formatType === FileFormatsSeparation.SLASH) {
      return this.seDocumentsService.setSlashFileFormat(value);
    }
    return '';
  }
}
