.se-side-tabs {
  margin: 0;

  &--nav {
    display: flex;
    flex-direction: column;
  }

  &--button {
    display: flex;
    text-align: left;
    padding-right: 30px;
    padding-left: 0;
    color: var(--color-gray-600);
    background-color: transparent;
    border: none;
    text-align: left;
    padding: 1rem 0 0.25rem 0;
    margin-bottom: 1rem;

    &.active {
      font-weight: bold;
      border-bottom: 2px solid var(--color-pink-600);
      color: var(--color-blue-600);
      font-weight: bold;
    }
  }

  &--content {
    padding: 0;
  }

  &--title {
    text-align: left !important;
    font-size: var(--text-2xl);
    color: var(--color-gray-700);
  }

  &--subtitle {
    font-family: var(--font-primary) !important;
    letter-spacing: 0;
    line-height: 26px;
    font-size: var(--text-lg);
    color: var(--color-gray-700);
  }

  .scroll-button {
    display: none;
  }
}

// RESPONSIVE
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

// @media (max-width: 767px) {
@include media-breakpoint-down(md) {
  .se-side-tabs {
    &--container {
      padding: 0;
      margin-bottom: 15px;
      overflow: hidden;
      position: relative;
    }

    &--nav {
      padding: 0;
      margin: 0;
      flex-direction: row;
      align-items: end;
      overflow-x: auto;
      overflow-y: hidden;
      scroll-behavior: smooth;
      scrollbar-width: none;
      overscroll-behavior: contain auto;
      -webkit-overflow-scrolling: touch;

      &::-webkit-scrollbar {
        height: 0;
      }
    }

    &--button {
      font-family: var(--font-primary);
      font-weight: var(--font-weight-regular);
      padding: 8px 32px 11px;
      margin: 0;
      cursor: pointer;
      box-sizing: border-box;
      outline: none;
      height: 100%;
      white-space: nowrap;
      align-content: space-evenly;

      &.active {
        box-shadow: inset 0 -5px 0 var(--color-pink-600);
        font-weight: var(--font-semibold);
        color: var(--color-gray-700);
      }

      &:not(.active) {
        color: var(--color-gray-400);
        background-color: var(--color-gray-200);
        box-shadow: inset 0 -2px 0 var(--color-gray-400);
      }
    }

    .scroll-button {
      position: absolute;
      top: 0;
      z-index: 3;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      &--prev {
        left: 0;
      }
      &--next {
        right: 0;
      }

      ::ng-deep button {
        width: 1.8rem;
        height: 100%;
        border: 0;
        padding: 8px 4px;
        margin: 0;
        box-shadow: 0px 0px 10px 20px rgba(255, 255, 255, 0.6);
        transition: box-shadow 0.2s, outline-color 0.2s;
        border-radius: 0;
        background: white;
        color: var(--color-primary-action);

        &:focus,
        &:active,
        &:hover {
          box-shadow: 0px 0px 10px 20px rgba(255, 255, 255, 0.6);
          border: none;
          outline: none;
        }

        ng-icon{
          font-size: 2.5rem;
        }
      }
    }
  }
}
