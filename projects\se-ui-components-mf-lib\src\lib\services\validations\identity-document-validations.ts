import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

/**
 * Validator: DNI
 * @description Validates if a field value has a valid DNI format
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorDNI: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  let numero;
  let letr: string;
  let letra: string;
  const dniRegExp: RegExp = /^\d{8}[a-zA-Z]$/;

  if (c.value && c.value.toString().trim() !== '') {
    if (dniRegExp.test(c.value) === true) {
      numero = c.value.substr(0, c.value.length - 1);
      letr = c.value.substr(c.value.length - 1, 1);
      numero = numero % 23;
      letra = 'TRWAGMYFPDXBNJZSQVHLCKET';
      letra = letra.substring(numero, numero + 1);
      if (letra !== letr.toUpperCase()) {
        return { dniLetter: 'UI_COMPONENTS.VALIDATIONS_ERRORS.dniLetter' };
      }
    } else {
      return { dniPattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.dniPattern' };
    }
  }

  return null;
};

/**
 * Validator: CIF (persona jurídica)
 * @description Validates if a field value has a valid CIF format
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorCIF: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  if (c.value && c.value.toString().trim() !== '') {
    if (c.value.length !== 9) {
      return { cifPattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.cifPattern' };
    }

    var letters = ['J', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'];
    var initialletters = [
      'A',
      'B',
      'C',
      'D',
      'E',
      'F',
      'G',
      'H',
      'J',
      'N',
      'P',
      'Q',
      'R',
      'S',
      'U',
      'V',
      'W',
    ];
    var digits = c.value.substr(1, c.value.length - 2);
    var letter = c.value.substr(0, 1);
    var control = c.value.substr(c.value.length - 1);
    var sum = 0;
    var i;
    var digit;

    if (!initialletters.includes(letter)) {
      return { cifPattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.cifPattern' };
    }

    for (i = 0; i < digits.length; ++i) {
      digit = parseInt(digits[i]);

      if (isNaN(digit)) {
        return { cifPattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.cifPattern' };
      }

      if (i % 2 === 0) {
        digit *= 2;
        if (digit > 9) {
          digit = parseInt((digit / 10).toString()) + (digit % 10);
        }

        sum += digit;
      } else {
        sum += digit;
      }
    }

    sum %= 10;
    if (sum !== 0) {
      digit = 10 - sum;
    } else {
      digit = sum;
    }

    if (letter.match(/[ABEH]/)) {
      if (String(digit) === control) {
        return null;
      } else {
        return { cifPattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.cifPattern' };
      }
    }
    if (letter.match(/[NPQRSW]/)) {
      if (letters[digit] === control) {
        return null;
      } else {
        return { cifPattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.cifPattern' };
      }
    }

    if (String(digit) === control || letters[digit] === control) {
      return null;
    } else {
      return { cifPattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.cifPattern' };
    }
  }

  return null;
};

/**
 * Validator: NIE (Número d'identificació estrangers)
 * @description Validates if a field value has a valid NIE format
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorNIE: ValidatorFn = (
  c: AbstractControl
): ValidationErrors | null => {
  // const firstLetters = 'XYZ';
  const availableLetters = { X: 0, Y: 1, Z: 2, K: 0, L: 0, M: 0 };
  const controlDigitLetters = 'TRWAGMYFPDXBNJZSQVHLCKET';
  const regexNIE = /^[xXyYzZkKlLmM]\d{7}[a-zA-Z]$/;
  const regexFirstLetter = /^[a-wA-W]\d{7}[a-zA-Z]$/;

  if (c.value && c.value.toString().trim() !== '') {
    if (regexNIE.test(c.value) === true) {
      let calculatedNumber: number;
      let calculatedLetter: string;
      let firstLetter: string;
      let lastLetter: string;
      let firstNumber: number;

      firstLetter = c.value.substr(0, 1).toUpperCase();
      lastLetter = c.value.substr(c.value.length - 1, 1);
      // firstNumber = firstLetters.indexOf(firstLetter);
      firstNumber =
        availableLetters[firstLetter as keyof typeof availableLetters];

      calculatedNumber =
        firstNumber * 10000000 +
        parseInt(c.value.substr(1, c.value.length - 1));
      calculatedNumber = calculatedNumber % 23;

      calculatedLetter = controlDigitLetters.substring(
        calculatedNumber,
        calculatedNumber + 1
      );
      if (calculatedLetter !== lastLetter.toUpperCase()) {
        return {
          validatorNIE: {
            expected: 'La lletra final informada no correspon.',
            actual: c.value,
          },
        };
      }
    } else if (regexFirstLetter.test(c.value) === true) {
      //Check the first letter (must be X, Y or Z)
      return { nieLetter: 'UI_COMPONENTS.VALIDATIONS_ERRORS.nieLetter' };
    } else {
      return { niePattern: 'UI_COMPONENTS.VALIDATIONS_ERRORS.niePattern' };
    }
  }

  return null;
};

/**
 * Validator: Mix validator (DNI | NIE)
 * @description Validates if a field value has a valid DNI or NIE format
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorDniNie = (translateKey?: string): ValidatorFn => {
  return (c: AbstractControl): ValidationErrors | null => {
    if (
      c.value &&
      c.value.toString().trim() !== '' &&
      validatorDNI(c) &&
      validatorNIE(c)
    ) {
      return {
        dniNieCif: {
          translation:
            translateKey ?? 'UI_COMPONENTS.VALIDATIONS_ERRORS.pattern',
        },
      };
    }

    return null;
  };
};

/**
 * Validator: Mix validator (DNI | NIE | CIF)
 * @description Validates if a field value has a valid DNI or NIE format
 * @returns {ValidatorFn} Return a validaton error
 */
export const validatorDniNieCif = (translateKey?: string): ValidatorFn => {
  return (c: AbstractControl): ValidationErrors | null => {
    if (
      c.value &&
      c.value.toString().trim() !== '' &&
      validatorDNI(c) &&
      validatorNIE(c) &&
      validatorCIF(c)
    ) {
      return {
        dniNieCif: {
          translation:
            translateKey ?? 'UI_COMPONENTS.VALIDATIONS_ERRORS.pattern',
        },
      };
    }

    return null;
  };
};
