<div class="se-dropdown-filter" [id]="id">
  <div
    class="dropdown-header"
    tabindex="0"
    (click)="toggleDropdown()"
    (keydown)="handleKeyboardEvent($event)"
  >
    <div
      class="dropdown-title text-sm"
      [ngClass]="{ 
        'dropdown-title-selected': (from || to) && !showClear,
        'dropdown-title-selected__clear-icon': (from || to) && showClear,
      }"
    >
      {{ placeholder | translate }}
      <div
        class="selected-count-badge"
        *ngIf="rangeForm.touched && !!badgeText"
      >
        <se-badge
          [badgeTheme]="'info'"
          [textInsideCircle]="badgeText"
        ></se-badge>
      </div>
    </div>
    <TimesIcon
    *ngIf="showClear && rangeForm.touched && !!badgeText"
      id="clear-icon"
      tabindex="0"
      (click)="resetForm()"
      />
    <ng-icon #ngIcon id="drop-icon" [name]="getIcon()"></ng-icon>
  </div>
  <div
    class="dropdown-list"
    *ngIf="dropdownOpen"
    [ngClass]="{ open: dropdownOpen }"
  >
    <div [ngStyle]="{ 'max-height': scrollHeight }">
      <ng-container [formGroup]="rangeForm">
        <div *ngIf="dropdownOpen" class="dropdown-filter-checkbox">
          <div class="d-flex gap-2">
            <se-input
              [ariaLabel]="fromInput?.ariaLabel"
              [currencyMode]="fromInput?.currencyMode || true"
              [currencySymbol]="fromInput?.currencySymbol || ''"
              [decimals]="fromInput?.decimals || 2"
              [formControlName]="'from'"
              [icon]="fromInput?.icon || ''"
              [id]="fromInput?.id || 'input-value-from-' + id"
              [inline]="fromInput?.inline || false"
              [label]="fromInput?.label || '' | translate"
              [labelAlign]="fromInput?.labelAlign || 'left'"
              [max]="fromInput?.max || 99999"
              [maxLength]="fromInput?.maxLength"
              [min]="fromInput?.min || 0"
              [placeholder]="fromInput?.placeholder || '' | translate"
              [readonly]="fromInput?.readonly || false"
              [showClear]="fromInput?.showClear || false"
              [showValidation]="false"
              [tooltip]="fromInput?.tooltip || false"
              [tooltipText]="fromInput?.tooltipText || ''"
              [type]="fromInput?.type || 'text'"
            >
            </se-input>
            <se-input
              [ariaLabel]="toInput?.ariaLabel"
              [currencyMode]="!!toInput?.currencyMode"
              [currencySymbol]="toInput?.currencySymbol || ''"
              [decimals]="toInput?.decimals || 2"
              [formControlName]="'to'"
              [icon]="toInput?.icon || ''"
              [id]="toInput?.id || 'input-value-to-' + id"
              [inline]="toInput?.inline || false"
              [label]="toInput?.label || '' | translate"
              [labelAlign]="toInput?.labelAlign || 'left'"
              [max]="fromInput?.max || 99999"
              [maxLength]="toInput?.maxLength"
              [min]="fromInput?.min || 0"
              [placeholder]="toInput?.placeholder || '' | translate"
              [readonly]="toInput?.readonly || false"
              [showClear]="toInput?.showClear || false"
              [showValidation]="false"
              [tooltip]="toInput?.tooltip || false"
              [tooltipText]="toInput?.tooltipText || ''"
              [type]="toInput?.type || 'text'"
            >
            </se-input>
          </div>
        </div>
      </ng-container>
    </div>
    <ng-container *ngIf="!!resetButton || !!applyButton">
      <div
        [ngClass]="{ 'action-buttons': !!resetButton && !!applyButton }"
        class="mb-2 buttons-container"
      >
        <se-button
          *ngIf="!!resetButton"
          (onClick)="resetForm()"
          [size]="'small'"
          [btnTheme]="'onlyText'"
          >{{ resetButton.label || "" | translate }}</se-button
        >
        <se-button
          *ngIf="!!applyButton"
          (onClick)="emitApply()"
          [disabled]="!rangeForm.valid"
          [size]="'small'"
          >{{ applyButton.label || "" | translate }}</se-button
        >
      </div>
    </ng-container>
  </div>
</div>
