import { Component } from "@angular/core";

@Component({
  selector: 'svg-empty-search',
  template: `<svg width="110" height="75" viewBox="0 0 110 75" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M105.213 13.3926C107.755 13.3926 109.816 15.4501 109.816 17.9882C109.816 20.5262 107.755 22.5838 105.213 22.5838H78.9096C81.4518 22.5838 83.5127 24.6413 83.5127 27.1793C83.5127 29.7174 81.4518 31.7749 78.9096 31.7749H93.3764C95.9186 31.7749 97.9794 33.8324 97.9794 36.3705C97.9794 38.9086 95.9186 40.9661 93.3764 40.9661H86.6862C83.4808 40.9661 80.8824 43.0236 80.8824 45.5617C80.8824 47.2537 82.1975 48.7856 84.8278 50.1573C87.37 50.1573 89.4309 52.2148 89.4309 54.7529C89.4309 57.2909 87.37 59.3485 84.8278 59.3485H30.2487C27.7065 59.3485 25.6456 57.2909 25.6456 54.7529C25.6456 52.2148 27.7065 50.1573 30.2487 50.1573H4.60306C2.06086 50.1573 0 48.0998 0 45.5617C0 43.0236 2.06086 40.9661 4.60306 40.9661H30.9063C33.4485 40.9661 35.5093 38.9086 35.5093 36.3705C35.5093 33.8324 33.4485 31.7749 30.9063 31.7749H14.4668C11.9246 31.7749 9.8637 29.7174 9.8637 27.1793C9.8637 24.6413 11.9246 22.5838 14.4668 22.5838H40.77C38.2278 22.5838 36.1669 20.5262 36.1669 17.9882C36.1669 15.4501 38.2278 13.3926 40.77 13.3926H105.213ZM105.213 31.7749C107.755 31.7749 109.816 33.8324 109.816 36.3705C109.816 38.9086 107.755 40.9661 105.213 40.9661C102.671 40.9661 100.61 38.9086 100.61 36.3705C100.61 33.8324 102.671 31.7749 105.213 31.7749Z" fill="#CBE3F4"/>
  <ellipse cx="57.5" cy="29" rx="19.5" ry="20" fill="#EBF6FF"/>
  <ellipse cx="44.9389" cy="54.2412" rx="14.0854" ry="14.0625" fill="#EBF6FF"/>
  <mask id="mask0_1459_134518" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="25" y="0" width="76" height="75">
  <rect x="25.0488" width="75.1219" height="75" fill="#D9D9D9"/>
  </mask>
  <g mask="url(#mask0_1459_134518)">
  <path d="M46.4392 55.6771L50.8736 60.1302C51.1518 60.408 51.4735 60.5382 51.8386 60.5209C52.2038 60.5035 52.5255 60.3559 52.8037 60.0781C53.082 59.8004 53.2211 59.4748 53.2211 59.1016C53.2211 58.7283 53.082 58.4028 52.8037 58.125L48.3173 53.698L52.9342 49.0885C53.2124 48.8108 53.3515 48.4896 53.3515 48.125C53.3515 47.7604 53.2124 47.4392 52.9342 47.1615C52.656 46.8837 52.3343 46.7448 51.9691 46.7448C51.6039 46.7448 51.2822 46.8837 51.004 47.1615L46.4392 51.7188L41.8746 47.1615C41.5963 46.8837 41.2833 46.7448 40.9355 46.7448C40.5877 46.7448 40.2747 46.8837 39.9965 47.1615C39.7183 47.4392 39.5792 47.7604 39.5792 48.125C39.5792 48.4896 39.7183 48.8108 39.9965 49.0885L44.5091 53.6459L39.9965 58.1511C39.7183 58.4289 39.5792 58.7587 39.5792 59.1406C39.5792 59.5226 39.7183 59.8524 39.9965 60.1302C40.2747 60.408 40.6051 60.5469 40.9877 60.5469C41.3703 60.5469 41.7007 60.408 41.9789 60.1302L46.4392 55.6771ZM46.4392 68.75C42.2832 68.75 38.7228 67.27 35.7578 64.3099C32.793 61.3498 31.3105 57.7951 31.3105 53.6459C31.3105 49.4965 32.793 45.9419 35.7578 42.9818C38.7228 40.0217 42.2832 38.5417 46.4392 38.5417C50.5953 38.5417 54.1558 40.0217 57.1206 42.9818C60.0855 45.9419 61.5679 49.4965 61.5679 53.6459C61.5679 57.7951 60.0855 61.3498 57.1206 64.3099C54.1558 67.27 50.5953 68.75 46.4392 68.75ZM69.7584 45.2604C69.2715 45.6424 68.7237 46.0157 68.1151 46.3802C67.5064 46.7448 66.9239 47.0486 66.3674 47.2917C65.9848 46.5799 65.5718 45.8551 65.1284 45.1173C64.685 44.3794 64.2111 43.698 63.7068 43.073C66.2457 41.9271 68.3672 40.165 70.0714 37.7865C71.7755 35.408 72.6276 32.6389 72.6276 29.4791C72.6276 25.3646 71.18 21.8533 68.2846 18.9453C65.3893 16.0373 61.881 14.5833 57.7597 14.5833C53.6037 14.5833 50.0693 16.0373 47.1566 18.9453C44.2438 21.8533 42.7875 25.3646 42.7875 29.4791C42.7875 30.1736 42.8527 30.8898 42.9831 31.6277C43.1135 32.3655 43.2744 33.0122 43.4657 33.5677C42.7006 33.6719 41.8528 33.8542 40.9225 34.1146C39.9922 34.375 39.1531 34.6789 38.4054 35.0261C38.1272 34.2101 37.9185 33.3247 37.7794 32.3698C37.6403 31.4149 37.5707 30.4514 37.5707 29.4791C37.5707 23.8715 39.5314 19.1189 43.4527 15.2213C47.3739 11.3238 52.143 9.375 57.7597 9.375C63.3417 9.375 68.0847 11.3325 71.9885 15.2474C75.8925 19.1623 77.8444 23.9062 77.8444 29.4791C77.8444 31.7187 77.4662 33.9019 76.7098 36.0287C75.9533 38.1554 74.9143 40.0347 73.5927 41.6666L92.0341 60C92.5384 60.5035 92.7949 61.1242 92.8036 61.862C92.8123 62.5998 92.5558 63.2292 92.0341 63.75C91.5298 64.2535 90.9038 64.5052 90.1561 64.5052C89.4084 64.5052 88.7823 64.2535 88.278 63.75L69.7584 45.2604Z" fill="#004E9B"/>
  </g>
  </svg>`

})
export class SvgEmptySearchComponent { }