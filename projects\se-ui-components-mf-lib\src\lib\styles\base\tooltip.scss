.p-tooltip {
  font-family: var(--font-primary);
  font-size: var(--text-sm);
  line-height: var(--line-sm);
  max-width: fit-content;

  &.p-tooltip-right .p-tooltip-arrow{
    border-right-color: var(--color-blue-500) !important;
    top: calc(50% - 1.5px);
    left: 1px;
  }

  &.p-tooltip-left .p-tooltip-arrow{
    border-left-color: var(--color-blue-500) !important;
    top: calc(50% - 1.5px);
    right: 1px;
  }

  &.p-tooltip-top .p-tooltip-arrow{
    border-top-color: var(--color-blue-500) !important;
    bottom: 1px;
    left: calc(50% - 1.5px);
  }

  &.p-tooltip-bottom .p-tooltip-arrow{
    border-bottom-color: var(--color-blue-500) !important;
    top: 1px;
    left: calc(50% - 1.5px);
  }

  .p-tooltip-text {
    background-color: var(--color-blue-500) !important;
    background-image: none !important;
    font-size: var(--text-sm);
    padding: 8px;
    max-width: 360px;
  }
}

.tooltip-icon {
  color: var(--color-blue-600);
  margin-left: 6px;
  top: 3px;
  position: relative;
}