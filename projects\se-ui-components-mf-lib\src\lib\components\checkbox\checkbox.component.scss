@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins/breakpoints';

.se-checkbox {
  width: fit-content;
  display: flex;
  align-items: center;
  gap: 5px;

  .checkbox-container {
    display: inline-flex;
    align-items: center;
    position: relative;
    padding-left: 24px;
    font-family: var(--font-primary);
    color: var(--color-black);

    &.focused {
      outline: var(--color-black) auto 1px;
    }

    &.disabled,
    &.disabled:hover {
      color: var(--color-gray-500);
      cursor: not-allowed;

      span {
        color: var(--color-gray-500);
        cursor: not-allowed;
      }

      .checkbox-input {
        cursor: not-allowed;
      }

      input {
        & ~ .checkmark {
          border-color: var(--color-gray-400);
          background-color: var(--color-gray-200);
          color: var(--color-gray-200);
          z-index: 2;
        }

        &:checked ~ .checkmark {
          border-color: var(--color-gray-400);
          background-color: var(--color-gray-400);
          color: var(--color-white);
        }
      }
    }

    .checkbox-input {
      position: absolute;
      opacity: 0;
      height: 16px;
      width: 16px;
      left: 0;
      top: 0;
      z-index: 1;
      cursor: pointer;
    }

    .checkbox-container-label {
      cursor: default;

      ::ng-deep a {
        color: var(--color-primary-action);
      }
    }

    .checkmark {
      position: absolute;
      top: 10px;
      left: 0;
      transform: translateY(-50%);
      height: 16px;
      width: 16px;
      border-radius: 4px;
      border: 1px solid var(--color-gray-400);
      background-color: white;
      color: var(--color-white);
      display: flex;
      align-items: center;
      justify-content: center;

      @at-root {
        .checkbox-container input:hover {
          &:not(:disabled):not(:checked) {
            & ~ .checkmark {
              border-color: var(--color-primary-action);
              background-color: var(--color-white);
            }
          }

          &:checked {
            & ~ .checkmark {
              border-color: var(--color-primary-action);
              background-color: var(--color-blue-600);
            }
          }
        }

        .checkbox-container input {
          &:checked {
            & ~ .checkmark {
              border-color: var(--color-primary-action);
              background-color: var(--color-primary-action);
            }
          }
        }
      }
    }

    &.invalid:not(.disabled) {
      .checkmark {
        border-color: var(--color-red-400);
      }
    }
  }

  .icon-button {
    all: initial;
    display: flex;
    align-items: center;
    cursor: pointer;
    border-radius: 8px;
    line-height: var(--line-sm);

    &:focus-visible {
      outline: var(--color-black) auto 1px;
    }

    .tooltip-icon {
      margin-left: 0px;
      position: static;
    }
  }

  &.label-ellipsis {
    width: auto;
    .checkbox-container {
      width: -webkit-fill-available;
      .checkbox-container-label {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
