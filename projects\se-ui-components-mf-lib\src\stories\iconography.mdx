import { Meta, Title, IconGallery, IconItem } from "@storybook/blocks";
import * as IconsOutline from "@ng-icons/material-icons/outline";
import * as IconsRound from "@ng-icons/material-icons/round";
import * as IconsSharp from "@ng-icons/material-icons/sharp";

<Meta title="Core/Iconography" />

# Iconography
## Angular Material

### Import your icon
> More info: [https://github.com/ng-icons/ng-icons](https://github.com/ng-icons/ng-icons)

Inside the `SeSharedModule`:
```js
import { NgIconsModule } from '@ng-icons/core';
import { featherAirplay } from '@ng-icons/feather-icons';
import { matCloseOutline } from '@ng-icons/material-icons/outline';

@NgModule({
  imports: [
    NgIconsModule.withIcons({ featherAirplay, matCloseOutline }),
  ],
})
export class SeSharedModule {}
```

You can import different icon styles:
```js
// Outline:
import { matCloseOutline } from '@ng-icons/material-icons/outline';

// Round:
import { matCloseRound} from '@ng-icons/material-icons/round';

// Sharp:
import { matCloseSharp } from '@ng-icons/material-icons/sharp';

// Baseline:
import { matCloseBaseline } from '@ng-icons/material-icons/baseline';
```

### How to use

Import `SeSharedModule` inside your component module and use:

```js
<ng-icon name="iconName"></ng-icon>
```

### Icon List

### Outline

<IconGallery>
  {Object.keys(IconsOutline)
    .map((name) => {
      const IconSVG = IconsOutline[name];
      return (
        <IconItem name={name}>
          <div class="text-2xl" dangerouslySetInnerHTML={{ __html: IconSVG }} />
        </IconItem>
      );
    })}
</IconGallery>


### Sharp

<IconGallery>
  {Object.keys(IconsSharp)
    .map((name) => {
      const IconSVG = IconsSharp[name];
      return (
        <IconItem name={name}>
          <div class="text-2xl" dangerouslySetInnerHTML={{ __html: IconSVG }} />
        </IconItem>
      );
    })}
</IconGallery>
