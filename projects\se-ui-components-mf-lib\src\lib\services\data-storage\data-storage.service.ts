import { Injectable } from '@angular/core';
import * as CryptoJS from 'crypto-js';

const SECRET_KEY = 'cG9ydGFsX3RyaWJ1dGFyaTIuMA==';

@Injectable({
  providedIn: 'root',
})
export class SeDataStorageService {
  constructor() {}

  private readonly encrypt = (key: string, data: any): void => {
    const encryptedMessage = CryptoJS.AES.encrypt(
      JSON.stringify(data),
      SECRET_KEY
    ).toString();
    sessionStorage.setItem(key, encryptedMessage);
  };

  private decrypt(key: string): any {
    let response: any = null;
    const encryptedData: string | null = sessionStorage.getItem(key);
    if (encryptedData) {
      const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
      response = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    }
    return response;
  };

  checkItem(key: string): boolean {
	  return !!this.decrypt(key);
  }

  getItem(key: string): any {
	  return this.decrypt(key);
  }

  setItem(key: string, data: any): void {
    this.encrypt(key, data);
  };

  deleteItem(key: string): void {
    this.setItem(key, null);
    sessionStorage.removeItem(key);
  };
}
