/**
 * WEBCOMPONENTS LIST
 */

export enum WebComponents {
  'wc-seguretat' = 'wc-seguretat',
  'wc-ciutada' = 'wc-ciutada',
  'wc-pagaments' = 'wc-pagaments',
  'wc-documents' = 'wc-documents',
  'wc-gestions' = 'wc-gestions',
  'wc-tributs' = 'wc-tributs',
}
export declare type WebComponentsT = keyof typeof WebComponents;

export interface iWcComponent {
  // Component path
  path: string;
  // Component
  component: any;
  // Component title
  title?: string;
  // Component stepper
  stepperId?: string;
  // Component step
  stepId?: string;
}

export class WcComponent implements iWcComponent {
  path: string;
  component: any;
  title?: string;
  stepperId?: string;
  stepId?: string;

  constructor(params?: Partial<iWcComponent>) {
    this.path = params?.path as string;
    this.component = params?.component;
    this.title = params?.title;
    this.stepperId = params?.stepperId;
    this.stepId = params?.stepId;
  }
}

/**
 * WEBCOMPONENT INPUT
 */

export interface iWcComponentInput {
  // Module to load inside the webcomponent
  module?: string;
  // Component to load inside the webcomponent's module
  component?: string;
  // Service method
  service?: string;
  data?: any;
}

export class WcComponentInput implements iWcComponentInput {
  module?: string;
  component?: string;
  service?: string;
  data?: any;

  constructor(params?: Partial<iWcComponentInput>) {
    this.module = params?.module;
    this.component = params?.component;
    this.service = params?.service;
    this.data = params?.data;
  }
}

/**
 * WEBCOMPONENT OUTPUT
 */

export interface iWcComponentEvent {
  // Webcomponent that originated the event
  webcomponentOrigin: WebComponentsT;
  // Component of the WC that originated the event
  componentOrigin: string;
  // Event data
  componentEvent: any;
}

export class WcComponentEvent implements iWcComponentEvent {
  webcomponentOrigin: WebComponentsT;
  componentOrigin: string;
  componentEvent: any;

  constructor(
    webcomponentOrigin: WebComponentsT,
    componentOrigin: string,
    componentEvent: any
  ) {
    this.webcomponentOrigin = webcomponentOrigin;
    this.componentOrigin = componentOrigin;
    this.componentEvent = componentEvent;
  }
}
