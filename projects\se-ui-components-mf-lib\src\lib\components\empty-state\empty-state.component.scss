.se-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--padding-base);
  padding: var(--padding-lg);
  height: inherit;

  &--primary{
    background: var(--color-blue-100);
  }

  &__container{
    display: grid;
    text-align: center;
    gap: var(--padding-xs);
  }

  &__label:not(.with-small-message) {
    font-style: italic;
  }

  &__small {
    font-style: italic;
  }
}