{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"se-ui-components-mf-lib": {"projectType": "library", "root": "projects/se-ui-components-mf-lib", "sourceRoot": "projects/se-ui-components-mf-lib/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/se-ui-components-mf-lib/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/se-ui-components-mf-lib/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/se-ui-components-mf-lib/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"tsConfig": "projects/se-ui-components-mf-lib/tsconfig.spec.json", "polyfills": ["zone.js", "zone.js/testing"]}}, "storybook": {"builder": "@storybook/angular:start-storybook", "options": {"configDir": "projects/se-ui-components-mf-lib/.storybook", "browserTarget": "se-ui-components-mf-lib:build", "compodoc": false, "port": 6006, "styles": ["node_modules/primeng/resources/themes/lara-light-blue/theme.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/bootstrap/scss/bootstrap.scss", "projects/se-ui-components-mf-lib/src/lib/styles.scss"]}}, "build-storybook": {"builder": "@storybook/angular:build-storybook", "options": {"configDir": "projects/se-ui-components-mf-lib/.storybook", "browserTarget": "se-ui-components-mf-lib:build", "compodoc": false, "outputDir": "storybook-static"}}}}, "storybook": {"projectType": "application", "root": "stories", "sourceRoot": "stories", "architect": {"build": {"options": {"tsConfig": "tsconfig.json", "styles": ["node_modules/primeng/resources/themes/lara-light-blue/theme.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/bootstrap/scss/bootstrap.scss", "projects/se-ui-components-mf-lib/src/lib/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}