import { SeHttpResponse } from "../services";

//getAppConfiguration - /api/gestio-configuracio/llistat - POST
export interface ListMfConfigurationResponse extends SeHttpResponse {
	content: ListMfConfiguration;
}

export interface ListMfConfiguration {
  results: MfConfiguration[];
  size: number;
  total: number;
}

export interface MfConfiguration {
  dadesContacte: boolean,
  nouCapcalera: boolean
  accessible: boolean
  isManteniment: boolean
  aplicacio: MF_AVAILABLE
  id: string
  mantenimentFinal: Date
  mantenimentInici: Date
  config?: any 
}

export interface MfConfigurationPersonal {
  id?: string;
  idPersCens?: string;
  mapConfig: { [x: string]: any } //Atributos configuracion por mf
  nif?: string;
  nom?: string;
}

export interface MfConfigurationPersonalResponse extends SeHttpResponse {
  content: MfConfigurationPersonal;
}

export interface MfConfigurationResponse extends SeHttpResponse {
  content: MfConfiguration;
}

export enum MF_AVAILABLE {
  EL_MEU_ESPAI_ATC = "EL_MEU_ESPAI_ATC",
  CITA_PREVIA = "CITA_PREVIA",
  ESTADES_TURISTIQUES = "ESTADES_TURISTIQUES",
  TRANSMISSIONS_PATRIMONIALS = "TRANSMISSIONS_PATRIMONIALS",
  SIGNATURA_INSPECCIO = "SIGNATURA_INSPECCIO",
  DEVOLUCIO_INGRESSOS = "DEVOLUCIO_INGRESSOS",
  PAGAMENT_LIQUIDACIONS = "PAGAMENT_LIQUIDACIONS",
  RECURS_REPOSICIO = "RECURS_REPOSICIO",
  RECURS = "RECURS",
  CSV = "CSV",
  ANTENES = "ANTENES",
  ACTIUS_NP = "ACTIUS_NP",
  GRANS_ESTABLIMENTS = "GRANS_ESTABLIMENTS",
  BEGUDES_ENSUCRADES = "BEGUDES_ENSUCRADES",
  HABITATGES_BUITS = "HABITATGES_BUITS",
  CASINOS = "CASINOS",
  BINGO = "BINGO",
  MAQUINES = "MAQUINES",
  APOSTES = "APOSTES",
  RIFES = "RIFES",
  HERENCIES = "HERENCIES",
  DONACIONS = "DONACIONS",
  ASSEGURANCES = "ASSEGURANCES",
  CONSOLIDACIO_DOMINI = "CONSOLIDACIO_DOMINI",
  GRAVAMEN = "GRAVAMEN",
  ALLEGACIONS = "ALLEGACIONS",
  CO2 = "CO2",
  PADRO = "PADRO",
  PAGAMENTS = "PAGAMENTS",
  DOCUMENTS = "DOCUMENTS",
  PRESENTACIONS = "PRESENTACIONS",
  SEGURETAT = "SEGURETAT",
  ITPAJ = "ITPAJ",
  DADES_CONTACTE = "DADES_CONTACTE",
  AVIACIO = "AVIACIO",
  GASOS = "GASOS",
}

export const CHANNEL_QUERY_PARAM_NAME = 'canal';

export enum ChannelEnum {
  APP = 'APP',
}
