import { Directive, ElementRef, Renderer2, Input, AfterViewInit, OnChanges, SimpleChanges } from '@angular/core';
import { FlattenedColumn } from './column.model';

@Directive({
  selector: '[columnResize]'
})
export class ResizeDirective implements OnChanges, AfterViewInit {
  @Input('columnResize') resizable = true;
  @Input() columns!: FlattenedColumn[];
  @Input() dragSpeed = 1;
  @Input() column!: FlattenedColumn;

  private minColumnWidth = 50;
  private maxColumnWidth = 1000;

  private startX!: number;
  private startWidth!: number;
  private startSiblingWidth!: number;
  private leftHandle!: HTMLElement | null;
  private rightHandle!: HTMLElement | null;

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes['resizable'] || changes['columns'] || changes['column']) {
      this.updateResizeHandles();
    }
  }

  ngAfterViewInit() {
    this.updateResizeHandles();
  }

  updateResizeHandles() {
    if (this.leftHandle) {
      this.renderer.removeChild(this.el.nativeElement, this.leftHandle);
      this.leftHandle = null;
    }
    if (this.rightHandle) {
      this.renderer.removeChild(this.el.nativeElement, this.rightHandle);
      this.rightHandle = null;
    }
  
    const currentIndex = this.columns.findIndex(col => col.id === this.column.id);
    const nextColumn = this.columns[currentIndex + 1];
    const prevColumn = this.columns[currentIndex - 1];
  
    if (prevColumn && (this.resizable && prevColumn && prevColumn.resizable)) {
      this.leftHandle = this.createHandle('left');
      this.renderer.listen(this.leftHandle, 'mousedown', this.onMouseDown.bind(this, 'left'));
    }
  
    if (nextColumn && (this.resizable && nextColumn && nextColumn.resizable)) {
      this.rightHandle = this.createHandle('right');
      this.renderer.listen(this.rightHandle, 'mousedown', this.onMouseDown.bind(this, 'right'));
    }
  }  

  createHandle(side: string): HTMLElement {
    const handle = this.renderer.createElement('span');
    this.renderer.addClass(handle, 'resize-handle');
    this.renderer.addClass(handle, `resize-handle-${side}`);
    this.renderer.appendChild(this.el.nativeElement, handle);
    return handle;
  }

  onMouseDown(side: string, event: MouseEvent): void {
    if (!this.resizable) return;

    // Prevent text selection on drag
    event.preventDefault();
    this.renderer.addClass(document.body, 'no-select');
  
    const sibling = (side === 'right') ? this.el.nativeElement.nextElementSibling : this.el.nativeElement.previousElementSibling;
  
    this.startX = event.pageX;
    this.startWidth = this.el.nativeElement.offsetWidth;
    this.startSiblingWidth = sibling ? sibling.offsetWidth : 0;
  
    const mouseMoveListener = this.onMouseMove.bind(this, side, sibling);
    document.addEventListener('mousemove', mouseMoveListener);

    document.addEventListener('mouseup', () => {
      this.renderer.removeClass(document.body, 'no-select');
      document.removeEventListener('mousemove', mouseMoveListener);
    });
  }

  onMouseMove(side: string, sibling: Element | null, event: MouseEvent): void {
    if (!this.resizable) return;

    const movementX = (event.pageX - this.startX) * this.dragSpeed;
    const direction = (side === 'right') ? 1 : -1;
  
    if (sibling) {
      const newSiblingWidth = this.startSiblingWidth - (direction * movementX);
      const newWidth = this.startWidth + (direction * movementX);
      
      const isSiblingWidthValid = newSiblingWidth > this.minColumnWidth && newSiblingWidth < this.maxColumnWidth;
      const isColumnWidthValid = newWidth > this.minColumnWidth && newWidth < this.maxColumnWidth;

      if (isSiblingWidthValid && isColumnWidthValid) {
        this.renderer.setStyle(sibling, 'width', `${newSiblingWidth}px`);
        this.renderer.setStyle(this.el.nativeElement, 'width', `${newWidth}px`);
      }
    }
  }
}
