import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';

import { ButtonComponent } from './button.component';
import { SeButtonModule } from './button.module';
import {
  SeButtonIconPositionEnum,
  SeButtonSizeEnum,
  SeButtonThemeEnum,
  SeButtonTooltipPositionEnum,
} from './models';

/* More on how to set up stories at:
https://storybook.js.org/docs/angular/writing-stories/introduction */
type ButtonComponentStoryType = ButtonComponent & { label: string };

const meta: Meta<ButtonComponentStoryType> = {
  title: 'Components/Button',
  component: ButtonComponent,
  decorators: [
    moduleMetadata({
      imports: [SeButtonModule],
    }),
  ],
  tags: ['autodocs'],
  args: {
    size: SeButtonSizeEnum.DEFAULT,
    btnTheme: SeButtonThemeEnum.PRIMARY,
    disabled: false,
    icon: '',
    title: '',
    iconPosition: SeButtonIconPositionEnum.LEFT,
    type: 'button',
    iconSize: '20px',
    label: 'Default button',
  },
  argTypes: {
    size: {
      description: 'Changes the button size.',
      options: Object.values(SeButtonSizeEnum),
      control: { type: 'select' },
      table: {
        defaultValue: { summary: SeButtonSizeEnum.DEFAULT },
      },
    },
    btnTheme: {
      description: 'Changes the button theme.',
      options: Object.values(SeButtonThemeEnum),
      control: { type: 'select' },
      table: {
        defaultValue: { summary: SeButtonThemeEnum.PRIMARY },
      },
    },
    tooltipText: {
      description: 'Set the tooltip text.',
      control: 'text',
    },
    tooltipPosition: {
      description: 'Set the tooltip position.',
      options: Object.values(SeButtonTooltipPositionEnum),
      control: { type: 'select' },
      table: {
        defaultValue: { summary: SeButtonTooltipPositionEnum.TOP },
      },
    },
    disabled: {
      description: 'Disable and stop the click event propagation',
      type: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
    icon: {
      description: 'Changes the button icon.',
      control: { type: 'text' },
    },
    title: {
      description: 'Changes the title.',
      control: { type: 'text' },
    },
    iconSize: {
      description: 'Set the icon size.',
      control: 'text',
      table: {
        defaultValue: { summary: '20px' },
      },
    },
    iconPosition: {
      description: 'Changes the button icon side.',
      options: Object.values(SeButtonIconPositionEnum),
      control: { type: 'select' },
      table: {
        defaultValue: { summary: SeButtonIconPositionEnum.LEFT },
      },
    },
    type: {
      description: 'Changes the button type.',
      control: { type: 'select' },
      options: ['button', 'submit', 'reset'],
      table: {
        defaultValue: { summary: 'button' },
      },
    },
    label: {
      description: 'Button content',
      control: 'text',
    },
  },
  render: (args) => ({
    props: {
      ...args,
      clickedButton: (): void => {
        console.log('Button clicked');
      },
    },
    template: `
      <se-button 
        [size]="size"
        [disabled]="disabled"
        [btnTheme]="btnTheme"
        [icon]="icon"
        [title]="title"
        [iconSize]="iconSize"
        [tooltipText]="tooltipText"
        [tooltipPosition]="tooltipPosition"
        [iconPosition]="iconPosition"
        [type]="type"
        (onClick)="clickedButton()"
      >
        {{ label }}
      </se-button>
    `,
  }),
};

export default meta;
type Story = StoryObj<ButtonComponentStoryType>;

/* More on writing stories with args:
https://storybook.js.org/docs/angular/writing-stories/args */
export const Primary: Story = {
  args: {
    size: SeButtonSizeEnum.DEFAULT,
    btnTheme: SeButtonThemeEnum.PRIMARY,
  },
};

export const Secondary: Story = {
  args: { btnTheme: SeButtonThemeEnum.SECONDARY },
};

export const OnlyText: Story = {
  args: { btnTheme: SeButtonThemeEnum.ONLY_TEXT },
};

export const WithIcon: Story = {
  args: {
    icon: 'matAddCircleOutlineOutline',
    // iconSize: '1em',
  },
};

export const OnlyIconAndTitle: Story = {
  args: {
    icon: 'matAddCircleOutlineOutline',
    // iconSize: '1em',
    label: undefined,
    title: 'Title Button',
  },
};

export const Danger: Story = { args: { btnTheme: SeButtonThemeEnum.DANGER } };

export const Large: Story = {
  args: { size: 'large' },
};

export const Small: Story = {
  args: { size: 'small' },
};

export const Tooltip: Story = {
  args: { tooltipText: 'Test', tooltipPosition: 'bottom' },
};
