import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { ErrorMessageComponent } from './form-control-error.component';
import { TranslateModule } from '@ngx-translate/core';
import { SeSharedModule } from '../../shared/shared.module';

@NgModule({
  imports: [CommonModule, TranslateModule.forChild(), ReactiveFormsModule, SeSharedModule],
  declarations: [ErrorMessageComponent],
  exports: [ErrorMessageComponent],
})
export class SeFormControlErrorModule {}
