import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgIcon } from '@ng-icons/core';
import { TranslateModule } from '@ngx-translate/core';
import { MultiSelectModule } from 'primeng/multiselect';
import { SeSharedModule } from '../../shared/shared.module';
import { SeBadgeModule } from '../badge/badge.module';
import { SeButtonModule } from '../button';
import { SeCheckboxModule } from '../checkbox/checkbox.module';
import { DropdownFilterComponent } from './dropdown-filter.component';
import { TimesIcon } from 'primeng/icons/times';

@NgModule({
  declarations: [DropdownFilterComponent],
  imports: [
    CommonModule,
    SeSharedModule,
    MultiSelectModule,
    ReactiveFormsModule,
    FormsModule,
    SeCheckboxModule,
    NgIcon,
    SeBadgeModule,
    SeButtonModule,
    TranslateModule.forChild(),
    TimesIcon
  ],
  exports: [DropdownFilterComponent],
})
export class SeDropdownFilterModule {}
