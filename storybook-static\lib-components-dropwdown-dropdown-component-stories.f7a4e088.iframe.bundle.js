"use strict";(self.webpackChunkse_ui_components_mf_lib=self.webpackChunkse_ui_components_mf_lib||[]).push([[4],{"./projects/se-ui-components-mf-lib/src/lib/components/dropwdown/dropdown.component.stories.ts":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{DropDown:()=>DropDown,default:()=>dropdown_component_stories});var _class,DropdownComponent_1,fesm2022_forms=__webpack_require__("./node_modules/@angular/forms/fesm2022/forms.mjs"),dist=__webpack_require__("./node_modules/@storybook/angular/dist/index.mjs"),tslib_es6=__webpack_require__("./node_modules/tslib/tslib.es6.mjs"),core=__webpack_require__("./node_modules/@angular/core/fesm2022/core.mjs");let DropdownComponent=(DropdownComponent_1=_class=class DropdownComponent{constructor(){this.disabled=!1,this.formControl=new fesm2022_forms.NI,this.onChange=()=>{},this.onTouched=()=>{}}onOptionSelected(event){this.onChange(event.value)}writeValue(value){this.formControl.setValue(value)}registerOnChange(fn){this.onChange=fn,this.formControl.valueChanges.subscribe(fn)}registerOnTouched(fn){this.onTouched=fn}setDisabledState(isDisabled){this.disabled=isDisabled}},_class.propDecorators={options:[{type:core.Input}],optionLabel:[{type:core.Input}],placeholder:[{type:core.Input}],disabled:[{type:core.Input}]},_class);DropdownComponent=DropdownComponent_1=(0,tslib_es6.gn)([(0,core.Component)({selector:"se-dropdown",template:'\n    <div>\n      <p-dropdown\n        [options]="options"\n        [optionLabel]="optionLabel"\n        [placeholder]="placeholder"\n        [disabled]="disabled"\n        [formControl]="formControl"\n        (onChange)="onOptionSelected($event)"\n      ></p-dropdown>\n    </div>\n  ',providers:[{provide:fesm2022_forms.JU,useExisting:(0,core.forwardRef)((()=>DropdownComponent_1)),multi:!0}]})],DropdownComponent);var primeng_dropdown=__webpack_require__("./node_modules/primeng/fesm2022/primeng-dropdown.mjs"),common=__webpack_require__("./node_modules/@angular/common/fesm2022/common.mjs");let SeDropdownModule=class SeDropdownModule{};SeDropdownModule=(0,tslib_es6.gn)([(0,core.NgModule)({imports:[common.CommonModule,fesm2022_forms.u5,fesm2022_forms.UX,primeng_dropdown.kW],declarations:[DropdownComponent],exports:[DropdownComponent]})],SeDropdownModule);var animations=__webpack_require__("./node_modules/@angular/platform-browser/fesm2022/animations.mjs");const dropdown_component_stories={title:"Components/Dropdown",component:DropdownComponent,decorators:[(0,dist.moduleMetadata)({imports:[SeDropdownModule,fesm2022_forms.UX]}),(0,dist.applicationConfig)({providers:[(0,animations.provideAnimations)()]})],args:{options:[{label:"New York",value:"NY"},{label:"Rome",value:"RM"},{label:"London",value:"LDN"},{label:"Istanbul",value:"IST"},{label:"Paris",value:"PRS"}],disabled:!1,optionLabel:"label",placeholder:"Select an option"},argTypes:{options:{control:{type:"object"}},disabled:{description:"Determines if the dropdown is disabled or not.",control:{type:"boolean"},table:{defaultValue:{summary:"false"}}},optionLabel:{description:"The property name to represent option label.",control:{type:"text"},table:{defaultValue:{summary:"label"}}},placeholder:{description:"The placeholder text for the dropdown when no option is selected.",control:{type:"text"},table:{defaultValue:{summary:"Select an option"}}}},tags:["autodocs"],render:args=>({template:'\n      <form [formGroup]="form">\n        <div style="min-height: 250px;">\n          <se-dropdown\n            [options]="options"\n            [disabled]="disabled"\n            [optionLabel]="optionLabel"\n            [placeholder]="placeholder"\n            [formControl]="form.controls[\'value\']">\n          </se-dropdown>\n        </div>\n      </form>\n    ',props:{...args,form:new fesm2022_forms.cw({value:new fesm2022_forms.NI({label:"New York",value:"NY"})})}})},DropDown={}}}]);