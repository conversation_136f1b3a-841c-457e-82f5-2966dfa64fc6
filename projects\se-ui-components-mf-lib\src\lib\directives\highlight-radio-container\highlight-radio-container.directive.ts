import { AfterViewInit, ChangeDetectorRef, Directive, ElementRef, HostBinding, HostListener } from '@angular/core';

const DEFAULT_BORDER = '1px solid var(--color-gray-400)';
const HIGHLIGHT_BORDER = '1px solid var(--color-blue-500)';
const HIGHLIGHT_BG = 'var(--color-blue-200)';
const TRANSPARENT_BG = 'transparent';

@Directive({
  selector: '[seHighlightRadioContainer]',
})
export class HighlightRadioContainerDirective implements AfterViewInit {
  @HostBinding('style.border') border = DEFAULT_BORDER;
  @HostBinding('style.padding') padding = '16px';
  @HostBinding('style.backgroundColor') backgroundColor = TRANSPARENT_BG;
  @HostBinding('style.cursor') cursor = 'pointer';
  @HostBinding('attr.tabindex') tabindex = '0';
  @HostBinding('attr.role') role = 'radio';
  @HostBinding('attr.aria-checked') get ariaChecked() {
    const checked = this.radioInputElement
      ? this.radioInputElement.checked
      : false;
    // Compruebo si esta seleccionado y el borde todavia no ha cambiado para cambiarlo
    if (checked && this.el.nativeElement.style.border === DEFAULT_BORDER) {
      this.el.nativeElement.style.border = HIGHLIGHT_BORDER;
      this.el.nativeElement.style.backgroundColor = HIGHLIGHT_BG;
    }
    return checked;
  }

  private radioInputElement!: HTMLInputElement;
  private radioGroupName!: string;

  constructor(private el: ElementRef, private cdr: ChangeDetectorRef) {}

  ngAfterViewInit(): void {
    this.fetchRadioElements();
    this.addGroupChangeListener();
    this.updateStyles();
    this.cdr.detectChanges();
  }

  @HostListener('click') onContainerClick() {
    if (this.radioInputElement && !this.radioInputElement.checked) {
      this.radioInputElement.click();
    }
  }

  @HostListener('keydown', ['$event']) handleKeyboardEvent(event: KeyboardEvent) {
    const activeElement = document.activeElement;
    if ((event.key === 'Enter' || event.key === ' ')  && activeElement === this.el.nativeElement) {
      this.onContainerClick();
      event.preventDefault();
    }
  }

  @HostListener('mouseover') onMouseOver() {
    this.setBorder(HIGHLIGHT_BORDER);
  }

  @HostListener('mouseout', ['$event']) onMouseOut() {
    this.updateStyles();
  }

  @HostListener('focus') onFocus() {
    this.setBorder(HIGHLIGHT_BORDER);
  }

  @HostListener('blur') onBlur() {
    this.updateStyles();
  }

  private fetchRadioElements() {
    const radioComponent = this.el.nativeElement.querySelector('se-radio');
    if (radioComponent) {
      this.radioInputElement = radioComponent.querySelector('input[type="radio"]');
      this.radioGroupName = this.radioInputElement.name;
      this.radioInputElement.addEventListener('change', () => this.updateStyles());
      this.makeFirstRadioUnfocusable();
    }
  }

  private makeFirstRadioUnfocusable() {
    const sameGroupRadios = this.el.nativeElement.querySelectorAll(`input[type="radio"][name="${this.radioGroupName}"]`);
    const sameGroupRadiosIcons = this.el.nativeElement.querySelectorAll(`input[type="radio"][name="${this.radioGroupName}"] + .radio-icon-container > ng-icon.radio-icon`);

    if (sameGroupRadios.length > 0 && sameGroupRadiosIcons[0] > 0) {
      (sameGroupRadios[0] as HTMLInputElement).tabIndex = -1;
      (sameGroupRadiosIcons[0] as HTMLInputElement).tabIndex = -1;
    }
  }

  private addGroupChangeListener() {
    const sameGroupRadios = document.querySelectorAll(`input[type="radio"][name="${this.radioGroupName}"]`);
    sameGroupRadios.forEach((radio) => {
      radio.addEventListener('change', () => this.updateStyles());
    });
  }

  private setBorder(value: string) {
    this.el.nativeElement.style.border = value;
  }

  private updateStyles() {
    const allRadioContainers = document.querySelectorAll(`[seHighlightRadioContainer]`);
    allRadioContainers.forEach((container) => {
      const radioInput = container.querySelector(`input[name="${this.radioGroupName}"]`) as HTMLInputElement | null;
      const containerElement = container as HTMLElement;
      if (radioInput) {
        if (radioInput.checked) {
          containerElement.style.border = HIGHLIGHT_BORDER;
          containerElement.style.backgroundColor = HIGHLIGHT_BG;
        } else {
          containerElement.style.border = DEFAULT_BORDER;
          containerElement.style.backgroundColor = TRANSPARENT_BG;
        }
      }
    });
  }
}
