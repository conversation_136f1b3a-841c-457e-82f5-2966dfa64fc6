version: 2.0.0
info:
  version: {project.version}
  description: PTR-UI-COMPONENTS-MF-LIB
global-env:
  - PUBLISH_PARAMS: './dist/se-ui-components-mf-lib'
components:
  - build:
      steps:
        - container:
            image:
              remote:
                name: registreimatges.sic.intranet.gencat.cat/gencat-sic-builders/node-builder:16.20.2
            resources:
              limits:
                cpu: 1000m
                memory: 5120Mi
              requests:
                cpu: 100m
                memory: 256Mi
          execution:
            commands:
              - npm install --cache /home/<USER>/agent/npmCache/4029
              - npm run build:lib
notifications:
  email:
    recipients: [ <EMAIL> ]
