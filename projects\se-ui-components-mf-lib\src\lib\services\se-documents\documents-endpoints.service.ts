import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  DownloadHttpResponse,
  SeHttpRequest,
  SeHttpResponse,
} from '../http/http-service.model';
import { SeHttpService } from '../http/http-service.service';
import {
  RequestDownloadFile,
  RequestDownloadNamedZipFiles,
  RequestJoinDocuments,
  ResponseDownloadCSV,
  ResponseDownloadFile,
  ResponseDownloadFiles,
  SendEmailDocumentRequest,
  SendEmailResponse,
} from './documents.model';

const BASE_URL_DOCUMENTS = '/api/documents';

@Injectable({
  providedIn: 'root',
})
export class SeDocumentsEndpointsService {

  constructor(private httpService: SeHttpService) {}

  endpointDownload = (
    request: RequestDownloadFile
  ): Observable<ResponseDownloadFile> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: BASE_URL_DOCUMENTS,
      url: `/v2/${request.id}`,
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  };

  endpointDownloadCSV = (csv: string): Observable<ResponseDownloadCSV> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: BASE_URL_DOCUMENTS,
      url: `/web/v1/csv/${csv}`,
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  };


  public getCsvStreaming = (csv: string, recaptcha?: string) : Observable<SeHttpResponse> => {
    let httpRequest: SeHttpRequest = {
      baseUrl: BASE_URL_DOCUMENTS,
      url: `/web/v2/csv/${csv}`,
      method: 'get',
      // Limpiamos los errores
      clearExceptions: true,
      // Binario
      responseType: 'blob',
      reportProgress: true,
      // Evitamos que intente parsear la respuesta como JSON
      parseResult: false
    };

    // Si no tenemos login, usamos la version publica con recaptcha
    if (recaptcha) {
      httpRequest = {
        ...httpRequest,
        url: `/public/web/v2/csv/${csv}`,
        // Añadimos el recaptcha en los headers
        headers: { recaptcha: recaptcha }
      };
    }

    return this.httpService.get(httpRequest);
  }

  getEndPointToDownloadDocumentsInZip(
    ids: string[]
  ): Observable<DownloadHttpResponse> {
    return this.httpService.get({
      method: 'post',
      baseUrl: BASE_URL_DOCUMENTS,
      url: `/v2/zip`,
      body: {
        idDocuments: ids,
      },
    });
  }

  getEndPointToDownloadNamedDocumentsInZip(
    request: RequestDownloadNamedZipFiles
  ): Observable<DownloadHttpResponse> {
    return this.httpService.get({
      method: 'post',
      baseUrl: BASE_URL_DOCUMENTS,
      url: `/v2/zip`,
      body: request
    });
  }

  downloadAllZipPadoct(request: string[]): Observable<SeHttpResponse<string>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: BASE_URL_DOCUMENTS,
      url: `/v2/padoct-zip`,
      method: 'post',
      body: request,
    };
    return this.httpService.post(httpRequest)
  }

  endpointJoinDocuments(request: string[]): Observable<ResponseDownloadFile> {
    const httpRequest: SeHttpRequest = {
      baseUrl: BASE_URL_DOCUMENTS,
      method: 'post',
      url: `/v2/ajuntar`,
      body: request,
    };
    return this.httpService.post<RequestJoinDocuments>(httpRequest);
  }

  endpointSendEmailDocument(
    body: SendEmailDocumentRequest
  ): Observable<SendEmailResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: BASE_URL_DOCUMENTS,
      url: `/v2/correu-electronic`,
      method: 'post',
      body,
    };

    return this.httpService.post(httpRequest);
  }

  endpointSendEmailDocumentZip(
    body: SendEmailDocumentRequest
  ): Observable<SendEmailResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: BASE_URL_DOCUMENTS,
      url: `/v2/correu-electronic-zip`,
      method: 'post',
      body,
    };

    return this.httpService.post(httpRequest);
  }

  endpointGetUploadedPadoctDocuments(
    entityId: string,
    idFunctionalModule: string,
    lstCodGTATSigedaType: string[] = [],
    getDocument = false
  ): Observable<ResponseDownloadFiles> {
    const httpRequest: SeHttpRequest = {
      baseUrl: BASE_URL_DOCUMENTS,
      url: `/v2/criteria`,
      method: 'post',
      body: {
        entityId,
        idFunctionalModule,
        lstCodGTATSigedaType,
        getDocument,
      },
    };

    return this.httpService.post(httpRequest);
  }
}
