import { Directive, ElementRef, Input, Renderer2, OnInit } from '@angular/core';

@Directive({
  selector: '[columnSize]',
})
export class ColumnSizeDirective implements OnInit {
  @Input('columnSize') size!: number | string | null;

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  ngOnInit() {
    if (this.size) {
      this.renderer.setStyle(this.el.nativeElement, 'width', this.getWidth());
    }
  }
  getWidth(): string {
    return typeof this.size === 'number' ? `${this.size}%` : this.size!;
  }
}
