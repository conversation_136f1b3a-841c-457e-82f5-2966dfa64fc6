import { Injectable, OnD<PERSON>roy } from '@angular/core';
import {
  Observable,
  Subject,
  catchError,
  map,
  of,
  take,
  takeUntil,
} from 'rxjs';
import { SeFile } from '../../components/upload-files/upload-files.model';
import {
  DownloadHttpResponse,
  DownloadResponse,
  SeHttpResponse,
} from '../http/http-service.model';
import { SeDocumentsEndpointsService } from './documents-endpoints.service';
import {
  MimeTypes,
  NamedDocument,
  RequestDownloadFile,
  RequestDownloadNamedZipFiles,
  ResponseDownloadFile,
  ResponseDownloadFiles,
  SendEmailResponse,
  iDocumentCSV,
  iDocumentPadoct,
} from './documents.model';
import { HttpResponse } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class SeDocumentsService implements OnDestroy {
  private unsubscribe: Subject<void> = new Subject();

  constructor(private endpointsService: SeDocumentsEndpointsService) {}

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  /**
   * Encode base64
   * @description Encode a file in base64
   */

  // Encode files
  base64(file: File): Promise<SeFile> {
    return new Promise((resolve) => {
      const reader: FileReader = new FileReader();
      // Get document extension
      const fileFormat = file.name.match(/\.[0-9a-z]+$/i) as unknown as string;
      const format: string = fileFormat['0'].split('.').pop() || '';
      const doc64: SeFile = {
        name: file.name,
        size: file.size,
        extension: format,
        base64: '',
        idPadoct: '',
      };
      if (file) {
        // Start reading the file
        reader.readAsArrayBuffer(file);
        // The reader executes the callbackFn when the 'readAsArrayBuffer' is completed
        reader.onload = () => {
          if (!reader.result) return;
          const fileUploaded: string | ArrayBuffer = reader.result;
          // Encode base64
          let docBase64: string;
          if (typeof fileUploaded === 'string') {
            docBase64 = btoa(fileUploaded.toString());
          } else {
            docBase64 = this._base64ArrayBuffer(fileUploaded);
          }
          doc64.base64 = docBase64;
          resolve(doc64);
        };
      }
    });
  }

  // Encode base64
  private _base64ArrayBuffer(arrayBuffer: ArrayBuffer): string {
    let base64 = '';
    const encodings =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';

    const bytes: Uint8Array = new Uint8Array(arrayBuffer);
    const byteLength: number = bytes.byteLength;
    const byteRemainder: number = byteLength % 3;
    const mainLength: number = byteLength - byteRemainder;

    let a;
    let b;
    let c;
    let d;
    let chunk;

    // Main loop deals with bytes in chunks of 3
    for (let i = 0; i < mainLength; i += 3) {
      // Combine the three bytes into a single integer
      chunk = (bytes[i] << 16) | (bytes[i + 1] << 8) | bytes[i + 2];

      // Use bitmasks to extract 6-bit segments from the triplet
      a = (chunk & 16515072) >> 18; // 16515072 = (2^6 - 1) << 18
      b = (chunk & 258048) >> 12; // 258048   = (2^6 - 1) << 12
      c = (chunk & 4032) >> 6; // 4032     = (2^6 - 1) << 6
      d = chunk & 63; // 63       = 2^6 - 1

      // Convert the raw binary segments to the appropriate ASCII encoding
      base64 += encodings[a] + encodings[b] + encodings[c] + encodings[d];
    }

    // Deal with the remaining bytes and padding
    if (byteRemainder === 1) {
      chunk = bytes[mainLength];
      a = (chunk & 252) >> 2; // 252 = (2^6 - 1) << 2
      // Set the 4 least significant bits to zero
      b = (chunk & 3) << 4; // 3   = 2^2 - 1

      base64 += `${encodings[a]}${encodings[b]}==`;
    } else if (byteRemainder === 2) {
      chunk = (bytes[mainLength] << 8) | bytes[mainLength + 1];
      a = (chunk & 64512) >> 10; // 64512 = (2^6 - 1) << 10
      b = (chunk & 1008) >> 4; // 1008  = (2^6 - 1) << 4
      // Set the 2 least significant bits to zero
      c = (chunk & 15) << 2; // 15    = 2^4 - 1

      base64 += `${encodings[a]}${encodings[b]}${encodings[c]}=`;
    }

    return base64;
  }

  openFile(content: string, documentType: string, customName?: string): void {
    const blob: Blob = this.convertBase64toBlob(content, documentType);
    this.dowloadBlob(blob, customName);
  }

  dowloadBlob(blob: Blob, customName?: string): void {
    const blobURL: string = URL.createObjectURL(blob);
    if (customName) {
      const fileLink = document.createElement('a');
      fileLink.href = blobURL;
      fileLink.download = customName;
      fileLink.click();
    } else {
      window.open(blobURL);
    }
    URL.revokeObjectURL(blobURL);
  }

  private createLinkAndDownload(
    format: string,
    base64File: string,
    nom?: string
  ) {
    const link = document.createElement('a');
    link.href = `data:${format};base64,${base64File}`;
    if (nom) {
      link.download = nom;
    }
    link.click();
    link.remove();
  }

  private convertBase64toBlob(content: string, contentType: string): Blob {
    contentType = contentType || '';
    const sliceSize = 512;
    // method which converts base64 to binary
    const byteCharacters = window.atob(content);
    const byteArrays: Uint8Array[] = [];
    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);
      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      const byteArray: Uint8Array = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }
    // Create blob
    const blob = new Blob(byteArrays, { type: contentType });
    return blob;
  }

  //// Download file
  downloadFile = (
    request: RequestDownloadFile,
    customName: string,
    onlyUseCustomName: boolean = false
  ): void => {
    this.downloadFile$(request).subscribe((response) => {
      if (response.content) {
        const data: iDocumentPadoct = response.content;
        const name = !onlyUseCustomName && data.nom ? data.nom : customName;
        this.createLinkAndDownload(data.format, data.base64File, name);
      }
    });
  };

  downloadAndOpenFile = (
    request: RequestDownloadFile,
    customName?: string
  ): void => {
    this.downloadFile$(request).subscribe((response) => {
      if (response.content) {
        const data: iDocumentPadoct = response.content;
        this.openFile(data.base64File, data.format, customName);
      }
    });
  };

  /**
   * Descarga de fichero binario
   * @param binaryString contenido del fichero en binario en formato string
   * @param filename
   * @param formatType predeterminado 'application/x-msdownload'
   */
  downloadBinariFile(
    binaryString: string,
    filename: string,
    formatType = 'application/x-msdownload'
  ): void {
    const bl = binaryString.length;
    const buffer = new ArrayBuffer(bl);
    const chars = new Uint8Array(buffer);
    for (let i = 0; i < bl; i++) {
      chars[i] = binaryString.charCodeAt(i);
    }

    const file = new File([buffer], filename, {
      type: formatType,
    });
    this.dowloadBlob(file, filename);
  }

  private downloadFile$ = (
    request: RequestDownloadFile
  ): Observable<ResponseDownloadFile> =>
    this.endpointsService
      .endpointDownload(request)
      .pipe(takeUntil(this.unsubscribe));

  //// Download file by CSV
  downloadCSV = (csv: string): Observable<boolean> => {
    return this.endpointsService.endpointDownloadCSV(csv).pipe(
      takeUntil(this.unsubscribe),
      map((response) => {
        if (response.content) {
          const data: iDocumentCSV = response.content;
          this.openFile(data.contingut, data.tipus, csv);
          return true;
        }
        return false;
      }),
      catchError(() => of(false))
    );
  };

  // Permite descargar un fichero a través de su CSV
  // Si se proporciona un recaptcha, se permite descargar el fichero de forma anonima
  // Si no se proporciona, se descarga el fichero de forma autenticada
  public getCsvStreaming = (csv: string, recaptcha?: string) => 
    this.endpointsService.getCsvStreaming(csv, recaptcha)
      .subscribe(blob => 
      {
        // Si es un binario, lo descarga
        if (blob && (blob instanceof Blob) && blob.type === 'application/octet-stream')
          this.dowloadBlob(blob, csv + '.pdf');
      }, error => { });

  // Download one or more files in a zip
  downloadDocumentsZIP(ids: string[], name: string): void {
    this.endpointsService
      .getEndPointToDownloadDocumentsInZip(ids)
      .pipe(take(1))
      .subscribe((response: DownloadHttpResponse) => {
        if (!response.content) return;
        const data: DownloadResponse = response.content;
        this.createLinkAndDownload(data.format, data.base64File, name);
      });
  }

  downloadNamedDocumentsZIP(documents: NamedDocument[], name: string): void {
    const documentMap: Record<string, string> = documents.reduce((map, { idPadoct, customName }) => {
      map[idPadoct] = customName;
      return map;
    }, {} as Record<string, string>);
    
    const documentsRequest: RequestDownloadNamedZipFiles = { documents: documentMap };
    this.endpointsService
      .getEndPointToDownloadNamedDocumentsInZip(documentsRequest)
      .pipe(take(1))
      .subscribe((response: DownloadHttpResponse) => {
        if (!response.content) return;
  
        const data: DownloadResponse = response.content;
        this.createLinkAndDownload(data.format, data.base64File, name);
      });
  }

  // Download one or more files in a zip
  downloadDocumentsPadocZIP(ids: string[], name: string): void {
    this.endpointsService
      .downloadAllZipPadoct(ids)
      .pipe(take(1))
      .subscribe((response: SeHttpResponse<string>) => {
        if (response?.content) {
          this.downloadBinariFile(
            response.content,
            name
          );
        }
      });
  }

  /**
   * Join the PDF's of the ids into another PDF file and gets download.
   */
  downloadJoinFiles(ids: string[], name: string): void {
    this.endpointsService
      .endpointJoinDocuments(ids)
      .pipe(take(1))
      .subscribe((response: ResponseDownloadFile) => {
        if (!response.content) return;
        const data: iDocumentPadoct = response.content;
        this.createLinkAndDownload(data.format, data.base64File, name);
      });
  }

  // if it's only 1 file downloads 1 file, if it's array downloads 1 zip
  downloadDocument(documentList: string[] | null, customName: string): void {
    if (!documentList || documentList.length == 0) {
      throw new Error(
        'La propiedad "idDocument" es requerida para descargar documentos en el proyecto "se-ui-components-mf"'
      );
    } else if (documentList.length > 1) {
      this.downloadDocumentsZIP(documentList, customName);
    } else {
      const request: RequestDownloadFile = { id: documentList[0] };
      this.downloadFile(request, customName);
    }
  }

  getFileExtension(mimeType: string): string | undefined {
    return MimeTypes[mimeType as keyof typeof MimeTypes];
  }

  getMimeType(fileExtension: string): string | undefined {
    const mimeTypesEntries = Object.entries(MimeTypes);

    const foundEntry = mimeTypesEntries.find(
      ([, extension]) => extension === fileExtension
    );

    return foundEntry ? foundEntry[0] : undefined;
  }

  convertSize(sizeInKb: number): string {
    const sizeInMb = sizeInKb / 1024;
    if (sizeInMb > 1) {
      // Convert to Mbps and round to 2 decimal places.

      return `${sizeInMb.toFixed(2)} MB`;
    } else {
      return `${Number(sizeInKb).toFixed(2)} KB`;
    }
  }

  // Send documents via email using a list of ids
  sendEmailDocumentIds = (
    email: string,
    listDocumentsIds: string[]
  ): Observable<SendEmailResponse> => {
    return this.endpointsService.endpointSendEmailDocument({
      email,
      listDocumentsIds,
    });
  };

  // Send document via email using a list of csv
  sendEmailDocumentCsv = (
    email: string,
    listDocumentsCsv: string[]
  ): Observable<SendEmailResponse> => {
    return this.endpointsService.endpointSendEmailDocument({
      email,
      listDocumentsCsv,
    });
  };

  sendEmailDocumentIdsZip = (
    email: string,
    listDocumentsIds: string[]
  ): Observable<SendEmailResponse> => {
    return this.endpointsService.endpointSendEmailDocumentZip({
      email,
      listDocumentsIds,
    });
  };

  sendEmailDocumentCsvZip = (
    email: string,
    listDocumentsCsv: string[]
  ): Observable<SendEmailResponse> => {
    return this.endpointsService.endpointSendEmailDocumentZip({
      email,
      listDocumentsCsv,
    });
  };

  /**
   * Get uploaded documents
   * @param entityId
   * @param idFunctionalModule
   * @param lstCodGTATSigedaType
   * @param getDocument
   * @returns
   */
  getUploadedPadoctDocuments(
    entityId: string,
    idFunctionalModule: string,
    lstCodGTATSigedaType: string[] = [],
    getDocument = false
  ): Observable<ResponseDownloadFiles> {
    return this.endpointsService.endpointGetUploadedPadoctDocuments(
      entityId,
      idFunctionalModule,
      lstCodGTATSigedaType,
      getDocument
    );
  }

  setCommaFileFormat(value: string[]): string {
    const uppercased = value.map(v => v.toUpperCase());
    if (uppercased.length === 1) {
      return  uppercased[0];
    } 
    
    if (uppercased.length === 2) {
      return `${uppercased[0]} o ${uppercased[1]}`;
    } 

    const allButLast = uppercased.slice(0, -1).join(', ');
    const last = uppercased[uppercased.length - 1];
    return `${allButLast} o ${last}`;
  }

  setSlashFileFormat(value: string[]): string {
    if (value.length === 0) {
      return '';
    }

    if (value.length === 1) {
      return value[0].toUpperCase();
    }
    
    return value.join(' / ').toUpperCase();
  }
}
